{"version": 3, "file": "subscriptions.cjs", "sources": ["index.js"], "sourcesContent": ["// This file is adapted from the graphql-ws npm package:\n// https://github.com/enisdenjo/graphql-ws\n//\n// Most of the file comes from that package's README; some other parts (such as\n// isLikeCloseEvent) come from its source.\n//\n// Here's the license of the original code:\n//\n// The MIT License (MIT)\n//\n// Copyright (c) 2020-2021 <PERSON>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\nimport { __assign, __extends } from \"tslib\";\nimport { print } from \"../../utilities/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { isNonNullObject, Observable } from \"../../utilities/index.js\";\nimport { ApolloError } from \"../../errors/index.js\";\n// https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/close_event\nfunction isLikeCloseEvent(val) {\n    return isNonNullObject(val) && \"code\" in val && \"reason\" in val;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/error_event\nfunction isLikeErrorEvent(err) {\n    var _a;\n    return isNonNullObject(err) && ((_a = err.target) === null || _a === void 0 ? void 0 : _a.readyState) === WebSocket.CLOSED;\n}\nvar GraphQLWsLink = /** @class */ (function (_super) {\n    __extends(GraphQLWsLink, _super);\n    function GraphQLWsLink(client) {\n        var _this = _super.call(this) || this;\n        _this.client = client;\n        return _this;\n    }\n    GraphQLWsLink.prototype.request = function (operation) {\n        var _this = this;\n        return new Observable(function (observer) {\n            return _this.client.subscribe(__assign(__assign({}, operation), { query: print(operation.query) }), {\n                next: observer.next.bind(observer),\n                complete: observer.complete.bind(observer),\n                error: function (err) {\n                    if (err instanceof Error) {\n                        return observer.error(err);\n                    }\n                    var likeClose = isLikeCloseEvent(err);\n                    if (likeClose || isLikeErrorEvent(err)) {\n                        return observer.error(\n                        // reason will be available on clean closes\n                        new Error(\"Socket closed\".concat(likeClose ? \" with event \".concat(err.code) : \"\").concat(likeClose ? \" \".concat(err.reason) : \"\")));\n                    }\n                    return observer.error(new ApolloError({\n                        graphQLErrors: Array.isArray(err) ? err : [err],\n                    }));\n                },\n                // casting around a wrong type in graphql-ws, which incorrectly expects `Sink<ExecutionResult>`\n            });\n        });\n    };\n    return GraphQLWsLink;\n}(ApolloLink));\nexport { GraphQLWsLink };\n//# sourceMappingURL=index.js.map"], "names": ["isNonNullObject", "__extends", "Observable", "__assign", "print", "ApolloError", "ApolloLink"], "mappings": ";;;;;;;;;AAmCA,SAAS,gBAAgB,CAAC,GAAG,EAAE;AAC/B,IAAI,OAAOA,yBAAe,CAAC,GAAG,CAAC,IAAI,MAAM,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,CAAC;AACpE,CAAC;AAED,SAAS,gBAAgB,CAAC,GAAG,EAAE;AAC/B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAOA,yBAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,MAAM,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,MAAM,SAAS,CAAC,MAAM,CAAC;AAC/H,CAAC;AACE,IAAC,aAAa,KAAkB,UAAU,MAAM,EAAE;AACrD,IAAIC,eAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AACrC,IAAI,SAAS,aAAa,CAAC,MAAM,EAAE;AACnC,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;AAC9C,QAAQ,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AAC9B,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,aAAa,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,SAAS,EAAE;AAC3D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,OAAO,IAAIC,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAClD,YAAY,OAAO,KAAK,CAAC,MAAM,CAAC,SAAS,CAACC,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,KAAK,EAAEC,eAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;AAChH,gBAAgB,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AAClD,gBAAgB,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC1D,gBAAgB,KAAK,EAAE,UAAU,GAAG,EAAE;AACtC,oBAAoB,IAAI,GAAG,YAAY,KAAK,EAAE;AAC9C,wBAAwB,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACnD,qBAAqB;AACrB,oBAAoB,IAAI,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAC1D,oBAAoB,IAAI,SAAS,IAAI,gBAAgB,CAAC,GAAG,CAAC,EAAE;AAC5D,wBAAwB,OAAO,QAAQ,CAAC,KAAK;AAE7C,wBAAwB,IAAI,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7J,qBAAqB;AACrB,oBAAoB,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAIC,kBAAW,CAAC;AAC1D,wBAAwB,aAAa,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AACvE,qBAAqB,CAAC,CAAC,CAAC;AACxB,iBAAiB;AAEjB,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,OAAO,aAAa,CAAC;AACzB,CAAC,CAACC,eAAU,CAAC;;;;"}