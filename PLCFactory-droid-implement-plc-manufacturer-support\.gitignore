# Continuum Platform .gitignore
# Comprehensive .gitignore for a multi-language cloud-native platform

### General ###
.env
.env.*
!.env.sample
.env.local
.env.development.local
.env.test.local
.env.production.local
*.log
logs/
tmp/
temp/
.tmp
.temp
.cache
.history
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

### Node.js / JavaScript / TypeScript ###
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-store/
.npm
.yarn
.pnp.*
dist/
build/
coverage/
.coverage
*.tsbuildinfo
.eslintcache
.next/
out/

### Elixir / Phoenix ###
/_build/
/deps/
/doc/
/.fetch
erl_crash.dump
*.ez
*.beam
/config/*.secret.exs
.elixir_ls/

### Rust ###
/target/
Cargo.lock
**/*.rs.bk

### Go ###
/bin/
/pkg/
/vendor/
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

### Python ###
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.venv
venv/
ENV/
env.bak/
venv.bak/

### IDE / Editors ###
# VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# JetBrains IDEs
.idea/
*.iml
*.iws
*.ipr
*.iws
out/
.idea_modules/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]
Session.vim
Sessionx.vim
.netrwhist
*~
tags

# Emacs
*~
\#*#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
.\#*

### Docker ###
.dockerignore
docker-volumes/

### Database ###
*.sqlite
*.sqlite3
*.db
dump.rdb
mongodb/
postgres-data/
timescale-data/
redis-data/
minio-data/

### Continuum specific ###
# Generated transpiler output
/services/transpiler/output/
# Ghost AI models
/services/ghost/models/
# Certificates and keys
*.pem
*.key
*.crt
*.cer
*.der
*.p12
*.pfx
# Build artifacts
/artifacts/
/releases/
# Helm charts
/charts/*/charts/
/charts/*/Chart.lock

# Local development overrides
docker-compose.override.yml
