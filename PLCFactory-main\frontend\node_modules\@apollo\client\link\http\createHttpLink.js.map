{"version": 3, "file": "createHttpLink.js", "sourceRoot": "", "sources": ["../../../src/link/http/createHttpLink.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAI7D,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACrE,OAAO,EAAE,uBAAuB,EAAE,MAAM,8BAA8B,CAAC;AACvE,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EACL,WAAW,EACX,iBAAiB,EACjB,yBAAyB,GAC1B,MAAM,gCAAgC,CAAC;AACxC,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAEjD,OAAO,EACL,gCAAgC,EAChC,cAAc,EACd,kBAAkB,GACnB,MAAM,+BAA+B,CAAC;AACvC,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,SAAS,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AACxE,OAAO,EACL,KAAK,EACL,iBAAiB,EACjB,4BAA4B,GAC7B,MAAM,0BAA0B,CAAC;AAElC,IAAM,WAAW,GAAG,KAAK,CAAC,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC,CAAC;AAEvC,MAAM,CAAC,IAAM,cAAc,GAAG,UAAC,WAA6B;IAA7B,4BAAA,EAAA,gBAA6B;IAExD,IAAA,KASE,WAAW,IATG,EAAhB,GAAG,mBAAG,UAAU,KAAA;IAChB,gDAAgD;IACzC,cAAc,GAOnB,WAAW,MAPQ,EACrB,KAME,WAAW,MANS,EAAtB,KAAK,mBAAG,cAAc,KAAA,EACtB,iBAAiB,GAKf,WAAW,kBALI,EACjB,kBAAkB,GAIhB,WAAW,mBAJK,EAClB,gBAAgB,GAGd,WAAW,iBAHG,EAChB,KAEE,WAAW,uBAFiB,EAA9B,sBAAsB,mBAAG,KAAK,KAAA,EAC3B,cAAc,UACf,WAAW,EAVX,kHAUH,CADkB,CACH;IAEhB,IAAI,OAAO,EAAE,CAAC;QACZ,4EAA4E;QAC5E,8CAA8C;QAC9C,YAAY,CAAC,cAAc,IAAI,WAAW,CAAC,CAAC;IAC9C,CAAC;IAED,IAAM,UAAU,GAAG;QACjB,IAAI,EAAE,EAAE,iBAAiB,mBAAA,EAAE,kBAAkB,oBAAA,EAAE;QAC/C,OAAO,EAAE,cAAc,CAAC,YAAY;QACpC,WAAW,EAAE,cAAc,CAAC,WAAW;QACvC,OAAO,EAAE,cAAc,CAAC,OAAO;KAChC,CAAC;IAEF,OAAO,IAAI,UAAU,CAAC,UAAC,SAAS;QAC9B,IAAI,SAAS,GAAG,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAE1C,IAAM,OAAO,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;QAEvC,8DAA8D;QAC9D,sEAAsE;QACtE,6DAA6D;QAC7D,yEAAyE;QACzE,oDAAoD;QACpD,6DAA6D;QAC7D,IAAM,sBAAsB,GAGxB,EAAE,CAAC;QAEP,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YACtB,IAAA,KAAoB,OAAO,CAAC,eAAe,EAAzC,MAAI,UAAA,EAAE,OAAO,aAA4B,CAAC;YAClD,IAAI,MAAI,EAAE,CAAC;gBACT,sBAAsB,CAAC,2BAA2B,CAAC,GAAG,MAAI,CAAC;YAC7D,CAAC;YACD,IAAI,OAAO,EAAE,CAAC;gBACZ,sBAAsB,CAAC,8BAA8B,CAAC,GAAG,OAAO,CAAC;YACnE,CAAC;QACH,CAAC;QAED,IAAM,cAAc,yBAAQ,sBAAsB,GAAK,OAAO,CAAC,OAAO,CAAE,CAAC;QAEzE,IAAM,aAAa,GAAG;YACpB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,OAAO,EAAE,OAAO,CAAC,YAAY;YAC7B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,OAAO,EAAE,cAAc;SACxB,CAAC;QAEF,IAAI,aAAa,CAAC,CAAC,QAAQ,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/C,IAAM,gBAAgB,GAAG,4BAA4B,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAEvE,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,OAAO,SAAS,CACd,IAAI,KAAK,CACP,uMAAuM,CACxM,CACF,CAAC;YACJ,CAAC;YAED,SAAS,CAAC,KAAK,GAAG,gBAAgB,CAAC;QACrC,CAAC;QAED,wDAAwD;QAClD,IAAA,KAAoB,gCAAgC,CACxD,SAAS,EACT,KAAK,EACL,kBAAkB,EAClB,UAAU,EACV,aAAa,CACd,EANO,OAAO,aAAA,EAAE,IAAI,UAMpB,CAAC;QAEF,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9C,IAAI,CAAC,SAAS,GAAG,wBAAwB,CACvC,IAAI,CAAC,SAAS,EACd,SAAS,CAAC,KAAK,CAChB,CAAC;QACJ,CAAC;QAED,IAAI,UAAuC,CAAC;QAC5C,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;YAC9D,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;YACnC,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;QACrC,CAAC;QAED,6DAA6D;QAC7D,IAAM,oBAAoB,GAAG,UAAC,CAAiB;YAC7C,OAAO,CAAC,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,SAAS,KAAK,UAAU,CAAC;QACxE,CAAC,CAAC;QACF,IAAM,wBAAwB,GAAG,UAAC,CAAiB;YACjD,OAAO,CAAC,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,SAAS,KAAK,cAAc,CAAC;QAC5E,CAAC,CAAC;QACF,IAAM,cAAc,GAAG,wBAAwB,CAC7C,iBAAiB,CAAC,SAAS,CAAC,KAAK,CAAC,CACnC,CAAC;QACF,yDAAyD;QACzD,IAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;QAC3D,IACE,gBAAgB;YAChB,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,EACvD,CAAC;YACD,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;QACzB,CAAC;QAED,IAAI,QAAQ,IAAI,cAAc,EAAE,CAAC;YAC/B,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;YACxC,IAAI,YAAY,GAAG,kBAAkB,CAAC;YACtC,wEAAwE;YACxE,2CAA2C;YAC3C,IAAI,cAAc,IAAI,QAAQ,EAAE,CAAC;gBAC/B,SAAS,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;YAClE,CAAC;YAED,IAAI,cAAc,EAAE,CAAC;gBACnB,YAAY;oBACV,wDAAwD,CAAC;YAC7D,CAAC;iBAAM,IAAI,QAAQ,EAAE,CAAC;gBACpB,YAAY,IAAI,qCAAqC,CAAC;YACxD,CAAC;YACD,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,YAAY,CAAC;QACxC,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;YACvB,IAAA,KAAyB,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,EAAxD,MAAM,YAAA,EAAE,UAAU,gBAAsC,CAAC;YACjE,IAAI,UAAU,EAAE,CAAC;gBACf,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC;YAC/B,CAAC;YACD,SAAS,GAAG,MAAM,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC;gBACF,OAAe,CAAC,IAAI,GAAG,uBAAuB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACnE,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,OAAO,IAAI,UAAU,CAAC,UAAC,QAAQ;YAC7B,uEAAuE;YACvE,qEAAqE;YACrE,wEAAwE;YACxE,yEAAyE;YACzE,iEAAiE;YACjE,IAAM,YAAY,GAAG,cAAc,IAAI,KAAK,CAAC,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC,IAAI,WAAW,CAAC;YAEzE,IAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClD,YAAa,CAAC,SAAS,EAAE,OAAO,CAAC;iBAC9B,IAAI,CAAC,UAAC,QAAQ;;gBACb,SAAS,CAAC,UAAU,CAAC,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;gBACnC,IAAM,KAAK,GAAG,MAAA,QAAQ,CAAC,OAAO,0CAAE,GAAG,CAAC,cAAc,CAAC,CAAC;gBAEpD,IAAI,KAAK,KAAK,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACvD,OAAO,iBAAiB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;gBACnD,CAAC;qBAAM,CAAC;oBACN,OAAO,yBAAyB,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CACxD,YAAY,CACb,CAAC;gBACJ,CAAC;YACH,CAAC,CAAC;iBACD,IAAI,CAAC;gBACJ,UAAU,GAAG,SAAS,CAAC;gBACvB,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,CAAC,CAAC;iBACD,KAAK,CAAC,UAAC,GAAG;gBACT,UAAU,GAAG,SAAS,CAAC;gBACvB,WAAW,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;YAEL,OAAO;gBACL,qCAAqC;gBACrC,oEAAoE;gBACpE,IAAI,UAAU;oBAAE,UAAU,CAAC,KAAK,EAAE,CAAC;YACrC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\n\nimport type { DefinitionNode } from \"graphql\";\n\nimport { ApolloLink } from \"../core/index.js\";\nimport { Observable, hasDirectives } from \"../../utilities/index.js\";\nimport { serializeFetchParameter } from \"./serializeFetchParameter.js\";\nimport { selectURI } from \"./selectURI.js\";\nimport {\n  handleError,\n  readMultipartBody,\n  parseAndCheckHttpResponse,\n} from \"./parseAndCheckHttpResponse.js\";\nimport { checkFetcher } from \"./checkFetcher.js\";\nimport type { HttpOptions } from \"./selectHttpOptionsAndBody.js\";\nimport {\n  selectHttpOptionsAndBodyInternal,\n  defaultPrinter,\n  fallbackHttpConfig,\n} from \"./selectHttpOptionsAndBody.js\";\nimport { rewriteURIForGET } from \"./rewriteURIForGET.js\";\nimport { fromError, filterOperationVariables } from \"../utils/index.js\";\nimport {\n  maybe,\n  getMainDefinition,\n  removeClientSetsFromDocument,\n} from \"../../utilities/index.js\";\n\nconst backupFetch = maybe(() => fetch);\n\nexport const createHttpLink = (linkOptions: HttpOptions = {}) => {\n  let {\n    uri = \"/graphql\",\n    // use default global fetch if nothing passed in\n    fetch: preferredFetch,\n    print = defaultPrinter,\n    includeExtensions,\n    preserveHeaderCase,\n    useGETForQueries,\n    includeUnusedVariables = false,\n    ...requestOptions\n  } = linkOptions;\n\n  if (__DEV__) {\n    // Make sure at least one of preferredFetch, window.fetch, or backupFetch is\n    // defined, so requests won't fail at runtime.\n    checkFetcher(preferredFetch || backupFetch);\n  }\n\n  const linkConfig = {\n    http: { includeExtensions, preserveHeaderCase },\n    options: requestOptions.fetchOptions,\n    credentials: requestOptions.credentials,\n    headers: requestOptions.headers,\n  };\n\n  return new ApolloLink((operation) => {\n    let chosenURI = selectURI(operation, uri);\n\n    const context = operation.getContext();\n\n    // `apollographql-client-*` headers are automatically set if a\n    // `clientAwareness` object is found in the context. These headers are\n    // set first, followed by the rest of the headers pulled from\n    // `context.headers`. If desired, `apollographql-client-*` headers set by\n    // the `clientAwareness` object can be overridden by\n    // `apollographql-client-*` headers set in `context.headers`.\n    const clientAwarenessHeaders: {\n      \"apollographql-client-name\"?: string;\n      \"apollographql-client-version\"?: string;\n    } = {};\n\n    if (context.clientAwareness) {\n      const { name, version } = context.clientAwareness;\n      if (name) {\n        clientAwarenessHeaders[\"apollographql-client-name\"] = name;\n      }\n      if (version) {\n        clientAwarenessHeaders[\"apollographql-client-version\"] = version;\n      }\n    }\n\n    const contextHeaders = { ...clientAwarenessHeaders, ...context.headers };\n\n    const contextConfig = {\n      http: context.http,\n      options: context.fetchOptions,\n      credentials: context.credentials,\n      headers: contextHeaders,\n    };\n\n    if (hasDirectives([\"client\"], operation.query)) {\n      const transformedQuery = removeClientSetsFromDocument(operation.query);\n\n      if (!transformedQuery) {\n        return fromError(\n          new Error(\n            \"HttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or set the `transformOptions.removeClientFields` option to `true`.\"\n          )\n        );\n      }\n\n      operation.query = transformedQuery;\n    }\n\n    //uses fallback, link, and then context to build options\n    const { options, body } = selectHttpOptionsAndBodyInternal(\n      operation,\n      print,\n      fallbackHttpConfig,\n      linkConfig,\n      contextConfig\n    );\n\n    if (body.variables && !includeUnusedVariables) {\n      body.variables = filterOperationVariables(\n        body.variables,\n        operation.query\n      );\n    }\n\n    let controller: AbortController | undefined;\n    if (!options.signal && typeof AbortController !== \"undefined\") {\n      controller = new AbortController();\n      options.signal = controller.signal;\n    }\n\n    // If requested, set method to GET if there are no mutations.\n    const definitionIsMutation = (d: DefinitionNode) => {\n      return d.kind === \"OperationDefinition\" && d.operation === \"mutation\";\n    };\n    const definitionIsSubscription = (d: DefinitionNode) => {\n      return d.kind === \"OperationDefinition\" && d.operation === \"subscription\";\n    };\n    const isSubscription = definitionIsSubscription(\n      getMainDefinition(operation.query)\n    );\n    // does not match custom directives beginning with @defer\n    const hasDefer = hasDirectives([\"defer\"], operation.query);\n    if (\n      useGETForQueries &&\n      !operation.query.definitions.some(definitionIsMutation)\n    ) {\n      options.method = \"GET\";\n    }\n\n    if (hasDefer || isSubscription) {\n      options.headers = options.headers || {};\n      let acceptHeader = \"multipart/mixed;\";\n      // Omit defer-specific headers if the user attempts to defer a selection\n      // set on a subscription and log a warning.\n      if (isSubscription && hasDefer) {\n        invariant.warn(\"Multipart-subscriptions do not support @defer\");\n      }\n\n      if (isSubscription) {\n        acceptHeader +=\n          \"boundary=graphql;subscriptionSpec=1.0,application/json\";\n      } else if (hasDefer) {\n        acceptHeader += \"deferSpec=20220824,application/json\";\n      }\n      options.headers.accept = acceptHeader;\n    }\n\n    if (options.method === \"GET\") {\n      const { newURI, parseError } = rewriteURIForGET(chosenURI, body);\n      if (parseError) {\n        return fromError(parseError);\n      }\n      chosenURI = newURI;\n    } else {\n      try {\n        (options as any).body = serializeFetchParameter(body, \"Payload\");\n      } catch (parseError) {\n        return fromError(parseError);\n      }\n    }\n\n    return new Observable((observer) => {\n      // Prefer linkOptions.fetch (preferredFetch) if provided, and otherwise\n      // fall back to the *current* global window.fetch function (see issue\n      // #7832), or (if all else fails) the backupFetch function we saved when\n      // this module was first evaluated. This last option protects against the\n      // removal of window.fetch, which is unlikely but not impossible.\n      const currentFetch = preferredFetch || maybe(() => fetch) || backupFetch;\n\n      const observerNext = observer.next.bind(observer);\n      currentFetch!(chosenURI, options)\n        .then((response) => {\n          operation.setContext({ response });\n          const ctype = response.headers?.get(\"content-type\");\n\n          if (ctype !== null && /^multipart\\/mixed/i.test(ctype)) {\n            return readMultipartBody(response, observerNext);\n          } else {\n            return parseAndCheckHttpResponse(operation)(response).then(\n              observerNext\n            );\n          }\n        })\n        .then(() => {\n          controller = undefined;\n          observer.complete();\n        })\n        .catch((err) => {\n          controller = undefined;\n          handleError(err, observer);\n        });\n\n      return () => {\n        // XXX support canceling this request\n        // https://developers.google.com/web/updates/2017/09/abortable-fetch\n        if (controller) controller.abort();\n      };\n    });\n  });\n};\n"]}