{"version": 3, "file": "DataProxy.js", "sourceRoot": "", "sources": ["../../../../src/cache/core/types/DataProxy.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { DocumentNode } from \"graphql\"; // ignore-comment eslint-disable-line import/no-extraneous-dependencies, import/no-unresolved\nimport type { TypedDocumentNode } from \"@graphql-typed-document-node/core\";\n\nimport type { MissingFieldError } from \"./common.js\";\nimport type { Reference } from \"../../../utilities/index.js\";\nimport type { Unmasked } from \"../../../masking/index.js\";\n\nexport namespace DataProxy {\n  export interface Query<TVariables, TData> {\n    /**\n     * The GraphQL query shape to be used constructed using the `gql` template\n     * string tag from `graphql-tag`. The query will be used to determine the\n     * shape of the data to be read.\n     */\n    query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n\n    /**\n     * Any variables that the GraphQL query may depend on.\n     */\n    variables?: TVariables;\n\n    /**\n     * The root id to be used. Defaults to \"ROOT_QUERY\", which is the ID of the\n     * root query object. This property makes write<PERSON><PERSON>y capable of writing data\n     * to any object in the cache.\n     */\n    id?: string;\n  }\n\n  export interface Fragment<TVariables, TData> {\n    /**\n     * The root id to be used. This id should take the same form as the\n     * value returned by your `dataIdFromObject` function. If a value with your\n     * id does not exist in the store, `null` will be returned.\n     */\n    id?: string;\n\n    /**\n     * A GraphQL document created using the `gql` template string tag from\n     * `graphql-tag` with one or more fragments which will be used to determine\n     * the shape of data to read. If you provide more than one fragment in this\n     * document then you must also specify `fragmentName` to select a single.\n     */\n    fragment: DocumentNode | TypedDocumentNode<TData, TVariables>;\n\n    /**\n     * The name of the fragment in your GraphQL document to be used. If you do\n     * not provide a `fragmentName` and there is only one fragment in your\n     * `fragment` document then that fragment will be used.\n     */\n    fragmentName?: string;\n\n    /**\n     * Any variables that your GraphQL fragments depend on.\n     */\n    variables?: TVariables;\n  }\n\n  export interface ReadQueryOptions<TData, TVariables>\n    extends Query<TVariables, TData> {\n    /**\n     * Whether to return incomplete data rather than null.\n     * Defaults to false.\n     */\n    returnPartialData?: boolean;\n    /**\n     * Whether to read from optimistic or non-optimistic cache data. If\n     * this named option is provided, the optimistic parameter of the\n     * readQuery method can be omitted. Defaults to false.\n     */\n    optimistic?: boolean;\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#canonizeResults:member} */\n    canonizeResults?: boolean;\n  }\n\n  export interface ReadFragmentOptions<TData, TVariables>\n    extends Fragment<TVariables, TData> {\n    /**\n     * Whether to return incomplete data rather than null.\n     * Defaults to false.\n     */\n    returnPartialData?: boolean;\n    /**\n     * Whether to read from optimistic or non-optimistic cache data. If\n     * this named option is provided, the optimistic parameter of the\n     * readQuery method can be omitted. Defaults to false.\n     */\n    optimistic?: boolean;\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#canonizeResults:member} */\n    canonizeResults?: boolean;\n  }\n\n  export interface WriteOptions<TData> {\n    /**\n     * The data you will be writing to the store.\n     */\n    data: Unmasked<TData>;\n    /**\n     * Whether to notify query watchers (default: true).\n     */\n    broadcast?: boolean;\n    /**\n     * When true, ignore existing field data rather than merging it with\n     * incoming data (default: false).\n     */\n    overwrite?: boolean;\n  }\n\n  export interface WriteQueryOptions<TData, TVariables>\n    extends Query<TVariables, TData>,\n      WriteOptions<TData> {}\n\n  export interface WriteFragmentOptions<TData, TVariables>\n    extends Fragment<TVariables, TData>,\n      WriteOptions<TData> {}\n\n  export interface UpdateQueryOptions<TData, TVariables>\n    extends Omit<\n      ReadQueryOptions<TData, TVariables> &\n        WriteQueryOptions<TData, TVariables>,\n      \"data\"\n    > {}\n\n  export interface UpdateFragmentOptions<TData, TVariables>\n    extends Omit<\n      ReadFragmentOptions<TData, TVariables> &\n        WriteFragmentOptions<TData, TVariables>,\n      \"data\"\n    > {}\n\n  export type DiffResult<T> = {\n    result?: T;\n    complete?: boolean;\n    missing?: MissingFieldError[];\n    fromOptimisticTransaction?: boolean;\n  };\n}\n\n/**\n * A proxy to the normalized data living in our store. This interface allows a\n * user to read and write denormalized data which feels natural to the user\n * whilst in the background this data is being converted into the normalized\n * store format.\n */\nexport interface DataProxy {\n  /**\n   * Reads a GraphQL query from the root query id.\n   */\n  readQuery<QueryType, TVariables = any>(\n    options: DataProxy.ReadQueryOptions<QueryType, TVariables>,\n    optimistic?: boolean\n  ): Unmasked<QueryType> | null;\n\n  /**\n   * Reads a GraphQL fragment from any arbitrary id. If there is more than\n   * one fragment in the provided document then a `fragmentName` must be\n   * provided to select the correct fragment.\n   */\n  readFragment<FragmentType, TVariables = any>(\n    options: DataProxy.ReadFragmentOptions<FragmentType, TVariables>,\n    optimistic?: boolean\n  ): Unmasked<FragmentType> | null;\n\n  /**\n   * Writes a GraphQL query to the root query id.\n   */\n  writeQuery<TData = any, TVariables = any>(\n    options: DataProxy.WriteQueryOptions<TData, TVariables>\n  ): Reference | undefined;\n\n  /**\n   * Writes a GraphQL fragment to any arbitrary id. If there is more than\n   * one fragment in the provided document then a `fragmentName` must be\n   * provided to select the correct fragment.\n   */\n  writeFragment<TData = any, TVariables = any>(\n    options: DataProxy.WriteFragmentOptions<TData, TVariables>\n  ): Reference | undefined;\n}\n"]}