{"version": 3, "sources": ["../../@mui/material/esm/utils/useEnhancedEffect.js", "../../@mui/utils/esm/useEventCallback/useEventCallback.js", "../../@mui/material/esm/utils/useEventCallback.js", "../../@mui/material/esm/utils/useForkRef.js"], "sourcesContent": ["'use client';\n\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nexport default useEnhancedEffect;", "'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from \"../useEnhancedEffect/index.js\";\n\n/**\n * Inspired by https://github.com/facebook/react/issues/14099#issuecomment-440013892\n * See RFC in https://github.com/reactjs/rfcs/pull/220\n */\n\nfunction useEventCallback(fn) {\n  const ref = React.useRef(fn);\n  useEnhancedEffect(() => {\n    ref.current = fn;\n  });\n  return React.useRef((...args) =>\n  // @ts-expect-error hide `this`\n  (0, ref.current)(...args)).current;\n}\nexport default useEventCallback;", "'use client';\n\nimport useEventCallback from '@mui/utils/useEventCallback';\nexport default useEventCallback;", "'use client';\n\nimport useForkRef from '@mui/utils/useForkRef';\nexport default useForkRef;"], "mappings": ";;;;;;;;;;;;;;AAGA,IAAOA,6BAAQ;;;ACDf,YAAuB;AAQvB,SAAS,iBAAiB,IAAI;AAC5B,QAAM,MAAY,aAAO,EAAE;AAC3B,4BAAkB,MAAM;AACtB,QAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAa,aAAO,IAAI;AAAA;AAAA,KAEvB,GAAG,IAAI,SAAS,GAAG,IAAI;AAAA,GAAC,EAAE;AAC7B;AACA,IAAO,2BAAQ;;;AChBf,IAAOC,4BAAQ;;;ACAf,IAAO,qBAAQ;", "names": ["useEnhancedEffect_default", "useEventCallback_default"]}