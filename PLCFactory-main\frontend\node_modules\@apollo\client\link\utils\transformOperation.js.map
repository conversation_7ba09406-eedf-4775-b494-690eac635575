{"version": 3, "file": "transformOperation.js", "sourceRoot": "", "sources": ["../../../src/link/utils/transformOperation.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAE5D,MAAM,UAAU,kBAAkB,CAAC,SAAyB;IAC1D,IAAM,oBAAoB,GAAmB;QAC3C,SAAS,EAAE,SAAS,CAAC,SAAS,IAAI,EAAE;QACpC,UAAU,EAAE,SAAS,CAAC,UAAU,IAAI,EAAE;QACtC,aAAa,EAAE,SAAS,CAAC,aAAa;QACtC,KAAK,EAAE,SAAS,CAAC,KAAK;KACvB,CAAC;IAEF,kCAAkC;IAClC,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,CAAC;QACxC,oBAAoB,CAAC,aAAa;YAChC,OAAO,oBAAoB,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC;gBAC9C,gBAAgB,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,SAAS;gBAC3D,CAAC,CAAC,EAAE,CAAC;IACT,CAAC;IAED,OAAO,oBAAiC,CAAC;AAC3C,CAAC", "sourcesContent": ["import type { GraphQLRequest, Operation } from \"../core/index.js\";\nimport { getOperationName } from \"../../utilities/index.js\";\n\nexport function transformOperation(operation: GraphQLRequest): GraphQLRequest {\n  const transformedOperation: GraphQLRequest = {\n    variables: operation.variables || {},\n    extensions: operation.extensions || {},\n    operationName: operation.operationName,\n    query: operation.query,\n  };\n\n  // Best guess at an operation name\n  if (!transformedOperation.operationName) {\n    transformedOperation.operationName =\n      typeof transformedOperation.query !== \"string\" ?\n        getOperationName(transformedOperation.query) || undefined\n      : \"\";\n  }\n\n  return transformedOperation as Operation;\n}\n"]}