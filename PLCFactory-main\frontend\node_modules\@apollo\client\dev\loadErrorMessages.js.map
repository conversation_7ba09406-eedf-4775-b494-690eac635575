{"version": 3, "file": "loadErrorMessages.js", "sourceRoot": "", "sources": ["../../src/dev/loadErrorMessages.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAE,uBAAuB,EAAE,MAAM,8BAA8B,CAAC;AAEvE,MAAM,UAAU,iBAAiB;IAC/B,uBAAuB,CAAC,UAAU,CAAC,CAAC;AACtC,CAAC", "sourcesContent": ["import { errorCodes } from \"../invariantErrorCodes.js\";\nimport { loadErrorMessageHandler } from \"./loadErrorMessageHandler.js\";\n\nexport function loadErrorMessages() {\n  loadErrorMessageHandler(errorCodes);\n}\n"]}