{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/link/core/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { GraphQLFormattedError } from \"graphql\";\nimport type { DocumentNode } from \"graphql\";\nimport type { DefaultContext } from \"../../core/index.js\";\nexport type { DocumentNode };\n\nimport type { Observable } from \"../../utilities/index.js\";\n\nexport type Path = ReadonlyArray<string | number>;\n\ninterface ExecutionPatchResultBase {\n  hasNext?: boolean;\n}\n\nexport interface ExecutionPatchInitialResult<\n  TData = Record<string, any>,\n  TExtensions = Record<string, any>,\n> extends ExecutionPatchResultBase {\n  // if data is present, incremental is not\n  data: TData | null | undefined;\n  incremental?: never;\n  errors?: ReadonlyArray<GraphQLFormattedError>;\n  extensions?: TExtensions;\n}\n\nexport interface IncrementalPayload<TData, TExtensions> {\n  // data and path must both be present\n  // https://github.com/graphql/graphql-spec/pull/742/files#diff-98d0cd153b72b63c417ad4238e8cc0d3385691ccbde7f7674bc0d2a718b896ecR288-R293\n  data: TData | null;\n  label?: string;\n  path: Path;\n  errors?: ReadonlyArray<GraphQLFormattedError>;\n  extensions?: TExtensions;\n}\n\nexport interface ExecutionPatchIncrementalResult<\n  TData = Record<string, any>,\n  TExtensions = Record<string, any>,\n> extends ExecutionPatchResultBase {\n  // the reverse is also true: if incremental is present,\n  // data (and errors and extensions) are not\n  incremental?: IncrementalPayload<TData, TExtensions>[];\n  data?: never;\n  // Errors only exist for chunks, not at the top level\n  // https://github.com/robrichard/defer-stream-wg/discussions/50#discussioncomment-3466739\n  errors?: never;\n  extensions?: never;\n}\n\nexport interface ApolloPayloadResult<\n  TData = Record<string, any>,\n  TExtensions = Record<string, any>,\n> {\n  payload:\n    | SingleExecutionResult<TData, DefaultContext, TExtensions>\n    | ExecutionPatchResult<TData, TExtensions>\n    | null;\n  // Transport layer errors (as distinct from GraphQL or NetworkErrors),\n  // these are fatal errors that will include done: true.\n  errors?: ReadonlyArray<GraphQLFormattedError>;\n}\n\nexport type ExecutionPatchResult<\n  TData = Record<string, any>,\n  TExtensions = Record<string, any>,\n> =\n  | ExecutionPatchInitialResult<TData, TExtensions>\n  | ExecutionPatchIncrementalResult<TData, TExtensions>;\n\nexport interface GraphQLRequest<TVariables = Record<string, any>> {\n  query: DocumentNode;\n  variables?: TVariables;\n  operationName?: string;\n  context?: DefaultContext;\n  extensions?: Record<string, any>;\n}\n\nexport interface Operation {\n  query: DocumentNode;\n  variables: Record<string, any>;\n  operationName: string;\n  extensions: Record<string, any>;\n  setContext: {\n    (context: Partial<DefaultContext>): void;\n    (\n      updateContext: (\n        previousContext: DefaultContext\n      ) => Partial<DefaultContext>\n    ): void;\n  };\n  getContext: () => DefaultContext;\n}\n\nexport interface SingleExecutionResult<\n  TData = Record<string, any>,\n  TContext = DefaultContext,\n  TExtensions = Record<string, any>,\n> {\n  // data might be undefined if errorPolicy was set to 'ignore'\n  data?: TData | null;\n  context?: TContext;\n  errors?: ReadonlyArray<GraphQLFormattedError>;\n  extensions?: TExtensions;\n}\n\nexport type FetchResult<\n  TData = Record<string, any>,\n  TContext = Record<string, any>,\n  TExtensions = Record<string, any>,\n> =\n  | SingleExecutionResult<TData, TContext, TExtensions>\n  | ExecutionPatchResult<TData, TExtensions>;\n\nexport type NextLink = (operation: Operation) => Observable<FetchResult>;\n\nexport type RequestHandler = (\n  operation: Operation,\n  forward: NextLink\n) => Observable<FetchResult> | null;\n"]}