{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/masking/index.ts"], "names": [], "mappings": "AAQA,OAAO,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAC;AACjD,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC", "sourcesContent": ["export type {\n  DataMasking,\n  FragmentType,\n  Masked,\n  MaskedDocumentNode,\n  MaybeMasked,\n  Unmasked,\n} from \"./types.js\";\nexport { disableWarningsSlot } from \"./utils.js\";\nexport { maskFragment } from \"./maskFragment.js\";\nexport { maskOperation } from \"./maskOperation.js\";\n"]}