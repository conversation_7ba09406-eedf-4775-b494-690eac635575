{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/masking/utils.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAChC,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAC1D,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAErE,MAAM,CAAC,IAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;AACrD,MAAM,CAAC,IAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;AAErD,gFAAgF;AAChF,gBAAgB;AAChB,gBAAgB;AAChB,MAAM,CAAC,IAAM,mBAAmB,GAAG,IAAI,IAAI,EAAW,CAAC;AAEvD,IAAI,aAAa,GAAG,KAAK,CAAC;AAC1B,MAAM,UAAU,iCAAiC;IAC/C,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,aAAa,GAAG,IAAI,CAAC;QACrB,SAAS,CAAC,IAAI,CACZ,kLAAkL,CACnL,CAAC;IACJ,CAAC;AACH,CAAC", "sourcesContent": ["import { Slot } from \"optimism\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport { canUseWeakMap, canUseWeakSet } from \"../utilities/index.js\";\n\nexport const MapImpl = canUseWeakMap ? WeakMap : Map;\nexport const SetImpl = canUseWeakSet ? WeakSet : Set;\n\n// Contextual slot that allows us to disable accessor warnings on fields when in\n// migrate mode.\n/** @internal */\nexport const disableWarningsSlot = new Slot<boolean>();\n\nlet issuedWarning = false;\nexport function warnOnImproperCacheImplementation() {\n  if (!issuedWarning) {\n    issuedWarning = true;\n    invariant.warn(\n      \"The configured cache does not support data masking which effectively disables it. Please use a cache that supports data masking or disable data masking to silence this warning.\"\n    );\n  }\n}\n"]}