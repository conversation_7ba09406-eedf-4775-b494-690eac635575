{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../../../../src/cache/core/types/common.ts"], "names": [], "mappings": ";AA4BA;IAAuC,qCAAK;IAC1C,2BACkB,OAAe,EACf,IAA0C,EAC1C,KAAmB,EACnB,SAA+B;;QAE/C,sCAAsC;QACtC,YAAA,MAAK,YAAC,OAAO,CAAC,SAAC;QANC,aAAO,GAAP,OAAO,CAAQ;QACf,UAAI,GAAJ,IAAI,CAAsC;QAC1C,WAAK,GAAL,KAAK,CAAc;QACnB,eAAS,GAAT,SAAS,CAAsB;QAK/C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7B,KAAI,CAAC,OAAO,GAAG,KAAI,CAAC,OAAO,CAAC;YAC5B,KAAK,IAAI,CAAC,GAAG,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;gBAC/C,KAAI,CAAC,OAAO,aAAK,GAAC,KAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAG,KAAI,CAAC,OAAO,KAAE,CAAC;YAClD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,KAAI,CAAC,OAAO,GAAG,KAAI,CAAC,IAAI,CAAC;QAC3B,CAAC;QAED,2EAA2E;QAC3E,gCAAgC;QAC/B,KAAY,CAAC,SAAS,GAAG,iBAAiB,CAAC,SAAS,CAAC;;IACxD,CAAC;IAGH,wBAAC;AAAD,CAAC,AAzBD,CAAuC,KAAK,GAyB3C", "sourcesContent": ["import type { DocumentNode, FieldNode } from \"graphql\";\n\nimport type {\n  Reference,\n  StoreObject,\n  StoreValue,\n  isReference,\n  AsStoreObject,\n  DeepPartial,\n} from \"../../../utilities/index.js\";\n\nimport type { StorageType } from \"../../inmemory/policies.js\";\n\n// The Readonly<T> type only really works for object types, since it marks\n// all of the object's properties as readonly, but there are many cases when\n// a generic type parameter like TExisting might be a string or some other\n// primitive type, in which case we need to avoid wrapping it with <PERSON>only.\n// SafeReadonly<string> collapses to just string, which makes string\n// assignable to SafeReadonly<any>, whereas string is not assignable to\n// Readonly<any>, somewhat surprisingly.\nexport type SafeReadonly<T> = T extends object ? Readonly<T> : T;\n\nexport type MissingTree =\n  | string\n  | {\n      readonly [key: string]: MissingTree;\n    };\n\nexport class MissingFieldError extends Error {\n  constructor(\n    public readonly message: string,\n    public readonly path: MissingTree | Array<string | number>,\n    public readonly query: DocumentNode,\n    public readonly variables?: Record<string, any>\n  ) {\n    // 'Error' breaks prototype chain here\n    super(message);\n\n    if (Array.isArray(this.path)) {\n      this.missing = this.message;\n      for (let i = this.path.length - 1; i >= 0; --i) {\n        this.missing = { [this.path[i]]: this.missing };\n      }\n    } else {\n      this.missing = this.path;\n    }\n\n    // We're not using `Object.setPrototypeOf` here as it isn't fully supported\n    // on Android (see issue #3236).\n    (this as any).__proto__ = MissingFieldError.prototype;\n  }\n\n  public readonly missing: MissingTree;\n}\n\nexport interface FieldSpecifier {\n  typename?: string;\n  fieldName: string;\n  field?: FieldNode;\n  args?: Record<string, any>;\n  variables?: Record<string, any>;\n}\n\nexport interface ReadFieldOptions extends FieldSpecifier {\n  from?: StoreObject | Reference;\n}\n\nexport interface ReadFieldFunction {\n  <V = StoreValue>(options: ReadFieldOptions): SafeReadonly<V> | undefined;\n  <V = StoreValue>(\n    fieldName: string,\n    from?: StoreObject | Reference\n  ): SafeReadonly<V> | undefined;\n}\n\nexport type ToReferenceFunction = (\n  objOrIdOrRef: StoreObject | string | Reference,\n  mergeIntoStore?: boolean\n) => Reference | undefined;\n\nexport type CanReadFunction = (value: StoreValue) => boolean;\n\ndeclare const _deleteModifier: unique symbol;\nexport interface DeleteModifier {\n  [_deleteModifier]: true;\n}\ndeclare const _invalidateModifier: unique symbol;\nexport interface InvalidateModifier {\n  [_invalidateModifier]: true;\n}\ndeclare const _ignoreModifier: unique symbol;\nexport interface IgnoreModifier {\n  [_ignoreModifier]: true;\n}\n\nexport type ModifierDetails = {\n  DELETE: DeleteModifier;\n  INVALIDATE: InvalidateModifier;\n  fieldName: string;\n  storeFieldName: string;\n  readField: ReadFieldFunction;\n  canRead: CanReadFunction;\n  isReference: typeof isReference;\n  toReference: ToReferenceFunction;\n  storage: StorageType;\n};\n\nexport type Modifier<T> = (\n  value: T,\n  details: ModifierDetails\n) => DeepPartial<T> | DeleteModifier | InvalidateModifier | undefined;\n\ntype StoreObjectValueMaybeReference<StoreVal> =\n  StoreVal extends Array<Record<string, any>> ?\n    StoreVal extends Array<infer Item> ?\n      [Item] extends [Record<string, any>] ?\n        ReadonlyArray<AsStoreObject<Item> | Reference>\n      : never\n    : never\n  : StoreVal extends Record<string, any> ? AsStoreObject<StoreVal> | Reference\n  : StoreVal;\n\nexport type AllFieldsModifier<Entity extends Record<string, any>> = Modifier<\n  Entity[keyof Entity] extends infer Value ?\n    StoreObjectValueMaybeReference<Exclude<Value, undefined>>\n  : never\n>;\n\nexport type Modifiers<T extends Record<string, any> = Record<string, unknown>> =\n  Partial<{\n    [FieldName in keyof T]: Modifier<\n      StoreObjectValueMaybeReference<Exclude<T[FieldName], undefined>>\n    >;\n  }>;\n"]}