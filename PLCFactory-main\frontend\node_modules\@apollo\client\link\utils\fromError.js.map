{"version": 3, "file": "fromError.js", "sourceRoot": "", "sources": ["../../../src/link/utils/fromError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAEtD,MAAM,UAAU,SAAS,CAAI,UAAe;IAC1C,OAAO,IAAI,UAAU,CAAI,UAAC,QAAQ;QAChC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { Observable } from \"../../utilities/index.js\";\n\nexport function fromError<T>(errorValue: any): Observable<T> {\n  return new Observable<T>((observer) => {\n    observer.error(errorValue);\n  });\n}\n"]}