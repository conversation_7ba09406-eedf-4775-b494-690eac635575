{"version": 3, "file": "core.cjs", "sources": ["ApolloLink.js", "empty.js", "from.js", "split.js", "concat.js", "execute.js"], "sourcesContent": ["import { newInvariantError, invariant } from \"../../utilities/globals/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport { validateOperation, createOperation, transformOperation, } from \"../utils/index.js\";\nfunction passthrough(op, forward) {\n    return (forward ? forward(op) : Observable.of());\n}\nfunction toLink(handler) {\n    return typeof handler === \"function\" ? new ApolloLink(handler) : handler;\n}\nfunction isTerminating(link) {\n    return link.request.length <= 1;\n}\nvar ApolloLink = /** @class */ (function () {\n    function ApolloLink(request) {\n        if (request)\n            this.request = request;\n    }\n    ApolloLink.empty = function () {\n        return new ApolloLink(function () { return Observable.of(); });\n    };\n    ApolloLink.from = function (links) {\n        if (links.length === 0)\n            return ApolloLink.empty();\n        return links.map(toLink).reduce(function (x, y) { return x.concat(y); });\n    };\n    ApolloLink.split = function (test, left, right) {\n        var leftLink = toLink(left);\n        var rightLink = toLink(right || new ApolloLink(passthrough));\n        var ret;\n        if (isTerminating(leftLink) && isTerminating(rightLink)) {\n            ret = new ApolloLink(function (operation) {\n                return test(operation) ?\n                    leftLink.request(operation) || Observable.of()\n                    : rightLink.request(operation) || Observable.of();\n            });\n        }\n        else {\n            ret = new ApolloLink(function (operation, forward) {\n                return test(operation) ?\n                    leftLink.request(operation, forward) || Observable.of()\n                    : rightLink.request(operation, forward) || Observable.of();\n            });\n        }\n        return Object.assign(ret, { left: leftLink, right: rightLink });\n    };\n    ApolloLink.execute = function (link, operation) {\n        return (link.request(createOperation(operation.context, transformOperation(validateOperation(operation)))) || Observable.of());\n    };\n    ApolloLink.concat = function (first, second) {\n        var firstLink = toLink(first);\n        if (isTerminating(firstLink)) {\n            globalThis.__DEV__ !== false && invariant.warn(38, firstLink);\n            return firstLink;\n        }\n        var nextLink = toLink(second);\n        var ret;\n        if (isTerminating(nextLink)) {\n            ret = new ApolloLink(function (operation) {\n                return firstLink.request(operation, function (op) { return nextLink.request(op) || Observable.of(); }) || Observable.of();\n            });\n        }\n        else {\n            ret = new ApolloLink(function (operation, forward) {\n                return (firstLink.request(operation, function (op) {\n                    return nextLink.request(op, forward) || Observable.of();\n                }) || Observable.of());\n            });\n        }\n        return Object.assign(ret, { left: firstLink, right: nextLink });\n    };\n    ApolloLink.prototype.split = function (test, left, right) {\n        return this.concat(ApolloLink.split(test, left, right || new ApolloLink(passthrough)));\n    };\n    ApolloLink.prototype.concat = function (next) {\n        return ApolloLink.concat(this, next);\n    };\n    ApolloLink.prototype.request = function (operation, forward) {\n        throw newInvariantError(39);\n    };\n    ApolloLink.prototype.onError = function (error, observer) {\n        if (observer && observer.error) {\n            observer.error(error);\n            // Returning false indicates that observer.error does not need to be\n            // called again, since it was already called (on the previous line).\n            // Calling observer.error again would not cause any real problems,\n            // since only the first call matters, but custom onError functions\n            // might have other reasons for wanting to prevent the default\n            // behavior by returning false.\n            return false;\n        }\n        // Throw errors will be passed to observer.error.\n        throw error;\n    };\n    ApolloLink.prototype.setOnError = function (fn) {\n        this.onError = fn;\n        return this;\n    };\n    return ApolloLink;\n}());\nexport { ApolloLink };\n//# sourceMappingURL=ApolloLink.js.map", "import { ApolloLink } from \"./ApolloLink.js\";\nexport var empty = ApolloLink.empty;\n//# sourceMappingURL=empty.js.map", "import { ApolloLink } from \"./ApolloLink.js\";\nexport var from = ApolloLink.from;\n//# sourceMappingURL=from.js.map", "import { ApolloLink } from \"./ApolloLink.js\";\nexport var split = ApolloLink.split;\n//# sourceMappingURL=split.js.map", "import { ApolloLink } from \"./ApolloLink.js\";\nexport var concat = ApolloLink.concat;\n//# sourceMappingURL=concat.js.map", "import { ApolloLink } from \"./ApolloLink.js\";\nexport var execute = ApolloLink.execute;\n//# sourceMappingURL=execute.js.map"], "names": ["Observable", "createOperation", "transformOperation", "validateOperation", "invariant", "newInvariantError"], "mappings": ";;;;;;;;AAGA,SAAS,WAAW,CAAC,EAAE,EAAE,OAAO,EAAE;AAClC,IAAI,QAAQ,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,GAAGA,oBAAU,CAAC,EAAE,EAAE,EAAE;AACrD,CAAC;AACD,SAAS,MAAM,CAAC,OAAO,EAAE;AACzB,IAAI,OAAO,OAAO,OAAO,KAAK,UAAU,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;AAC7E,CAAC;AACD,SAAS,aAAa,CAAC,IAAI,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;AACpC,CAAC;AACE,IAAC,UAAU,KAAkB,YAAY;AAC5C,IAAI,SAAS,UAAU,CAAC,OAAO,EAAE;AACjC,QAAQ,IAAI,OAAO;AACnB,YAAY,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACnC,KAAK;AACL,IAAI,UAAU,CAAC,KAAK,GAAG,YAAY;AACnC,QAAQ,OAAO,IAAI,UAAU,CAAC,YAAY,EAAE,OAAOA,oBAAU,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;AACvE,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,IAAI,GAAG,UAAU,KAAK,EAAE;AACvC,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;AAC9B,YAAY,OAAO,UAAU,CAAC,KAAK,EAAE,CAAC;AACtC,QAAQ,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACjF,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,KAAK,GAAG,UAAU,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;AACpD,QAAQ,IAAI,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AACpC,QAAQ,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,IAAI,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;AACrE,QAAQ,IAAI,GAAG,CAAC;AAChB,QAAQ,IAAI,aAAa,CAAC,QAAQ,CAAC,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE;AACjE,YAAY,GAAG,GAAG,IAAI,UAAU,CAAC,UAAU,SAAS,EAAE;AACtD,gBAAgB,OAAO,IAAI,CAAC,SAAS,CAAC;AACtC,oBAAoB,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,IAAIA,oBAAU,CAAC,EAAE,EAAE;AAClE,sBAAsB,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,IAAIA,oBAAU,CAAC,EAAE,EAAE,CAAC;AACtE,aAAa,CAAC,CAAC;AACf,SAAS;AACT,aAAa;AACb,YAAY,GAAG,GAAG,IAAI,UAAU,CAAC,UAAU,SAAS,EAAE,OAAO,EAAE;AAC/D,gBAAgB,OAAO,IAAI,CAAC,SAAS,CAAC;AACtC,oBAAoB,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,IAAIA,oBAAU,CAAC,EAAE,EAAE;AAC3E,sBAAsB,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,IAAIA,oBAAU,CAAC,EAAE,EAAE,CAAC;AAC/E,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;AACxE,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,OAAO,GAAG,UAAU,IAAI,EAAE,SAAS,EAAE;AACpD,QAAQ,QAAQ,IAAI,CAAC,OAAO,CAACC,qBAAe,CAAC,SAAS,CAAC,OAAO,EAAEC,wBAAkB,CAACC,uBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAIH,oBAAU,CAAC,EAAE,EAAE,EAAE;AACvI,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,MAAM,GAAG,UAAU,KAAK,EAAE,MAAM,EAAE;AACjD,QAAQ,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACtC,QAAQ,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE;AACtC,YAAY,UAAU,CAAC,OAAO,KAAK,KAAK,IAAII,iBAAS,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;AAC1E,YAAY,OAAO,SAAS,CAAC;AAC7B,SAAS;AACT,QAAQ,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACtC,QAAQ,IAAI,GAAG,CAAC;AAChB,QAAQ,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE;AACrC,YAAY,GAAG,GAAG,IAAI,UAAU,CAAC,UAAU,SAAS,EAAE;AACtD,gBAAgB,OAAO,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,EAAE,EAAE,EAAE,OAAO,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,IAAIJ,oBAAU,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,IAAIA,oBAAU,CAAC,EAAE,EAAE,CAAC;AAC1I,aAAa,CAAC,CAAC;AACf,SAAS;AACT,aAAa;AACb,YAAY,GAAG,GAAG,IAAI,UAAU,CAAC,UAAU,SAAS,EAAE,OAAO,EAAE;AAC/D,gBAAgB,QAAQ,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,EAAE,EAAE;AACnE,oBAAoB,OAAO,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,IAAIA,oBAAU,CAAC,EAAE,EAAE,CAAC;AAC5E,iBAAiB,CAAC,IAAIA,oBAAU,CAAC,EAAE,EAAE,EAAE;AACvC,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AACxE,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;AAC9D,QAAQ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AAC/F,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,IAAI,EAAE;AAClD,QAAQ,OAAO,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC7C,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,SAAS,EAAE,OAAO,EAAE;AACjE,QAAQ,MAAMK,yBAAiB,CAAC,EAAE,CAAC,CAAC;AACpC,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,KAAK,EAAE,QAAQ,EAAE;AAC9D,QAAQ,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxC,YAAY,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAOlC,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AAET,QAAQ,MAAM,KAAK,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,EAAE,EAAE;AACpD,QAAQ,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AAC1B,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,OAAO,UAAU,CAAC;AACtB,CAAC,EAAE;;ACjGO,IAAC,KAAK,GAAG,UAAU,CAAC;;ACApB,IAAC,IAAI,GAAG,UAAU,CAAC;;ACAnB,IAAC,KAAK,GAAG,UAAU,CAAC;;ACApB,IAAC,MAAM,GAAG,UAAU,CAAC;;ACArB,IAAC,OAAO,GAAG,UAAU,CAAC;;;;;;;;;"}