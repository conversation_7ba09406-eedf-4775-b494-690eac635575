{"version": 3, "sources": ["../../@apollo/src/link/subscriptions/index.ts"], "sourcesContent": ["// This file is adapted from the graphql-ws npm package:\n// https://github.com/enisdenjo/graphql-ws\n//\n// Most of the file comes from that package's README; some other parts (such as\n// isLikeCloseEvent) come from its source.\n//\n// Here's the license of the original code:\n//\n// The MIT License (MIT)\n//\n// Copyright (c) 2020-2021 <PERSON>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nimport { print } from \"../../utilities/index.js\";\nimport type { Client, Sink } from \"graphql-ws\";\n\nimport type { Operation, FetchResult } from \"../core/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { isNonNullObject, Observable } from \"../../utilities/index.js\";\nimport { ApolloError } from \"../../errors/index.js\";\nimport type { FormattedExecutionResult } from \"graphql\";\n\n// https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/close_event\nfunction isLikeCloseEvent(val: unknown): val is CloseEvent {\n  return isNonNullObject(val) && \"code\" in val && \"reason\" in val;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/error_event\nfunction isLikeErrorEvent(err: unknown): err is Event {\n  return isNonNullObject(err) && err.target?.readyState === WebSocket.CLOSED;\n}\n\nexport class GraphQLWsLink extends ApolloLink {\n  constructor(public readonly client: Client) {\n    super();\n  }\n\n  public request(operation: Operation): Observable<FetchResult> {\n    return new Observable((observer) => {\n      return this.client.subscribe<FetchResult>(\n        { ...operation, query: print(operation.query) },\n        {\n          next: observer.next.bind(observer),\n          complete: observer.complete.bind(observer),\n          error: (err) => {\n            if (err instanceof Error) {\n              return observer.error(err);\n            }\n            const likeClose = isLikeCloseEvent(err);\n            if (likeClose || isLikeErrorEvent(err)) {\n              return observer.error(\n                // reason will be available on clean closes\n                new Error(\n                  `Socket closed${likeClose ? ` with event ${err.code}` : \"\"}${\n                    likeClose ? ` ${err.reason}` : \"\"\n                  }`\n                )\n              );\n            }\n\n            return observer.error(\n              new ApolloError({\n                graphQLErrors: Array.isArray(err) ? err : [err],\n              })\n            );\n          },\n          // casting around a wrong type in graphql-ws, which incorrectly expects `Sink<ExecutionResult>`\n        } satisfies Sink<FormattedExecutionResult> as any\n      );\n    });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAwCA,SAAS,iBAAiB,KAAY;AACpC,SAAO,gBAAgB,GAAG,KAAK,UAAU,OAAO,YAAY;AAC9D;AAGA,SAAS,iBAAiB,KAAY;;AACpC,SAAO,gBAAgB,GAAG,OAAK,KAAA,IAAI,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,gBAAe,UAAU;AACtE;AAEA,IAAA;;EAAA,SAAA,QAAA;AAAmC,cAAAA,gBAAA,MAAA;AACjC,aAAAA,eAA4B,QAAc;AACxC,UAAA,QAAA,OAAK,KAAA,IAAA,KAAE;AADmB,YAAA,SAAA;;IAE5B;AAEO,IAAAA,eAAA,UAAA,UAAP,SAAe,WAAoB;AAAnC,UAAA,QAAA;AACE,aAAO,IAAI,WAAW,SAAC,UAAQ;AAC7B,eAAO,MAAK,OAAO,UAAS,SAAA,SAAA,CAAA,GACrB,SAAS,GAAA,EAAE,OAAO,MAAM,UAAU,KAAK,EAAC,CAAA,GAC7C;UACE,MAAM,SAAS,KAAK,KAAK,QAAQ;UACjC,UAAU,SAAS,SAAS,KAAK,QAAQ;UACzC,OAAO,SAAC,KAAG;AACT,gBAAI,eAAe,OAAO;AACxB,qBAAO,SAAS,MAAM,GAAG;YAC3B;AACA,gBAAM,YAAY,iBAAiB,GAAG;AACtC,gBAAI,aAAa,iBAAiB,GAAG,GAAG;AACtC,qBAAO,SAAS;;gBAEd,IAAI,MACF,gBAAA,OAAgB,YAAY,eAAA,OAAe,IAAI,IAAI,IAAK,EAAE,EAAA,OACxD,YAAY,IAAA,OAAI,IAAI,MAAM,IAAK,EAAE,CACjC;cACH;YAEL;AAEA,mBAAO,SAAS,MACd,IAAI,YAAY;cACd,eAAe,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,GAAG;aAC/C,CAAC;UAEN;;SAE+C;MAErD,CAAC;IACH;AACF,WAAAA;EAAA,EAvCmC,UAAU;;", "names": ["GraphQLWsLink"]}