# AutoLadder Authentication Architecture

## Overview

This document outlines the authentication and authorization architecture for AutoLadder, the "GitHub for PLCs" platform. The system supports multiple authentication methods including OAuth 2.0, JWT tokens, and enterprise SSO.

## Architecture Components

### 1. Authentication Service Layer

```
┌─────────────────────────────────────────────────────────────────┐
│                    Authentication Service                        │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │   OAuth     │  │    JWT      │  │    SAML     │  │  Local  │ │
│  │ Providers   │  │   Manager   │  │    SSO      │  │  Auth   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 2. Database Schema

#### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    display_name VARCHAR(100),
    avatar_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);
```

#### OAuth Providers Table
```sql
CREATE TABLE oauth_providers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    provider VARCHAR(50) NOT NULL,
    provider_user_id VARCHAR(255) NOT NULL,
    access_token TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Sessions Table
```sql
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Roles Table
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '[]',
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- System roles
INSERT INTO roles (name, description, permissions, is_system_role) VALUES
('admin', 'Platform administrator', '["*"]', true),
('maintainer', 'Repository maintainer', '["repo:read", "repo:write", "repo:admin", "user:read"]', true),
('developer', 'Repository developer', '["repo:read", "repo:write", "user:read"]', true),
('viewer', 'Repository viewer', '["repo:read", "user:read"]', true);
```

#### Organizations Table
```sql
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(100),
    description TEXT,
    avatar_url TEXT,
    website_url TEXT,
    billing_email VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    plan VARCHAR(50) DEFAULT 'free', -- 'free', 'pro', 'enterprise'
    settings JSONB DEFAULT '{}'
);
```

#### Organization Members Table
```sql
CREATE TABLE organization_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id),
    invited_by UUID REFERENCES users(id),
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    invitation_token VARCHAR(255),
    invitation_expires_at TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'pending', 'suspended'
    UNIQUE(organization_id, user_id)
);
```

#### Repository Permissions Table
```sql
CREATE TABLE repository_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repository_id UUID NOT NULL, -- References repositories table
    user_id UUID REFERENCES users(id),
    organization_id UUID REFERENCES organizations(id),
    permission_level VARCHAR(20) NOT NULL, -- 'read', 'write', 'admin'
    granted_by UUID REFERENCES users(id),
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CHECK (user_id IS NOT NULL OR organization_id IS NOT NULL)
);
```

### 3. JWT Token Structure

#### Access Token Payload
```json
{
  "sub": "user-uuid",
  "iss": "autoladder.com",
  "exp": **********,
  "user": {
    "id": "user-uuid",
    "username": "john_doe",
    "email": "<EMAIL>"
  }
}
```

#### Refresh Token Payload
```json
{
  "sub": "user-uuid",
  "iss": "autoladder.com",
  "aud": "autoladder-api",
  "exp": **********,
  "iat": **********,
  "jti": "refresh-token-uuid",
  "type": "refresh",
  "session_id": "session-uuid"
}
```

### 4. OAuth 2.0 Flow

#### Supported Providers
- **GitHub**: Primary for PLC developers
- **Google**: General authentication
- **Microsoft**: Enterprise integration

#### OAuth Flow Diagram
```
User → Frontend → Auth Service → OAuth Provider → Auth Service → Frontend → User
  1. Click "Login with GitHub"
  2. Redirect to /auth/github
  3. Redirect to GitHub OAuth
  4. User authorizes
  5. GitHub callback with code
  6. Exchange code for token
  7. Fetch user profile
  8. Create/update user
  9. Generate JWT tokens
  10. Set secure cookies
  11. Redirect to dashboard
```

### 5. Role-Based Access Control (RBAC)

#### Permission System
```typescript
interface Permission {
  resource: string; // 'repo', 'user', 'org', 'admin'
  action: string;   // 'read', 'write', 'delete', 'admin'
}

// Examples:
// "repo:read" - Can view repositories
// "repo:write" - Can modify repositories
// "repo:admin" - Can manage repository settings
// "org:admin" - Can manage organization
// "admin:*" - Platform administrator
```

#### Role Hierarchy
```
Admin (Platform)
├── Organization Owner
│   ├── Organization Admin
│   ├── Repository Maintainer
│   │   ├── Repository Developer
│   │   └── Repository Viewer
│   └── Organization Member
└── Individual User
```

### 6. Security Measures

#### Token Security
- JWT signed with RS256 (RSA with SHA-256)
- Separate signing keys for access and refresh tokens
- Token rotation on refresh
- Secure HTTP-only cookies for web clients
- Token blacklisting for logout

#### Password Security (if local auth enabled)
- bcrypt with cost factor 12
- Password strength requirements
- Rate limiting on login attempts
- Account lockout after failed attempts

#### Session Security
- Session fingerprinting (IP + User-Agent)
- Automatic session invalidation
- Concurrent session limits
- Device tracking and management

### 7. Enterprise SSO (SAML 2.0)

#### Supported Identity Providers
- Active Directory Federation Services (ADFS)
- Azure Active Directory
- Okta
- Auth0
- Generic SAML 2.0 providers

#### SAML Configuration
```typescript
interface SAMLConfig {
  organizationId: string;
  entityId: string;
  ssoUrl: string;
  sloUrl?: string;
  certificate: string;
  attributeMapping: {
    email: string;
    firstName?: string;
    lastName?: string;
    groups?: string;
  };
  autoProvisioning: boolean;
  defaultRole: string;
}
```

### 8. API Endpoints

#### Authentication Endpoints
```
POST   /api/auth/login              # Local login
POST   /api/auth/logout             # Logout
POST   /api/auth/refresh            # Refresh tokens
GET    /api/auth/me                 # Current user info

# OAuth
GET    /api/auth/oauth/:provider     # OAuth redirect
GET    /api/auth/callback/:provider # OAuth callback

# SAML SSO
GET    /api/auth/saml/:org          # SAML redirect
POST   /api/auth/saml/callback      # SAML callback
```

#### User Management Endpoints
```
GET    /api/users/profile           # Get user profile
PUT    /api/users/profile           # Update profile
POST   /api/users/avatar            # Upload avatar
GET    /api/users/sessions          # List sessions
DELETE /api/users/sessions/:id      # Revoke session
```

#### Organization Endpoints
```
GET    /api/orgs                    # List user's organizations
POST   /api/orgs                    # Create organization
GET    /api/orgs/:id                # Get organization
PUT    /api/orgs/:id                # Update organization
GET    /api/orgs/:id/members        # List members
POST   /api/orgs/:id/invite         # Invite member
PUT    /api/orgs/:id/members/:uid   # Update member role
DELETE /api/orgs/:id/members/:uid   # Remove member
```

### 9. Frontend Integration

#### Authentication Context
```typescript
interface AuthContext {
  user: User | null;
  loading: boolean;
  login: (provider: string) => void;
  logout: () => void;
  refreshToken: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
}
```

#### Protected Routes
```typescript
// Route protection wrapper
<ProtectedRoute requiredPermission="repo:read">
  <RepositoryPage />
</ProtectedRoute>

// Organization context
<OrganizationProvider orgId={orgId}>
  <ProtectedRoute requiredRole="maintainer">
    <OrgSettingsPage />
  </ProtectedRoute>
</OrganizationProvider>
```

### 10. Implementation Phases

#### Phase 1: Core Authentication
- [ ] Database schema setup
- [ ] JWT token management
- [ ] OAuth 2.0 providers (GitHub, Google, Microsoft)
- [ ] Basic user profiles
- [ ] Session management

#### Phase 2: Authorization System
- [ ] Role-based access control
- [ ] Permission middleware
- [ ] Repository permissions
- [ ] Frontend route protection

#### Phase 3: Organization Features
- [ ] Organization management
- [ ] Team invitations
- [ ] Member role management
- [ ] Organization settings

#### Phase 4: Enterprise Features
- [ ] SAML SSO integration
- [ ] Advanced security features
- [ ] Audit logging
- [ ] Compliance features

### 11. Security Considerations

#### Data Protection
- Encrypt sensitive data at rest
- Use HTTPS for all communications
- Implement CORS properly
- Sanitize all user inputs
- Regular security audits

#### Compliance
- GDPR compliance for EU users
- SOC 2 Type II for enterprise
- Regular penetration testing
- Security incident response plan

#### Monitoring
- Authentication event logging
- Failed login attempt monitoring
- Unusual activity detection
- Security metrics dashboard

### 12. Testing Strategy

#### Unit Tests
- JWT token generation/validation
- Password hashing/verification
- Permission checking logic
- OAuth flow components

#### Integration Tests
- Complete OAuth flows
- SAML SSO integration
- Database operations
- API endpoint security

#### Security Tests
- Token manipulation attempts
- Permission bypass attempts
- Rate limiting effectiveness
- Session security validation

#### End-to-End Tests
- User registration flow
- Login/logout flows
- Organization management
- Repository access control

## Conclusion

This authentication architecture provides a secure, scalable foundation for AutoLadder's user management needs. It supports modern authentication patterns while maintaining enterprise-grade security and compliance requirements. 