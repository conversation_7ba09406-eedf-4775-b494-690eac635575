{"version": 3, "file": "object-canon.js", "sourceRoot": "", "sources": ["../../../src/cache/inmemory/object-canon.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AACjC,OAAO,EACL,aAAa,EACb,aAAa,EACb,eAAe,IAAI,eAAe,GACnC,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAEvC,SAAS,WAAW,CAAI,KAAQ;IAC9B,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;QAC3B,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;YAClB,KAAK,CAAC,KAAK,CAAC,CAAC,CAAc;YAC9B,CAAC,YAAG,SAAS,EAAE,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,IAAK,KAAK,CAAE,CAAC;IAC5D,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,sEAAsE;AACtE,qEAAqE;AACrE,iDAAiD;AACjD,EAAE;AACF,oEAAoE;AACpE,yEAAyE;AACzE,sEAAsE;AACtE,sEAAsE;AACtE,mEAAmE;AACnE,0EAA0E;AAC1E,4BAA4B;AAC5B,EAAE;AACF,wEAAwE;AACxE,qEAAqE;AACrE,sEAAsE;AACtE,qDAAqD;AACrD,EAAE;AACF,wEAAwE;AACxE,sEAAsE;AACtE,uEAAuE;AACvE,wEAAwE;AACxE,yEAAyE;AACzE,sDAAsD;AACtD,EAAE;AACF,yEAAyE;AACzE,yEAAyE;AACzE,wEAAwE;AACxE,wEAAwE;AACxE,yDAAyD;AACzD,EAAE;AACF,sEAAsE;AACtE,wEAAwE;AACxE,oEAAoE;AACpE,mEAAmE;AACnE,EAAE;AACF,kEAAkE;AAClE,0EAA0E;AAC1E,wEAAwE;AACxE,wEAAwE;AACxE,EAAE;AACF,uEAAuE;AACvE,wEAAwE;AACxE,wEAAwE;AACxE,oCAAoC;AACpC,EAAE;AACF,yEAAyE;AACzE,mEAAmE;AACnE,uEAAuE;AACvE,wEAAwE;AACxE,yEAAyE;AACzE,oBAAoB;AACpB,EAAE;AACF,uEAAuE;AACvE,yEAAyE;AACzE,sBAAsB;AACtB;IAAA;QACE,uEAAuE;QACvE,sEAAsE;QAC9D,UAAK,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAU,CAAC;QAE9D,4DAA4D;QACpD,SAAI,GAAG,IAAI,IAAI,CAIpB,aAAa,CAAC,CAAC;QAMlB,0DAA0D;QAC1D,iBAAiB;QACT,WAAM,GAAG,IAAI,OAAO,EAAkB,CAAC;QA+F/C,uEAAuE;QACvE,kDAAkD;QAC1C,eAAU,GAAG,IAAI,GAAG,EAA0B,CAAC;QAEvD,0DAA0D;QAC1C,UAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACzC,CAAC;IA3GQ,6BAAO,GAAd,UAAe,KAAU;QACvB,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACzD,CAAC;IAMM,0BAAI,GAAX,UAAY,KAAU;QACpB,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3B,IAAM,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAIM,2BAAK,GAAZ,UAAa,KAAU;QAAvB,iBAgEC;QA/DC,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3B,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,QAAQ;gBAAE,OAAO,QAAQ,CAAC;YAE9B,IAAM,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC3C,QAAQ,KAAK,EAAE,CAAC;gBACd,KAAK,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;oBACrB,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;wBAAE,OAAO,KAAK,CAAC;oBACxC,IAAM,KAAK,GAAW,KAAe,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;oBAC5D,2DAA2D;oBAC3D,gEAAgE;oBAChE,2BAA2B;oBAC3B,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;oBAC1C,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;wBAChB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;wBACrC,sDAAsD;wBACtD,wDAAwD;wBACxD,wDAAwD;wBACxD,IAAI,OAAO,EAAE,CAAC;4BACZ,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBACvB,CAAC;oBACH,CAAC;oBACD,OAAO,IAAI,CAAC,KAAK,CAAC;gBACpB,CAAC;gBAED,KAAK,IAAI,CAAC;gBACV,KAAK,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;oBACtB,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;wBAAE,OAAO,KAAK,CAAC;oBACxC,IAAM,OAAK,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;oBAC3C,IAAM,OAAK,GAAG,CAAC,OAAK,CAAC,CAAC;oBACtB,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;oBACpC,OAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACtB,IAAM,iBAAe,GAAG,OAAK,CAAC,MAAM,CAAC;oBACrC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAC,GAAG;wBACtB,OAAK,CAAC,IAAI,CAAC,KAAI,CAAC,KAAK,CAAE,KAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC9C,CAAC,CAAC,CAAC;oBACH,8DAA8D;oBAC9D,0DAA0D;oBAC1D,yDAAyD;oBACzD,gEAAgE;oBAChE,+DAA+D;oBAC/D,gEAAgE;oBAChE,6DAA6D;oBAC7D,mCAAmC;oBACnC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAK,CAAC,CAAC;oBAC1C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;wBACjB,IAAM,KAAG,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,OAAK,CAAC,CAAC,CAAC;wBACjD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAG,CAAC,CAAC;wBACpB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAC,GAAG,EAAE,CAAC;4BACzB,KAAG,CAAC,GAAG,CAAC,GAAG,OAAK,CAAC,iBAAe,GAAG,CAAC,CAAC,CAAC;wBACxC,CAAC,CAAC,CAAC;wBACH,uDAAuD;wBACvD,wDAAwD;wBACxD,wDAAwD;wBACxD,IAAI,OAAO,EAAE,CAAC;4BACZ,MAAM,CAAC,MAAM,CAAC,KAAG,CAAC,CAAC;wBACrB,CAAC;oBACH,CAAC;oBACD,OAAO,IAAI,CAAC,MAAM,CAAC;gBACrB,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,uEAAuE;IACvE,kEAAkE;IAClE,qEAAqE;IACrE,qEAAqE;IAC7D,gCAAU,GAAlB,UAAmB,GAAW;QAC5B,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBAC7C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAOH,kBAAC;AAAD,CAAC,AAvHD,IAuHC", "sourcesContent": ["import { <PERSON>e } from \"@wry/trie\";\nimport {\n  canUseWeakMap,\n  canUseWeakSet,\n  isNonNullObject as isObjectOrArray,\n} from \"../../utilities/index.js\";\nimport { isArray } from \"./helpers.js\";\n\nfunction shallowCopy<T>(value: T): T {\n  if (isObjectOrArray(value)) {\n    return isArray(value) ?\n        (value.slice(0) as any as T)\n      : { __proto__: Object.getPrototypeOf(value), ...value };\n  }\n  return value;\n}\n\n// When programmers talk about the \"canonical form\" of an object, they\n// usually have the following meaning in mind, which I've copied from\n// https://en.wiktionary.org/wiki/canonical_form:\n//\n// 1. A standard or normal presentation of a mathematical entity [or\n//    object]. A canonical form is an element of a set of representatives\n//    of equivalence classes of forms such that there is a function or\n//    procedure which projects every element of each equivalence class\n//    onto that one element, the canonical form of that equivalence\n//    class. The canonical form is expected to be simpler than the rest of\n//    the forms in some way.\n//\n// That's a long-winded way of saying any two objects that have the same\n// canonical form may be considered equivalent, even if they are !==,\n// which usually means the objects are structurally equivalent (deeply\n// equal), but don't necessarily use the same memory.\n//\n// Like a literary or musical canon, this ObjectCanon class represents a\n// collection of unique canonical items (JavaScript objects), with the\n// important property that canon.admit(a) === canon.admit(b) if a and b\n// are deeply equal to each other. In terms of the definition above, the\n// canon.admit method is the \"function or procedure which projects every\"\n// object \"onto that one element, the canonical form.\"\n//\n// In the worst case, the canonicalization process may involve looking at\n// every property in the provided object tree, so it takes the same order\n// of time as deep equality checking. Fortunately, already-canonicalized\n// objects are returned immediately from canon.admit, so the presence of\n// canonical subtrees tends to speed up canonicalization.\n//\n// Since consumers of canonical objects can check for deep equality in\n// constant time, canonicalizing cache results can massively improve the\n// performance of application code that skips re-rendering unchanged\n// results, such as \"pure\" UI components in a framework like React.\n//\n// Of course, since canonical objects may be shared widely between\n// unrelated consumers, it's important to think of them as immutable, even\n// though they are not actually frozen with Object.freeze in production,\n// due to the extra performance overhead that comes with frozen objects.\n//\n// Custom scalar objects whose internal class name is neither Array nor\n// Object can be included safely in the admitted tree, but they will not\n// be replaced with a canonical version (to put it another way, they are\n// assumed to be canonical already).\n//\n// If we ignore custom objects, no detection of cycles or repeated object\n// references is currently required by the StoreReader class, since\n// GraphQL result objects are JSON-serializable trees (and thus contain\n// neither cycles nor repeated subtrees), so we can avoid the complexity\n// of keeping track of objects we've already seen during the recursion of\n// the admit method.\n//\n// In the future, we may consider adding additional cases to the switch\n// statement to handle other common object types, such as \"[object Date]\"\n// objects, as needed.\nexport class ObjectCanon {\n  // Set of all canonical objects this ObjectCanon has admitted, allowing\n  // canon.admit to return previously-canonicalized objects immediately.\n  private known = new (canUseWeakSet ? WeakSet : Set)<object>();\n\n  // Efficient storage/lookup structure for canonical objects.\n  private pool = new Trie<{\n    array?: any[];\n    object?: Record<string, any>;\n    keys?: SortedKeysInfo;\n  }>(canUseWeakMap);\n\n  public isKnown(value: any): boolean {\n    return isObjectOrArray(value) && this.known.has(value);\n  }\n\n  // Make the ObjectCanon assume this value has already been\n  // canonicalized.\n  private passes = new WeakMap<object, object>();\n  public pass<T>(value: T): T;\n  public pass(value: any) {\n    if (isObjectOrArray(value)) {\n      const copy = shallowCopy(value);\n      this.passes.set(copy, value);\n      return copy;\n    }\n    return value;\n  }\n\n  // Returns the canonical version of value.\n  public admit<T>(value: T): T;\n  public admit(value: any) {\n    if (isObjectOrArray(value)) {\n      const original = this.passes.get(value);\n      if (original) return original;\n\n      const proto = Object.getPrototypeOf(value);\n      switch (proto) {\n        case Array.prototype: {\n          if (this.known.has(value)) return value;\n          const array: any[] = (value as any[]).map(this.admit, this);\n          // Arrays are looked up in the Trie using their recursively\n          // canonicalized elements, and the known version of the array is\n          // preserved as node.array.\n          const node = this.pool.lookupArray(array);\n          if (!node.array) {\n            this.known.add((node.array = array));\n            // Since canonical arrays may be shared widely between\n            // unrelated consumers, it's important to regard them as\n            // immutable, even if they are not frozen in production.\n            if (__DEV__) {\n              Object.freeze(array);\n            }\n          }\n          return node.array;\n        }\n\n        case null:\n        case Object.prototype: {\n          if (this.known.has(value)) return value;\n          const proto = Object.getPrototypeOf(value);\n          const array = [proto];\n          const keys = this.sortedKeys(value);\n          array.push(keys.json);\n          const firstValueIndex = array.length;\n          keys.sorted.forEach((key) => {\n            array.push(this.admit((value as any)[key]));\n          });\n          // Objects are looked up in the Trie by their prototype (which\n          // is *not* recursively canonicalized), followed by a JSON\n          // representation of their (sorted) keys, followed by the\n          // sequence of recursively canonicalized values corresponding to\n          // those keys. To keep the final results unambiguous with other\n          // sequences (such as arrays that just happen to contain [proto,\n          // keys.json, value1, value2, ...]), the known version of the\n          // object is stored as node.object.\n          const node = this.pool.lookupArray(array);\n          if (!node.object) {\n            const obj = (node.object = Object.create(proto));\n            this.known.add(obj);\n            keys.sorted.forEach((key, i) => {\n              obj[key] = array[firstValueIndex + i];\n            });\n            // Since canonical objects may be shared widely between\n            // unrelated consumers, it's important to regard them as\n            // immutable, even if they are not frozen in production.\n            if (__DEV__) {\n              Object.freeze(obj);\n            }\n          }\n          return node.object;\n        }\n      }\n    }\n    return value;\n  }\n\n  // It's worthwhile to cache the sorting of arrays of strings, since the\n  // same initial unsorted arrays tend to be encountered many times.\n  // Fortunately, we can reuse the Trie machinery to look up the sorted\n  // arrays in linear time (which is faster than sorting large arrays).\n  private sortedKeys(obj: object) {\n    const keys = Object.keys(obj);\n    const node = this.pool.lookupArray(keys);\n    if (!node.keys) {\n      keys.sort();\n      const json = JSON.stringify(keys);\n      if (!(node.keys = this.keysByJSON.get(json))) {\n        this.keysByJSON.set(json, (node.keys = { sorted: keys, json }));\n      }\n    }\n    return node.keys;\n  }\n  // Arrays that contain the same elements in a different order can share\n  // the same SortedKeysInfo object, to save memory.\n  private keysByJSON = new Map<string, SortedKeysInfo>();\n\n  // This has to come last because it depends on keysByJSON.\n  public readonly empty = this.admit({});\n}\n\ntype SortedKeysInfo = {\n  sorted: string[];\n  json: string;\n};\n"]}