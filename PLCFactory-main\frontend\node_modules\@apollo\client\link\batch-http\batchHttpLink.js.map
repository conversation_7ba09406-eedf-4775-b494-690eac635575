{"version": 3, "file": "batchHttpLink.js", "sourceRoot": "", "sources": ["../../../src/link/batch-http/batchHttpLink.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EACL,UAAU,EACV,aAAa,EACb,KAAK,EACL,4BAA4B,GAC7B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,OAAO,EACL,uBAAuB,EACvB,SAAS,EACT,yBAAyB,EACzB,YAAY,EACZ,gCAAgC,EAChC,cAAc,EACd,kBAAkB,GACnB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAC9C,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAUhF,IAAM,WAAW,GAAG,KAAK,CAAC,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC,CAAC;AAEvC;;;GAGG;AACH;IAAmC,iCAAU;IAM3C,uBAAY,WAAmC;QAC7C,YAAA,MAAK,WAAE,SAAC;QAER,IAAI,KAaA,WAAW,IAAK,EAA4B,EAZ9C,WAAgB,EAAhB,GAAG,mBAAG,UAAU,KAAA;QAChB,mDAAmD;QAC5C,cAAc,WAAA,EACrB,aAAsB,EAAtB,KAAK,mBAAG,cAAc,KAAA,EACtB,iBAAiB,uBAAA,EACjB,kBAAkB,wBAAA,EAClB,aAAa,mBAAA,EACb,aAAa,mBAAA,EACb,QAAQ,cAAA,EACR,QAAQ,cAAA,EACR,8BAA8B,EAA9B,sBAAsB,mBAAG,KAAK,KAAA,EAC3B,cAAc,cAZf,wJAaH,CAA+C,CAAC;QAEjD,IAAI,OAAO,EAAE,CAAC;YACZ,yEAAyE;YACzE,iDAAiD;YACjD,YAAY,CAAC,cAAc,IAAI,WAAW,CAAC,CAAC;QAC9C,CAAC;QAED,IAAM,UAAU,GAAG;YACjB,IAAI,EAAE,EAAE,iBAAiB,mBAAA,EAAE,kBAAkB,oBAAA,EAAE;YAC/C,OAAO,EAAE,cAAc,CAAC,YAAY;YACpC,WAAW,EAAE,cAAc,CAAC,WAAW;YACvC,OAAO,EAAE,cAAc,CAAC,OAAO;SAChC,CAAC;QAEF,KAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,KAAI,CAAC,aAAa,GAAG,aAAa,IAAI,EAAE,CAAC;QACzC,KAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,EAAE,CAAC;QAE/B,IAAM,YAAY,GAAG,UAAC,UAAuB;YAC3C,IAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAEhD,IAAM,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;YAE3C,IAAM,sBAAsB,GAGxB,EAAE,CAAC;YACP,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;gBACtB,IAAA,KAAoB,OAAO,CAAC,eAAe,EAAzC,MAAI,UAAA,EAAE,OAAO,aAA4B,CAAC;gBAClD,IAAI,MAAI,EAAE,CAAC;oBACT,sBAAsB,CAAC,2BAA2B,CAAC,GAAG,MAAI,CAAC;gBAC7D,CAAC;gBACD,IAAI,OAAO,EAAE,CAAC;oBACZ,sBAAsB,CAAC,8BAA8B,CAAC,GAAG,OAAO,CAAC;gBACnE,CAAC;YACH,CAAC;YAED,IAAM,aAAa,GAAG;gBACpB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,OAAO,EAAE,OAAO,CAAC,YAAY;gBAC7B,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,OAAO,wBAAO,sBAAsB,GAAK,OAAO,CAAC,OAAO,CAAE;aAC3D,CAAC;YAEF,IAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,UAAC,EAAS;oBAAP,KAAK,WAAA;gBACrC,IAAI,aAAa,CAAC,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC;oBACrC,OAAO,4BAA4B,CAAC,KAAK,CAAC,CAAC;gBAC7C,CAAC;gBAED,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAC;YAEH,qEAAqE;YACrE,qEAAqE;YACrE,IAAI,OAAO,CAAC,IAAI,CAAC,UAAC,KAAK,IAAK,OAAA,CAAC,KAAK,EAAN,CAAM,CAAC,EAAE,CAAC;gBACpC,OAAO,SAAS,CACd,IAAI,KAAK,CACP,qMAAqM,CACtM,CACF,CAAC;YACJ,CAAC;YAED,wDAAwD;YACxD,IAAM,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,UAAC,SAAS,EAAE,KAAK;gBAClD,IAAM,MAAM,GAAG,gCAAgC,uBACxC,SAAS,KAAE,KAAK,EAAE,OAAO,CAAC,KAAK,CAAE,KACtC,KAAK,EACL,kBAAkB,EAClB,UAAU,EACV,aAAa,CACd,CAAC;gBAEF,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,sBAAsB,EAAE,CAAC;oBACrD,MAAM,CAAC,IAAI,CAAC,SAAS,GAAG,wBAAwB,CAC9C,MAAM,CAAC,IAAI,CAAC,SAAS,EACrB,SAAS,CAAC,KAAK,CAChB,CAAC;gBACJ,CAAC;gBAED,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC,CAAC;YAEH,IAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,UAAC,EAAQ;oBAAN,IAAI,UAAA;gBAAO,OAAA,IAAI;YAAJ,CAAI,CAAC,CAAC;YACvD,IAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YAEvC,8CAA8C;YAC9C,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;gBAC7B,OAAO,SAAS,CACd,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAClE,CAAC;YACJ,CAAC;YAED,IAAI,CAAC;gBACF,OAAe,CAAC,IAAI,GAAG,uBAAuB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YACzE,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,OAAO,SAAS,CAAgB,UAAU,CAAC,CAAC;YAC9C,CAAC;YAED,IAAI,UAAuC,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;gBAC9D,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;gBACnC,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YACrC,CAAC;YAED,OAAO,IAAI,UAAU,CAAgB,UAAC,QAAQ;gBAC5C,uEAAuE;gBACvE,oEAAoE;gBACpE,wEAAwE;gBACxE,wEAAwE;gBACxE,iEAAiE;gBACjE,cAAc;gBACd,IAAM,YAAY,GAChB,cAAc,IAAI,KAAK,CAAC,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC,IAAI,WAAW,CAAC;gBAEtD,YAAa,CAAC,SAAS,EAAE,OAAO,CAAC;qBAC9B,IAAI,CAAC,UAAC,QAAQ;oBACb,kDAAkD;oBAClD,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS;wBAC3B,OAAA,SAAS,CAAC,UAAU,CAAC,EAAE,QAAQ,UAAA,EAAE,CAAC;oBAAlC,CAAkC,CACnC,CAAC;oBACF,OAAO,QAAQ,CAAC;gBAClB,CAAC,CAAC;qBACD,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;qBAC3C,IAAI,CAAC,UAAC,MAAM;oBACX,UAAU,GAAG,SAAS,CAAC;oBACvB,yDAAyD;oBACzD,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACtB,QAAQ,CAAC,QAAQ,EAAE,CAAC;oBACpB,OAAO,MAAM,CAAC;gBAChB,CAAC,CAAC;qBACD,KAAK,CAAC,UAAC,GAAG;oBACT,UAAU,GAAG,SAAS,CAAC;oBACvB,6DAA6D;oBAC7D,8CAA8C;oBAC9C,sFAAsF;oBACtF,gBAAgB;oBAChB,iFAAiF;oBACjF,WAAW;oBACX,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;wBACvD,6EAA6E;wBAC7E,yBAAyB;wBACzB,uEAAuE;wBACvE,yEAAyE;wBACzE,iEAAiE;wBACjE,wEAAwE;wBACxE,IAAI;wBACJ,mCAAmC;wBACnC,eAAe;wBACf,KAAK;wBACL,kDAAkD;wBAClD,UAAU;wBACV,KAAK;wBACL,IAAI;wBACJ,EAAE;wBACF,8CAA8C;wBAC9C,IAAI;wBACJ,2CAA2C;wBAC3C,eAAe;wBACf,8CAA8C;wBAC9C,gBAAgB;wBAChB,OAAO;wBACP,IAAI;wBACJ,sCAAsC;wBACtC,8EAA8E;wBAC9E,oCAAoC;wBACpC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBAC5B,CAAC;oBAED,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC;gBAEL,OAAO;oBACL,qCAAqC;oBACrC,oEAAoE;oBACpE,IAAI,UAAU;wBAAE,UAAU,CAAC,KAAK,EAAE,CAAC;gBACrC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,QAAQ;YACN,QAAQ;gBACR,CAAC,UAAC,SAAoB;oBACpB,IAAM,OAAO,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;oBAEvC,IAAM,aAAa,GAAG;wBACpB,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,OAAO,EAAE,OAAO,CAAC,YAAY;wBAC7B,WAAW,EAAE,OAAO,CAAC,WAAW;wBAChC,OAAO,EAAE,OAAO,CAAC,OAAO;qBACzB,CAAC;oBAEF,4CAA4C;oBAC5C,OAAO,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;gBACnE,CAAC,CAAC,CAAC;QAEL,KAAI,CAAC,OAAO,GAAG,IAAI,SAAS,CAAC;YAC3B,aAAa,EAAE,KAAI,CAAC,aAAa;YACjC,aAAa,EAAE,KAAI,CAAC,aAAa;YACjC,QAAQ,EAAE,KAAI,CAAC,QAAQ;YACvB,QAAQ,UAAA;YACR,YAAY,cAAA;SACb,CAAC,CAAC;;IACL,CAAC;IAEM,+BAAO,GAAd,UAAe,SAAoB;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACzC,CAAC;IACH,oBAAC;AAAD,CAAC,AAtOD,CAAmC,UAAU,GAsO5C", "sourcesContent": ["import type { Operation, FetchResult } from \"../core/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport {\n  Observable,\n  hasDirectives,\n  maybe,\n  removeClientSetsFromDocument,\n} from \"../../utilities/index.js\";\nimport { fromError } from \"../utils/index.js\";\nimport type { HttpOptions } from \"../http/index.js\";\nimport {\n  serializeFetchParameter,\n  selectURI,\n  parseAndCheckHttpResponse,\n  checkFetcher,\n  selectHttpOptionsAndBodyInternal,\n  defaultPrinter,\n  fallbackHttpConfig,\n} from \"../http/index.js\";\nimport { BatchLink } from \"../batch/index.js\";\nimport { filterOperationVariables } from \"../utils/filterOperationVariables.js\";\n\nexport namespace BatchHttpLink {\n  export type Options = Pick<\n    BatchLink.Options,\n    \"batchMax\" | \"batchDebounce\" | \"batchInterval\" | \"batchKey\"\n  > &\n    Omit<HttpOptions, \"useGETForQueries\">;\n}\n\nconst backupFetch = maybe(() => fetch);\n\n/**\n * Transforms Operation for into HTTP results.\n * context can include the headers property, which will be passed to the fetch function\n */\nexport class BatchHttpLink extends ApolloLink {\n  private batchDebounce?: boolean;\n  private batchInterval: number;\n  private batchMax: number;\n  private batcher: ApolloLink;\n\n  constructor(fetchParams?: BatchHttpLink.Options) {\n    super();\n\n    let {\n      uri = \"/graphql\",\n      // use default global fetch if nothing is passed in\n      fetch: preferredFetch,\n      print = defaultPrinter,\n      includeExtensions,\n      preserveHeaderCase,\n      batchInterval,\n      batchDebounce,\n      batchMax,\n      batchKey,\n      includeUnusedVariables = false,\n      ...requestOptions\n    } = fetchParams || ({} as BatchHttpLink.Options);\n\n    if (__DEV__) {\n      // Make sure at least one of preferredFetch, window.fetch, or backupFetch\n      // is defined, so requests won't fail at runtime.\n      checkFetcher(preferredFetch || backupFetch);\n    }\n\n    const linkConfig = {\n      http: { includeExtensions, preserveHeaderCase },\n      options: requestOptions.fetchOptions,\n      credentials: requestOptions.credentials,\n      headers: requestOptions.headers,\n    };\n\n    this.batchDebounce = batchDebounce;\n    this.batchInterval = batchInterval || 10;\n    this.batchMax = batchMax || 10;\n\n    const batchHandler = (operations: Operation[]) => {\n      const chosenURI = selectURI(operations[0], uri);\n\n      const context = operations[0].getContext();\n\n      const clientAwarenessHeaders: {\n        \"apollographql-client-name\"?: string;\n        \"apollographql-client-version\"?: string;\n      } = {};\n      if (context.clientAwareness) {\n        const { name, version } = context.clientAwareness;\n        if (name) {\n          clientAwarenessHeaders[\"apollographql-client-name\"] = name;\n        }\n        if (version) {\n          clientAwarenessHeaders[\"apollographql-client-version\"] = version;\n        }\n      }\n\n      const contextConfig = {\n        http: context.http,\n        options: context.fetchOptions,\n        credentials: context.credentials,\n        headers: { ...clientAwarenessHeaders, ...context.headers },\n      };\n\n      const queries = operations.map(({ query }) => {\n        if (hasDirectives([\"client\"], query)) {\n          return removeClientSetsFromDocument(query);\n        }\n\n        return query;\n      });\n\n      // If we have a query that returned `null` after removing client-only\n      // fields, it indicates a query that is using all client-only fields.\n      if (queries.some((query) => !query)) {\n        return fromError<FetchResult[]>(\n          new Error(\n            \"BatchHttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or enable the `transformOptions.removeClientFields` option.\"\n          )\n        );\n      }\n\n      //uses fallback, link, and then context to build options\n      const optsAndBody = operations.map((operation, index) => {\n        const result = selectHttpOptionsAndBodyInternal(\n          { ...operation, query: queries[index]! },\n          print,\n          fallbackHttpConfig,\n          linkConfig,\n          contextConfig\n        );\n\n        if (result.body.variables && !includeUnusedVariables) {\n          result.body.variables = filterOperationVariables(\n            result.body.variables,\n            operation.query\n          );\n        }\n\n        return result;\n      });\n\n      const loadedBody = optsAndBody.map(({ body }) => body);\n      const options = optsAndBody[0].options;\n\n      // There's no spec for using GET with batches.\n      if (options.method === \"GET\") {\n        return fromError<FetchResult[]>(\n          new Error(\"apollo-link-batch-http does not support GET requests\")\n        );\n      }\n\n      try {\n        (options as any).body = serializeFetchParameter(loadedBody, \"Payload\");\n      } catch (parseError) {\n        return fromError<FetchResult[]>(parseError);\n      }\n\n      let controller: AbortController | undefined;\n      if (!options.signal && typeof AbortController !== \"undefined\") {\n        controller = new AbortController();\n        options.signal = controller.signal;\n      }\n\n      return new Observable<FetchResult[]>((observer) => {\n        // Prefer BatchHttpLink.Options.fetch (preferredFetch) if provided, and\n        // otherwise fall back to the *current* global window.fetch function\n        // (see issue #7832), or (if all else fails) the backupFetch function we\n        // saved when this module was first evaluated. This last option protects\n        // against the removal of window.fetch, which is unlikely but not\n        // impossible.\n        const currentFetch =\n          preferredFetch || maybe(() => fetch) || backupFetch;\n\n        currentFetch!(chosenURI, options)\n          .then((response) => {\n            // Make the raw response available in the context.\n            operations.forEach((operation) =>\n              operation.setContext({ response })\n            );\n            return response;\n          })\n          .then(parseAndCheckHttpResponse(operations))\n          .then((result) => {\n            controller = undefined;\n            // we have data and can send it to back up the link chain\n            observer.next(result);\n            observer.complete();\n            return result;\n          })\n          .catch((err) => {\n            controller = undefined;\n            // if it is a network error, BUT there is graphql result info\n            // fire the next observer before calling error\n            // this gives apollo-client (and react-apollo) the `graphqlErrors` and `networkErrors`\n            // to pass to UI\n            // this should only happen if we *also* have data as part of the response key per\n            // the spec\n            if (err.result && err.result.errors && err.result.data) {\n              // if we dont' call next, the UI can only show networkError because AC didn't\n              // get andy graphqlErrors\n              // this is graphql execution result info (i.e errors and possibly data)\n              // this is because there is no formal spec how errors should translate to\n              // http status codes. So an auth error (401) could have both data\n              // from a public field, errors from a private field, and a status of 401\n              // {\n              //  user { // this will have errors\n              //    firstName\n              //  }\n              //  products { // this is public so will have data\n              //    cost\n              //  }\n              // }\n              //\n              // the result of above *could* look like this:\n              // {\n              //   data: { products: [{ cost: \"$10\" }] },\n              //   errors: [{\n              //      message: 'your session has timed out',\n              //      path: []\n              //   }]\n              // }\n              // status code of above would be a 401\n              // in the UI you want to show data where you can, errors as data where you can\n              // and use correct http status codes\n              observer.next(err.result);\n            }\n\n            observer.error(err);\n          });\n\n        return () => {\n          // XXX support canceling this request\n          // https://developers.google.com/web/updates/2017/09/abortable-fetch\n          if (controller) controller.abort();\n        };\n      });\n    };\n\n    batchKey =\n      batchKey ||\n      ((operation: Operation) => {\n        const context = operation.getContext();\n\n        const contextConfig = {\n          http: context.http,\n          options: context.fetchOptions,\n          credentials: context.credentials,\n          headers: context.headers,\n        };\n\n        //may throw error if config not serializable\n        return selectURI(operation, uri) + JSON.stringify(contextConfig);\n      });\n\n    this.batcher = new BatchLink({\n      batchDebounce: this.batchDebounce,\n      batchInterval: this.batchInterval,\n      batchMax: this.batchMax,\n      batchKey,\n      batchHandler,\n    });\n  }\n\n  public request(operation: Operation): Observable<FetchResult> | null {\n    return this.batcher.request(operation);\n  }\n}\n"]}