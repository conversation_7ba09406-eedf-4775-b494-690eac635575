package models

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

// ManufacturerType defines the type of PLC manufacturer.
type ManufacturerType string

const (
	ManufacturerAllenBradley ManufacturerType = "allen_bradley"
	ManufacturerSiemens      ManufacturerType = "siemens"
	ManufacturerMitsubishi   ManufacturerType = "mitsubishi"
	ManufacturerSchneider    ManufacturerType = "schneider"
	ManufacturerOmron        ManufacturerType = "omron"
	ManufacturerGeneric      ManufacturerType = "generic"
)

// TagAddressValidationError represents an error that occurs during tag address validation.
type TagAddressValidationError struct {
	Manufacturer ManufacturerType
	Address      string
	DataType     string
	Message      string
}

func (e *TagAddressValidationError) Error() string {
	return fmt.Sprintf("Invalid %s tag address '%s' (type: %s): %s", e.Manufacturer, e.Address, e.DataType, e.Message)
}

// NewTagAddressValidationError creates a new tag address validation error.
func NewTagAddressValidationError(manufacturer ManufacturerType, address, dataType, message string) *TagAddressValidationError {
	return &TagAddressValidationError{
		Manufacturer: manufacturer,
		Address:      address,
		DataType:     dataType,
		Message:      message,
	}
}

// TagAddressValidator defines the interface for validating tag addresses.
type TagAddressValidator interface {
	// ValidateAddress checks if the given address string is valid for the specific manufacturer.
	ValidateAddress(address string, dataType string) error
	
	// ParseAddress parses an address into its components.
	// Returns a map of components (e.g., area, offset, bit) and any error.
	ParseAddress(address string, dataType string) (map[string]interface{}, error)
	
	// GetManufacturer returns the manufacturer type for this validator.
	GetManufacturer() ManufacturerType
	
	// GetSupportedDataTypes returns a list of data types supported by this manufacturer.
	GetSupportedDataTypes() []string
}

// AllenBradleyValidator implements TagAddressValidator for Allen-Bradley PLCs.
type AllenBradleyValidator struct{}

// NewAllenBradleyValidator creates a new Allen-Bradley validator.
func NewAllenBradleyValidator() *AllenBradleyValidator {
	return &AllenBradleyValidator{}
}

// ValidateAddress checks if the given address string is valid for Allen-Bradley PLCs.
func (v *AllenBradleyValidator) ValidateAddress(address string, dataType string) error {
	if address == "" {
		return NewTagAddressValidationError(ManufacturerAllenBradley, address, dataType, "address cannot be empty")
	}
	
	// Check if data type is supported
	supportedTypes := v.GetSupportedDataTypes()
	dataTypeSupported := false
	for _, t := range supportedTypes {
		if strings.EqualFold(t, dataType) {
			dataTypeSupported = true
			break
		}
	}
	if !dataTypeSupported {
		return NewTagAddressValidationError(ManufacturerAllenBradley, address, dataType, 
			fmt.Sprintf("data type not supported (supported types: %s)", strings.Join(supportedTypes, ", ")))
	}
	
	// Regex for valid Allen-Bradley tag names (alphanumeric, underscore, no leading numbers)
	tagPattern := `^[a-zA-Z_][a-zA-Z0-9_]*$`
	programTagPattern := `^Program:[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*$`
	arrayTagPattern := `^[a-zA-Z_][a-zA-Z0-9_]*\[\d+\]$`
	structTagPattern := `^[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*$`
	// Nested structs / multidimensional arrays e.g. Tag[0].Member.Sub[1]
	nestedTagPattern := `^[a-zA-Z_][a-zA-Z0-9_]*(\[[0-9]+\])?(?:\.[a-zA-Z_][a-zA-Z0-9_]*(\[[0-9]+\])?)*$`
	// Indirect addressing e.g. @TagPath or @@TagPath (two @ = external)
	indirectPattern := `^@{1,2}[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*(\[[0-9]+\])?)*$`
	
	// Check for program tags (Program:ProgramName.TagName)
	if strings.HasPrefix(address, "Program:") {
		match, _ := regexp.MatchString(programTagPattern, address)
		if !match {
			return NewTagAddressValidationError(ManufacturerAllenBradley, address, dataType, 
				"invalid program tag format, expected 'Program:ProgramName.TagName'")
		}
		return nil
	}
	
	// Check for array tags (TagName[Index])
	if strings.Contains(address, "[") && strings.Contains(address, "]") {
		match, _ := regexp.MatchString(arrayTagPattern, address)
		if !match {
			return NewTagAddressValidationError(ManufacturerAllenBradley, address, dataType, 
				"invalid array tag format, expected 'TagName[Index]'")
		}
		return nil
	}
	
	// Check for struct tags (TagName.Member)
	if strings.Contains(address, ".") {
		match, _ := regexp.MatchString(structTagPattern, address)
		if !match {
			return NewTagAddressValidationError(ManufacturerAllenBradley, address, dataType, 
				"invalid struct tag format, expected 'TagName.Member'")
		}
		return nil
	}
	
	// Check for simple tags (TagName)
	match, _ := regexp.MatchString(tagPattern, address)
	if !match {
		return NewTagAddressValidationError(ManufacturerAllenBradley, address, dataType, 
			"invalid tag format, must start with letter or underscore and contain only alphanumeric characters and underscores")
	}
	// Allow nested struct / multidimensional array paths
	if ok, _ := regexp.MatchString(nestedTagPattern, address); ok {
		return nil
	}

	// Allow indirect addressing
	if ok, _ := regexp.MatchString(indirectPattern, address); ok {
		return nil
	}

	return nil
}

// ParseAddress parses an Allen-Bradley address into its components.
func (v *AllenBradleyValidator) ParseAddress(address string, dataType string) (map[string]interface{}, error) {
	if err := v.ValidateAddress(address, dataType); err != nil {
		return nil, err
	}
	
	result := make(map[string]interface{})
	
	// Parse program tags (Program:ProgramName.TagName)
	if strings.HasPrefix(address, "Program:") {
		parts := strings.SplitN(address[8:], ".", 2) // Remove "Program:" and split
		if len(parts) == 2 {
			result["programName"] = parts[0]
			result["tagName"] = parts[1]
			result["isProgramTag"] = true
		}
		return result, nil
	}
	
	// Parse array tags (TagName[Index])
	if strings.Contains(address, "[") && strings.Contains(address, "]") {
		re := regexp.MustCompile(`^([a-zA-Z_][a-zA-Z0-9_]*)\[(\d+)\]$`)
		matches := re.FindStringSubmatch(address)
		if len(matches) == 3 {
			result["tagName"] = matches[1]
			index, _ := strconv.Atoi(matches[2])
			result["arrayIndex"] = index
			result["isArrayTag"] = true
		}
		return result, nil
	}
	
	// Parse struct tags (TagName.Member)
	if strings.Contains(address, ".") {
		parts := strings.SplitN(address, ".", 2)
		if len(parts) == 2 {
			result["tagName"] = parts[0]
			result["memberName"] = parts[1]
			result["isStructTag"] = true
		}
		return result, nil
	}
	
	// Simple tag (TagName)
	result["tagName"] = address
	result["isSimpleTag"] = true
	
	return result, nil
}

// GetManufacturer returns the manufacturer type.
func (v *AllenBradleyValidator) GetManufacturer() ManufacturerType {
	return ManufacturerAllenBradley
}

// GetSupportedDataTypes returns a list of data types supported by Allen-Bradley PLCs.
func (v *AllenBradleyValidator) GetSupportedDataTypes() []string {
	return []string{
		"BOOL", "SINT", "INT", "DINT", "LINT", 
		"USINT", "UINT", "UDINT", "ULINT", 
		"REAL", "LREAL", 
		"STRING", "TIMER", "COUNTER", "CONTROL",
	}
}

// SiemensValidator implements TagAddressValidator for Siemens PLCs.
type SiemensValidator struct{}

// NewSiemensValidator creates a new Siemens validator.
func NewSiemensValidator() *SiemensValidator {
	return &SiemensValidator{}
}

// ValidateAddress checks if the given address string is valid for Siemens PLCs.
func (v *SiemensValidator) ValidateAddress(address string, dataType string) error {
	if address == "" {
		return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, "address cannot be empty")
	}
	
	// Check if data type is supported
	supportedTypes := v.GetSupportedDataTypes()
	dataTypeSupported := false
	for _, t := range supportedTypes {
		if strings.EqualFold(t, dataType) {
			dataTypeSupported = true
			break
		}
	}
	if !dataTypeSupported {
		return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, 
			fmt.Sprintf("data type not supported (supported types: %s)", strings.Join(supportedTypes, ", ")))
	}
	
	// Regex for DB addresses: DB<num>.(DBX|DBW|DBD)<offset>.<bit_offset> (bit_offset optional for X)
	dbRegex := regexp.MustCompile(`^DB(\d+)\.(DBX|DBW|DBD)(\d+)(\.(\d+))?$`)
	// Regex for memory area addresses: (M|I|Q)<offset>.<bit_offset> (bit_offset optional)
	areaRegex := regexp.MustCompile(`^(M|I|Q)(\d+)(\.(\d+))?$`)
	
	if dbRegex.MatchString(address) {
		matches := dbRegex.FindStringSubmatch(address)
		dbNum, _ := strconv.Atoi(matches[1])
		memType := matches[2]
		offset, _ := strconv.Atoi(matches[3])
		
		// Validate DB number
		if dbNum < 1 || dbNum > 65535 {
			return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, 
				"DB number must be between 1 and 65535")
		}
		
		// Validate offset
		if offset < 0 || offset > 65535 {
			return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, 
				"offset must be between 0 and 65535")
		}
		
		// Validate bit offset for DBX
		if memType == "DBX" && matches[5] != "" {
			bitOffset, _ := strconv.Atoi(matches[5])
			if bitOffset < 0 || bitOffset > 7 {
				return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, 
					"bit offset must be between 0 and 7")
			}
		}
		
		// Validate data type compatibility with memory type
		switch strings.ToUpper(dataType) {
		case "BOOL":
			if memType != "DBX" || matches[5] == "" {
				return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, 
					"BOOL type requires DBX with bit offset")
			}
		case "BYTE", "CHAR", "SINT", "USINT":
			if memType != "DBX" || matches[5] != "" {
				return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, 
					"BYTE/CHAR/SINT/USINT types require DBX without bit offset")
			}
		case "WORD", "INT", "UINT":
			if memType != "DBW" {
				return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, 
					"WORD/INT/UINT types require DBW")
			}
		case "DWORD", "DINT", "UDINT", "REAL":
			if memType != "DBD" {
				return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, 
					"DWORD/DINT/UDINT/REAL types require DBD")
			}
		}
		
		return nil
	} else if areaRegex.MatchString(address) {
		matches := areaRegex.FindStringSubmatch(address)
		memArea := matches[1]
		offset, _ := strconv.Atoi(matches[2])
		
		// Validate offset
		if offset < 0 || offset > 65535 {
			return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, 
				"offset must be between 0 and 65535")
		}
		
		// Validate bit offset if present
		if matches[4] != "" {
			bitOffset, _ := strconv.Atoi(matches[4])
			if bitOffset < 0 || bitOffset > 7 {
				return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, 
					"bit offset must be between 0 and 7")
			}
		}
		
		// Validate data type compatibility with memory area
		switch strings.ToUpper(dataType) {
		case "BOOL":
			if matches[4] == "" {
				return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, 
					"BOOL type requires bit offset")
			}
		case "BYTE", "CHAR", "SINT", "USINT", "WORD", "INT", "UINT", "DWORD", "DINT", "UDINT", "REAL":
			if matches[4] != "" {
				return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, 
					"numeric types should not have bit offset")
			}
		}
		
		return nil
	}
	
	return NewTagAddressValidationError(ManufacturerSiemens, address, dataType, 
		"address format not recognized, expected DB<num>.(DBX|DBW|DBD)<offset>[.<bit>] or (M|I|Q)<offset>[.<bit>]")
}

// ParseAddress parses a Siemens address into its components.
func (v *SiemensValidator) ParseAddress(address string, dataType string) (map[string]interface{}, error) {
	if err := v.ValidateAddress(address, dataType); err != nil {
		return nil, err
	}
	
	result := make(map[string]interface{})
	
	// Parse DB addresses: DB<num>.(DBX|DBW|DBD)<offset>.<bit_offset>
	dbRegex := regexp.MustCompile(`^DB(\d+)\.(DBX|DBW|DBD)(\d+)(\.(\d+))?$`)
	if dbRegex.MatchString(address) {
		matches := dbRegex.FindStringSubmatch(address)
		dbNum, _ := strconv.Atoi(matches[1])
		memType := matches[2]
		offset, _ := strconv.Atoi(matches[3])
		
		result["area"] = "DB"
		result["dbNumber"] = dbNum
		result["memoryType"] = memType
		result["offset"] = offset
		result["isDB"] = true
		
		if matches[5] != "" {
			bitOffset, _ := strconv.Atoi(matches[5])
			result["bitOffset"] = bitOffset
		}
		
		return result, nil
	}
	
	// Parse memory area addresses: (M|I|Q)<offset>.<bit_offset>
	areaRegex := regexp.MustCompile(`^(M|I|Q)(\d+)(\.(\d+))?$`)
	if areaRegex.MatchString(address) {
		matches := areaRegex.FindStringSubmatch(address)
		memArea := matches[1]
		offset, _ := strconv.Atoi(matches[2])
		
		result["area"] = memArea
		result["offset"] = offset
		
		if matches[4] != "" {
			bitOffset, _ := strconv.Atoi(matches[4])
			result["bitOffset"] = bitOffset
		}
		
		return result, nil
	}
	
	return nil, NewTagAddressValidationError(ManufacturerSiemens, address, dataType, "failed to parse address")
}

// GetManufacturer returns the manufacturer type.
func (v *SiemensValidator) GetManufacturer() ManufacturerType {
	return ManufacturerSiemens
}

// GetSupportedDataTypes returns a list of data types supported by Siemens PLCs.
func (v *SiemensValidator) GetSupportedDataTypes() []string {
	return []string{
		"BOOL", "BYTE", "WORD", "DWORD", 
		"CHAR", "SINT", "INT", "DINT", 
		"USINT", "UINT", "UDINT", 
		"REAL", "STRING",
	}
}

// MitsubishiValidator implements TagAddressValidator for Mitsubishi PLCs.
type MitsubishiValidator struct{}

// NewMitsubishiValidator creates a new Mitsubishi validator.
func NewMitsubishiValidator() *MitsubishiValidator {
	return &MitsubishiValidator{}
}

// ValidateAddress checks if the given address string is valid for Mitsubishi PLCs.
func (v *MitsubishiValidator) ValidateAddress(address string, dataType string) error {
	if address == "" {
		return NewTagAddressValidationError(ManufacturerMitsubishi, address, dataType, "address cannot be empty")
	}
	
	// Check if data type is supported
	supportedTypes := v.GetSupportedDataTypes()
	dataTypeSupported := false
	for _, t := range supportedTypes {
		if strings.EqualFold(t, dataType) {
			dataTypeSupported = true
			break
		}
	}
	if !dataTypeSupported {
		return NewTagAddressValidationError(ManufacturerMitsubishi, address, dataType, 
			fmt.Sprintf("data type not supported (supported types: %s)", strings.Join(supportedTypes, ", ")))
	}
	
	// Regex for bit devices: (X|Y|M|SM|L|F|V|B|SB|DX|DY)[0-9A-F]+
	bitDeviceRegex := regexp.MustCompile(`^(X|Y|M|SM|L|F|V|B|SB|DX|DY)([0-9A-F]+)$`)
	// Regex for word devices: (D|SD|Z|R|ZR|W|SW|TN|CN|STN|SCN|LTN|LCN|LZ|RD)[0-9]+
	wordDeviceRegex := regexp.MustCompile(`^(D|SD|Z|R|ZR|W|SW|TN|CN|STN|SCN|LTN|LCN|LZ|RD)([0-9]+)$`)
	// Regex for indexed bit access: (D|SD|Z|R|ZR|W|SW)[0-9]+\.[0-9]+
	indexedBitRegex := regexp.MustCompile(`^(D|SD|Z|R|ZR|W|SW)([0-9]+)\.([0-9]+)$`)
	
	if bitDeviceRegex.MatchString(address) {
		matches := bitDeviceRegex.FindStringSubmatch(address)
		deviceType := matches[1]
		deviceNumber := matches[2]
		
		// For X and Y, validate hexadecimal format
		if deviceType == "X" || deviceType == "Y" {
			if _, err := strconv.ParseUint(deviceNumber, 16, 32); err != nil {
				return NewTagAddressValidationError(ManufacturerMitsubishi, address, dataType, 
					"X and Y device numbers must be in hexadecimal format")
			}
		} else {
			// For other bit devices, validate decimal format
			if _, err := strconv.Atoi(deviceNumber); err != nil {
				return NewTagAddressValidationError(ManufacturerMitsubishi, address, dataType, 
					"device number must be a valid number")
			}
		}
		
		// Bit devices should be used with BOOL data type
		if strings.ToUpper(dataType) != "BOOL" {
			return NewTagAddressValidationError(ManufacturerMitsubishi, address, dataType, 
				"bit devices should be used with BOOL data type")
		}
		
		return nil
	} else if wordDeviceRegex.MatchString(address) {
		matches := wordDeviceRegex.FindStringSubmatch(address)
		deviceNumber := matches[2]
		
		// Validate device number
		if _, err := strconv.Atoi(deviceNumber); err != nil {
			return NewTagAddressValidationError(ManufacturerMitsubishi, address, dataType, 
				"device number must be a valid number")
			
		}
		
		// Word devices should be used with word data types
		if strings.ToUpper(dataType) == "BOOL" {
			return NewTagAddressValidationError(ManufacturerMitsubishi, address, dataType, 
				"word devices should not be used with BOOL data type, use indexed bit access instead")
		}
		
		return nil
	} else if indexedBitRegex.MatchString(address) {
		matches := indexedBitRegex.FindStringSubmatch(address)
		deviceNumber := matches[2]
		bitIndex := matches[3]
		
		// Validate device number
		if _, err := strconv.Atoi(deviceNumber); err != nil {
			return NewTagAddressValidationError(ManufacturerMitsubishi, address, dataType, 
				"device number must be a valid number")
		}
		
		// Validate bit index (0-15)
		bitIndexInt, err := strconv.Atoi(bitIndex)
		if err != nil || bitIndexInt < 0 || bitIndexInt > 15 {
			return NewTagAddressValidationError(ManufacturerMitsubishi, address, dataType, 
				"bit index must be between 0 and 15")
		}
		
		// Indexed bit access should be used with BOOL data type
		if strings.ToUpper(dataType) != "BOOL" {
			return NewTagAddressValidationError(ManufacturerMitsubishi, address, dataType, 
				"indexed bit access should be used with BOOL data type")
		}
		
		return nil
	}
	
	return NewTagAddressValidationError(ManufacturerMitsubishi, address, dataType, 
		"address format not recognized")
}

// ParseAddress parses a Mitsubishi address into its components.
func (v *MitsubishiValidator) ParseAddress(address string, dataType string) (map[string]interface{}, error) {
	if err := v.ValidateAddress(address, dataType); err != nil {
		return nil, err
	}
	
	result := make(map[string]interface{})
	
	// Parse bit devices: (X|Y|M|SM|L|F|V|B|SB|DX|DY)[0-9A-F]+
	bitDeviceRegex := regexp.MustCompile(`^(X|Y|M|SM|L|F|V|B|SB|DX|DY)([0-9A-F]+)$`)
	if bitDeviceRegex.MatchString(address) {
		matches := bitDeviceRegex.FindStringSubmatch(address)
		deviceType := matches[1]
		deviceNumber := matches[2]
		
		result["deviceType"] = deviceType
		result["isBitDevice"] = true
		
		// For X and Y, parse as hexadecimal
		if deviceType == "X" || deviceType == "Y" {
			number, _ := strconv.ParseUint(deviceNumber, 16, 32)
			result["deviceNumber"] = number
		} else {
			number, _ := strconv.Atoi(deviceNumber)
			result["deviceNumber"] = number
		}
		
		return result, nil
	}
	
	// Parse word devices: (D|SD|Z|R|ZR|W|SW|TN|CN|STN|SCN|LTN|LCN|LZ|RD)[0-9]+
	wordDeviceRegex := regexp.MustCompile(`^(D|SD|Z|R|ZR|W|SW|TN|CN|STN|SCN|LTN|LCN|LZ|RD)([0-9]+)$`)
	if wordDeviceRegex.MatchString(address) {
		matches := wordDeviceRegex.FindStringSubmatch(address)
		deviceType := matches[1]
		deviceNumber, _ := strconv.Atoi(matches[2])
		
		result["deviceType"] = deviceType
		result["deviceNumber"] = deviceNumber
		result["isWordDevice"] = true
		
		return result, nil
	}
	
	// Parse indexed bit access: (D|SD|Z|R|ZR|W|SW)[0-9]+\.[0-9]+
	indexedBitRegex := regexp.MustCompile(`^(D|SD|Z|R|ZR|W|SW)([0-9]+)\.([0-9]+)$`)
	if indexedBitRegex.MatchString(address) {
		matches := indexedBitRegex.FindStringSubmatch(address)
		deviceType := matches[1]
		deviceNumber, _ := strconv.Atoi(matches[2])
		bitIndex, _ := strconv.Atoi(matches[3])
		
		result["deviceType"] = deviceType
		result["deviceNumber"] = deviceNumber
		result["bitIndex"] = bitIndex
		result["isIndexedBit"] = true
		
		return result, nil
	}
	
	return nil, NewTagAddressValidationError(ManufacturerMitsubishi, address, dataType, "failed to parse address")
}

// GetManufacturer returns the manufacturer type.
func (v *MitsubishiValidator) GetManufacturer() ManufacturerType {
	return ManufacturerMitsubishi
}

// GetSupportedDataTypes returns a list of data types supported by Mitsubishi PLCs.
func (v *MitsubishiValidator) GetSupportedDataTypes() []string {
	return []string{
		"BOOL", "WORD", "DWORD", 
		"INT", "DINT", 
		"UINT", "UDINT", 
		"FLOAT", "STRING",
	}
}

// SchneiderValidator implements TagAddressValidator for Schneider Electric PLCs.
type SchneiderValidator struct{}

// NewSchneiderValidator creates a new Schneider Electric validator.
func NewSchneiderValidator() *SchneiderValidator {
	return &SchneiderValidator{}
}

// ValidateAddress checks if the given address string is valid for Schneider Electric PLCs.
func (v *SchneiderValidator) ValidateAddress(address string, dataType string) error {
	if address == "" {
		return NewTagAddressValidationError(ManufacturerSchneider, address, dataType, "address cannot be empty")
	}
	
	// Check if data type is supported
	supportedTypes := v.GetSupportedDataTypes()
	dataTypeSupported := false
	for _, t := range supportedTypes {
		if strings.EqualFold(t, dataType) {
			dataTypeSupported = true
			break
		}
	}
	if !dataTypeSupported {
		return NewTagAddressValidationError(ManufacturerSchneider, address, dataType, 
			fmt.Sprintf("data type not supported (supported types: %s)", strings.Join(supportedTypes, ", ")))
	}
	
	// Regex for Modicon addressing: %<memory_type><size><address>
	// Memory types: M (memory), I (input), Q (output)
	// Sizes: X (bit), B (byte), W (word), D (double word), F (float)
	modiconRegex := regexp.MustCompile(`^%([MIQ])([XBWDF])(\d+)(\.(\d+))?$`)
	
	if modiconRegex.MatchString(address) {
		matches := modiconRegex.FindStringSubmatch(address)
		memoryType := matches[1]
		size := matches[2]
		addressNum, _ := strconv.Atoi(matches[3])
		
		// Validate address number
		if addressNum < 0 {
			return NewTagAddressValidationError(ManufacturerSchneider, address, dataType, 
				"address number must be non-negative")
		}
		
		// For bit access, validate bit index if present
		if size == "X" && matches[5] != "" {
			bitIndex, _ := strconv.Atoi(matches[5])
			if bitIndex < 0 || bitIndex > 15 {
				return NewTagAddressValidationError(ManufacturerSchneider, address, dataType, 
					"bit index must be between 0 and 15")
			}
		}
		
		// Validate size and data type compatibility
		switch size {
		case "X":
			if strings.ToUpper(dataType) != "BOOL" {
				return NewTagAddressValidationError(ManufacturerSchneider, address, dataType, 
					"X (bit) size should be used with BOOL data type")
			}
		case "B":
			if !strings.EqualFold(dataType, "BYTE") && !strings.EqualFold(dataType, "SINT") && !strings.EqualFold(dataType, "USINT") {
				return NewTagAddressValidationError(ManufacturerSchneider, address, dataType, 
					"B (byte) size should be used with BYTE, SINT, or USINT data types")
			}
		case "W":
			if !strings.EqualFold(dataType, "WORD") && !strings.EqualFold(dataType, "INT") && !strings.EqualFold(dataType, "UINT") {
				return NewTagAddressValidationError(ManufacturerSchneider, address, dataType, 
					"W (word) size should be used with WORD, INT, or UINT data types")
			}
		case "D":
			if !strings.EqualFold(dataType, "DWORD") && !strings.EqualFold(dataType, "DINT") && !strings.EqualFold(dataType, "UDINT") {
				return NewTagAddressValidationError(ManufacturerSchneider, address, dataType, 
					"D (double word) size should be used with DWORD, DINT, or UDINT data types")
			}
		case "F":
			if !strings.EqualFold(dataType, "REAL") && !strings.EqualFold(dataType, "FLOAT") {
				return NewTagAddressValidationError(ManufacturerSchneider, address, dataType, 
					"F (float) size should be used with REAL or FLOAT data types")
			}
		}
		
		return nil
	}
	
	return NewTagAddressValidationError(ManufacturerSchneider, address, dataType, 
		"address format not recognized, expected %<memory_type><size><address>[.<bit>]")
}

// ParseAddress parses a Schneider Electric address into its components.
func (v *SchneiderValidator) ParseAddress(address string, dataType string) (map[string]interface{}, error) {
	if err := v.ValidateAddress(address, dataType); err != nil {
		return nil, err
	}
	
	result := make(map[string]interface{})
	
	// Parse Modicon addressing: %<memory_type><size><address>
	modiconRegex := regexp.MustCompile(`^%([MIQ])([XBWDF])(\d+)(\.(\d+))?$`)
	if modiconRegex.MatchString(address) {
		matches := modiconRegex.FindStringSubmatch(address)
		memoryType := matches[1]
		size := matches[2]
		addressNum, _ := strconv.Atoi(matches[3])
		
		result["memoryType"] = memoryType
		result["size"] = size
		result["address"] = addressNum
		
		if matches[5] != "" {
			bitIndex, _ := strconv.Atoi(matches[5])
			result["bitIndex"] = bitIndex
		}
		
		return result, nil
	}
	
	return nil, NewTagAddressValidationError(ManufacturerSchneider, address, dataType, "failed to parse address")
}

// GetManufacturer returns the manufacturer type.
func (v *SchneiderValidator) GetManufacturer() ManufacturerType {
	return ManufacturerSchneider
}

// GetSupportedDataTypes returns a list of data types supported by Schneider Electric PLCs.
func (v *SchneiderValidator) GetSupportedDataTypes() []string {
	return []string{
		"BOOL", "BYTE", "WORD", "DWORD", 
		"SINT", "INT", "DINT", 
		"USINT", "UINT", "UDINT", 
		"REAL", "FLOAT", "STRING",
	}
}

// OmronValidator implements TagAddressValidator for Omron PLCs.
type OmronValidator struct{}

// NewOmronValidator creates a new Omron validator.
func NewOmronValidator() *OmronValidator {
	return &OmronValidator{}
}

// ValidateAddress checks if the given address string is valid for Omron PLCs.
func (v *OmronValidator) ValidateAddress(address string, dataType string) error {
	if address == "" {
		return NewTagAddressValidationError(ManufacturerOmron, address, dataType, "address cannot be empty")
	}
	
	// Check if data type is supported
	supportedTypes := v.GetSupportedDataTypes()
	dataTypeSupported := false
	for _, t := range supportedTypes {
		if strings.EqualFold(t, dataType) {
			dataTypeSupported = true
			break
		}
	}
	if !dataTypeSupported {
		return NewTagAddressValidationError(ManufacturerOmron, address, dataType, 
			fmt.Sprintf("data type not supported (supported types: %s)", strings.Join(supportedTypes, ", ")))
	}
	
	// Regex for CIO area: CIO(\d+)(\.(\d+))?
	cioRegex := regexp.MustCompile(`^CIO(\d+)(\.(\d+))?$`)
	// Regex for Work area: W(\d+)(\.(\d+))?
	workRegex := regexp.MustCompile(`^W(\d+)(\.(\d+))?$`)
	// Regex for Holding area: H(\d+)(\.(\d+))?
	holdingRegex := regexp.MustCompile(`^H(\d+)(\.(\d+))?$`)
	// Regex for Auxiliary area: A(\d+)(\.(\d+))?
	auxRegex := regexp.MustCompile(`^A(\d+)(\.(\d+))?$`)
	// Regex for Data Memory: D(\d+)(\.(\d+))?
	dmRegex := regexp.MustCompile(`^D(\d+)(\.(\d+))?$`)
	// Regex for Timer/Counter: (T|C)(\d+)
	tcRegex := regexp.MustCompile(`^(T|C)(\d+)$`)
	
	var matches []string
	var addressNum int
	var bitIndex int = -1
	var area string
	
	if cioRegex.MatchString(address) {
		matches = cioRegex.FindStringSubmatch(address)
		area = "CIO"
		addressNum, _ = strconv.Atoi(matches[1])
		if matches[3] != "" {
			bitIndex, _ = strconv.Atoi(matches[3])
		}
	} else if workRegex.MatchString(address) {
		matches = workRegex.FindStringSubmatch(address)
		area = "W"
		addressNum, _ = strconv.Atoi(matches[1])
		if matches[3] != "" {
			bitIndex, _ = strconv.Atoi(matches[3])
		}
	} else if holdingRegex.MatchString(address) {
		matches = holdingRegex.FindStringSubmatch(address)
		area = "H"
		addressNum, _ = strconv.Atoi(matches[1])
		if matches[3] != "" {
			bitIndex, _ = strconv.Atoi(matches[3])
		}
	} else if auxRegex.MatchString(address) {
		matches = auxRegex.FindStringSubmatch(address)
		area = "A"
		addressNum, _ = strconv.Atoi(matches[1])
		if matches[3] != "" {
			bitIndex, _ = strconv.Atoi(matches[3])
		}
	} else if dmRegex.MatchString(address) {
		matches = dmRegex.FindStringSubmatch(address)
		area = "D"
		addressNum, _ = strconv.Atoi(matches[1])
		if matches[3] != "" {
			bitIndex, _ = strconv.Atoi(matches[3])
		}
	} else if tcRegex.MatchString(address) {
		matches = tcRegex.FindStringSubmatch(address)
		area = matches[1] // T or C
		addressNum, _ = strconv.Atoi(matches[2])
	} else {
		return NewTagAddressValidationError(ManufacturerOmron, address, dataType, 
			"address format not recognized")
	}
	
	// Validate address range based on area
	switch area {
	case "CIO":
		if addressNum < 0 || addressNum > 6143 {
			return NewTagAddressValidationError(ManufacturerOmron, address, dataType, 
				"CIO address must be between 0 and 6143")
		}
	case "W":
		if addressNum < 0 || addressNum > 511 {
			return NewTagAddressValidationError(ManufacturerOmron, address, dataType, 
				"W address must be between 0 and 511")
		}
	case "H":
		if addressNum < 0 || addressNum > 511 {
			return NewTagAddressValidationError(ManufacturerOmron, address, dataType, 
				"H address must be between 0 and 511")
		}
	case "A":
		if addressNum < 0 || addressNum > 959 {
			return NewTagAddressValidationError(ManufacturerOmron, address, dataType, 
				"A address must be between 0 and 959")
		}
	case "D":
		if addressNum < 0 || addressNum > 32767 {
			return NewTagAddressValidationError(ManufacturerOmron, address, dataType, 
				"D address must be between 0 and 32767")
		}
	case "T", "C":
		if addressNum < 0 || addressNum > 4095 {
			return NewTagAddressValidationError(ManufacturerOmron, address, dataType, 
				"T/C address must be between 0 and 4095")
		}
	}
	
	// Validate bit index if present
	if bitIndex != -1 {
		if bitIndex < 0 || bitIndex > 15 {
			return NewTagAddressValidationError(ManufacturerOmron, address, dataType, 
				"bit index must be between 0 and 15")
		}
		
		// If bit index is specified, data type should be BOOL
		if strings.ToUpper(dataType) != "BOOL" {
			return NewTagAddressValidationError(ManufacturerOmron, address, dataType, 
				"when bit index is specified, data type should be BOOL")
		}
	} else if area != "T" && area != "C" && strings.ToUpper(dataType) == "BOOL" {
		return NewTagAddressValidationError(ManufacturerOmron, address, dataType, 
			"BOOL data type requires a bit index")
	}
	
	// T/C should be used with BOOL data type
	if (area == "T" || area == "C") && strings.ToUpper(dataType) != "BOOL" {
		return NewTagAddressValidationError(ManufacturerOmron, address, dataType, 
			"T/C addresses should be used with BOOL data type")
	}
	
	return nil
}

// ParseAddress parses an Omron address into its components.
func (v *OmronValidator) ParseAddress(address string, dataType string) (map[string]interface{}, error) {
	if err := v.ValidateAddress(address, dataType); err != nil {
		return nil, err
	}
	
	result := make(map[string]interface{})
	
	// Regex for CIO area: CIO(\d+)(\.(\d+))?
	cioRegex := regexp.MustCompile(`^CIO(\d+)(\.(\d+))?$`)
	// Regex for Work area: W(\d+)(\.(\d+))?
	workRegex := regexp.MustCompile(`^W(\d+)(\.(\d+))?$`)
	// Regex for Holding area: H(\d+)(\.(\d+))?
	holdingRegex := regexp.MustCompile(`^H(\d+)(\.(\d+))?$`)
	// Regex for Auxiliary area: A(\d+)(\.(\d+))?
	auxRegex := regexp.MustCompile(`^A(\d+)(\.(\d+))?$`)
	// Regex for Data Memory: D(\d+)(\.(\d+))?
	dmRegex := regexp.MustCompile(`^D(\d+)(\.(\d+))?$`)
	// Regex for Timer/Counter: (T|C)(\d+)
	tcRegex := regexp.MustCompile(`^(T|C)(\d+)$`)
	
	if cioRegex.MatchString(address) {
		matches := cioRegex.FindStringSubmatch(address)
		addressNum, _ := strconv.Atoi(matches[1])
		
		result["area"] = "CIO"
		result["address"] = addressNum
		
		if matches[3] != "" {
			bitIndex, _ := strconv.Atoi(matches[3])
			result["bitIndex"] = bitIndex
		}
	} else if workRegex.MatchString(address) {
		matches := workRegex.FindStringSubmatch(address)
		addressNum, _ := strconv.Atoi(matches[1])
		
		result["area"] = "W"
		result["address"] = addressNum
		
		if matches[3] != "" {
			bitIndex, _ := strconv.Atoi(matches[3])
			result["bitIndex"] = bitIndex
		}
	} else if holdingRegex.MatchString(address) {
		matches := holdingRegex.FindStringSubmatch(address)
		addressNum, _ := strconv.Atoi(matches[1])
		
		result["area"] = "H"
		result["address"] = addressNum
		
		if matches[3] != "" {
			bitIndex, _ := strconv.Atoi(matches[3])
			result["bitIndex"] = bitIndex
		}
	} else if auxRegex.MatchString(address) {
		matches := auxRegex.FindStringSubmatch(address)
		addressNum, _ := strconv.Atoi(matches[1])
		
		result["area"] = "A"
		result["address"] = addressNum
		
		if matches[3] != "" {
			bitIndex, _ := strconv.Atoi(matches[3])
			result["bitIndex"] = bitIndex
		}
	} else if dmRegex.MatchString(address) {
		matches := dmRegex.FindStringSubmatch(address)
		addressNum, _ := strconv.Atoi(matches[1])
		
		result["area"] = "D"
		result["address"] = addressNum
		
		if matches[3] != "" {
			bitIndex, _ := strconv.Atoi(matches[3])
			result["bitIndex"] = bitIndex
		}
	} else if tcRegex.MatchString(address) {
		matches := tcRegex.FindStringSubmatch(address)
		area := matches[1] // T or C
		addressNum, _ := strconv.Atoi(matches[2])
		
		result["area"] = area
		result["address"] = addressNum
	}
	
	return result, nil
}

// GetManufacturer returns the manufacturer type.
func (v *OmronValidator) GetManufacturer() ManufacturerType {
	return ManufacturerOmron
}

// GetSupportedDataTypes returns a list of data types supported by Omron PLCs.
func (v *OmronValidator) GetSupportedDataTypes() []string {
	return []string{
		"BOOL", "WORD", "DWORD", 
		"INT", "DINT", 
		"UINT", "UDINT", 
		"REAL", "STRING",
	}
}

// CreateTagAddressValidator creates a validator for the specified manufacturer.
func CreateTagAddressValidator(manufacturer ManufacturerType) (TagAddressValidator, error) {
	switch manufacturer {
	case ManufacturerAllenBradley:
		return NewAllenBradleyValidator(), nil
	case ManufacturerSiemens:
		return NewSiemensValidator(), nil
	case ManufacturerMitsubishi:
		return NewMitsubishiValidator(), nil
	case ManufacturerSchneider:
		return NewSchneiderValidator(), nil
	case ManufacturerOmron:
		return NewOmronValidator(), nil
	default:
		return nil, fmt.Errorf("unsupported manufacturer: %s", manufacturer)
	}
}
