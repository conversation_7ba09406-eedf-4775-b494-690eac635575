{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/link/error/index.ts"], "names": [], "mappings": ";AAEA,OAAO,EACL,8BAA8B,EAC9B,sBAAsB,GACvB,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAEtD,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAmC9C,MAAM,UAAU,OAAO,CAAC,YAA0B;IAChD,OAAO,IAAI,UAAU,CAAC,UAAC,SAAS,EAAE,OAAO;QACvC,OAAO,IAAI,UAAU,CAAC,UAAC,QAAQ;YAC7B,IAAI,GAAQ,CAAC;YACb,IAAI,UAAe,CAAC;YACpB,IAAI,aAAkB,CAAC;YAEvB,IAAI,CAAC;gBACH,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC;oBACjC,IAAI,EAAE,UAAC,MAAM;wBACX,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;4BAClB,aAAa,GAAG,YAAY,CAAC;gCAC3B,aAAa,EAAE,MAAM,CAAC,MAAM;gCAC5B,QAAQ,EAAE,MAAM;gCAChB,SAAS,WAAA;gCACT,OAAO,SAAA;6BACR,CAAC,CAAC;wBACL,CAAC;6BAAM,IAAI,8BAA8B,CAAC,MAAM,CAAC,EAAE,CAAC;4BAClD,aAAa,GAAG,YAAY,CAAC;gCAC3B,cAAc,EAAE,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC;gCACzD,QAAQ,EAAE,MAAM;gCAChB,SAAS,WAAA;gCACT,OAAO,SAAA;6BACR,CAAC,CAAC;wBACL,CAAC;wBAED,IAAI,aAAa,EAAE,CAAC;4BAClB,UAAU,GAAG,aAAa,CAAC,SAAS,CAAC;gCACnC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;gCAClC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;gCACpC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;6BAC3C,CAAC,CAAC;4BACH,OAAO;wBACT,CAAC;wBAED,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACxB,CAAC;oBACD,KAAK,EAAE,UAAC,YAAY;wBAClB,aAAa,GAAG,YAAY,CAAC;4BAC3B,SAAS,WAAA;4BACT,YAAY,cAAA;4BACZ,+DAA+D;4BAC/D,aAAa,EACX,CAAC,YAAY;gCACX,YAAY,CAAC,MAAM;gCACnB,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;gCAC7B,KAAK,CAAC;4BACR,OAAO,SAAA;yBACR,CAAC,CAAC;wBACH,IAAI,aAAa,EAAE,CAAC;4BAClB,UAAU,GAAG,aAAa,CAAC,SAAS,CAAC;gCACnC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;gCAClC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;gCACpC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;6BAC3C,CAAC,CAAC;4BACH,OAAO;wBACT,CAAC;wBACD,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBAC/B,CAAC;oBACD,QAAQ,EAAE;wBACR,+DAA+D;wBAC/D,yBAAyB;wBACzB,IAAI,CAAC,aAAa,EAAE,CAAC;4BACnB,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;wBACrC,CAAC;oBACH,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,YAAY,CAAC,EAAE,YAAY,EAAE,CAAU,EAAE,SAAS,WAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;gBAC/D,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC;YAED,OAAO;gBACL,IAAI,GAAG;oBAAE,GAAG,CAAC,WAAW,EAAE,CAAC;gBAC3B,IAAI,UAAU;oBAAE,GAAG,CAAC,WAAW,EAAE,CAAC;YACpC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED;IAA+B,6BAAU;IAEvC,mBAAY,YAAoC;QAC9C,YAAA,MAAK,WAAE,SAAC;QACR,KAAI,CAAC,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;;IACpC,CAAC;IAEM,2BAAO,GAAd,UACE,SAAoB,EACpB,OAAiB;QAEjB,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;IACH,gBAAC;AAAD,CAAC,AAbD,CAA+B,UAAU,GAaxC", "sourcesContent": ["import type { FormattedExecutionResult, GraphQLFormattedError } from \"graphql\";\n\nimport {\n  graphQLResultHasProtocolErrors,\n  PROTOCOL_ERRORS_SYMBOL,\n} from \"../../errors/index.js\";\nimport type { NetworkError } from \"../../errors/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport type { Operation, FetchResult, NextLink } from \"../core/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\n\nexport interface ErrorResponse {\n  /**\n   * Errors returned in the `errors` property of the GraphQL response.\n   */\n  graphQLErrors?: ReadonlyArray<GraphQLFormattedError>;\n  /**\n   * Errors thrown during a network request. This is usually an error thrown\n   * during a `fetch` call or an error while parsing the response from the\n   * network.\n   */\n  networkError?: NetworkError;\n  /**\n   * Fatal transport-level errors from multipart subscriptions.\n   * See the [multipart subscription protocol](https://www.apollographql.com/docs/graphos/routing/operations/subscriptions/multipart-protocol#message-and-error-format) for more information.\n   */\n  protocolErrors?: ReadonlyArray<GraphQLFormattedError>;\n  response?: FormattedExecutionResult;\n  operation: Operation;\n  forward: NextLink;\n}\n\nexport namespace ErrorLink {\n  /**\n   * Callback to be triggered when an error occurs within the link stack.\n   */\n  export interface ErrorHandler {\n    (error: ErrorResponse): Observable<FetchResult> | void;\n  }\n}\n\n// For backwards compatibility.\nexport import ErrorHandler = ErrorLink.ErrorHandler;\n\nexport function onError(errorHandler: ErrorHandler): ApolloLink {\n  return new ApolloLink((operation, forward) => {\n    return new Observable((observer) => {\n      let sub: any;\n      let retriedSub: any;\n      let retriedResult: any;\n\n      try {\n        sub = forward(operation).subscribe({\n          next: (result) => {\n            if (result.errors) {\n              retriedResult = errorHandler({\n                graphQLErrors: result.errors,\n                response: result,\n                operation,\n                forward,\n              });\n            } else if (graphQLResultHasProtocolErrors(result)) {\n              retriedResult = errorHandler({\n                protocolErrors: result.extensions[PROTOCOL_ERRORS_SYMBOL],\n                response: result,\n                operation,\n                forward,\n              });\n            }\n\n            if (retriedResult) {\n              retriedSub = retriedResult.subscribe({\n                next: observer.next.bind(observer),\n                error: observer.error.bind(observer),\n                complete: observer.complete.bind(observer),\n              });\n              return;\n            }\n\n            observer.next(result);\n          },\n          error: (networkError) => {\n            retriedResult = errorHandler({\n              operation,\n              networkError,\n              //Network errors can return GraphQL errors on for example a 403\n              graphQLErrors:\n                (networkError &&\n                  networkError.result &&\n                  networkError.result.errors) ||\n                void 0,\n              forward,\n            });\n            if (retriedResult) {\n              retriedSub = retriedResult.subscribe({\n                next: observer.next.bind(observer),\n                error: observer.error.bind(observer),\n                complete: observer.complete.bind(observer),\n              });\n              return;\n            }\n            observer.error(networkError);\n          },\n          complete: () => {\n            // disable the previous sub from calling complete on observable\n            // if retry is in flight.\n            if (!retriedResult) {\n              observer.complete.bind(observer)();\n            }\n          },\n        });\n      } catch (e) {\n        errorHandler({ networkError: e as Error, operation, forward });\n        observer.error(e);\n      }\n\n      return () => {\n        if (sub) sub.unsubscribe();\n        if (retriedSub) sub.unsubscribe();\n      };\n    });\n  });\n}\n\nexport class ErrorLink extends ApolloLink {\n  private link: ApolloLink;\n  constructor(errorHandler: ErrorLink.ErrorHandler) {\n    super();\n    this.link = onError(errorHandler);\n  }\n\n  public request(\n    operation: Operation,\n    forward: NextLink\n  ): Observable<FetchResult> | null {\n    return this.link.request(operation, forward);\n  }\n}\n"]}