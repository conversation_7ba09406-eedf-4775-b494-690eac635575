{"version": 3, "file": "watchQueryOptions.js", "sourceRoot": "", "sources": ["../../src/core/watchQueryOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { DocumentNode } from \"graphql\";\nimport type { TypedDocumentNode } from \"@graphql-typed-document-node/core\";\n\nimport type { FetchResult } from \"../link/core/index.js\";\nimport type {\n  DefaultContext,\n  MutationQueryReducersMap,\n  OperationVariables,\n  MutationUpdaterFunction,\n  OnQueryUpdated,\n  InternalRefetchQueriesInclude,\n} from \"./types.js\";\nimport type { ApolloCache } from \"../cache/index.js\";\nimport type { ObservableQuery } from \"./ObservableQuery.js\";\nimport type { IgnoreModifier } from \"../cache/core/types/common.js\";\nimport type { Unmasked } from \"../masking/index.js\";\nimport type { DeepPartial, NoInfer } from \"../utilities/index.js\";\n\n/**\n * fetchPolicy determines where the client may return a result from. The options are:\n * - cache-first (default): return result from cache. Only fetch from network if cached result is not available.\n * - cache-and-network: return result from cache first (if it exists), then return network result once it's available.\n * - cache-only: return result from cache if available, fail otherwise.\n * - no-cache: return result from network, fail if network call doesn't succeed, don't save to cache\n * - network-only: return result from network, fail if network call doesn't succeed, save to cache\n * - standby: only for queries that aren't actively watched, but should be available for refetch and updateQueries.\n */\nexport type FetchPolicy =\n  | \"cache-first\"\n  | \"network-only\"\n  | \"cache-only\"\n  | \"no-cache\"\n  | \"standby\";\n\nexport type WatchQueryFetchPolicy = FetchPolicy | \"cache-and-network\";\n\nexport type MutationFetchPolicy = Extract<\n  FetchPolicy,\n  | \"network-only\" // default behavior (mutation results written to cache)\n  | \"no-cache\" // alternate behavior (results not written to cache)\n>;\n\nexport type RefetchWritePolicy = \"merge\" | \"overwrite\";\n\n/**\n * errorPolicy determines the level of events for errors in the execution result. The options are:\n * - none (default): any errors from the request are treated like runtime errors and the observable is stopped\n * - ignore: errors from the request do not stop the observable, but also don't call `next`\n * - all: errors are treated like data and will notify observables\n */\nexport type ErrorPolicy = \"none\" | \"ignore\" | \"all\";\n\n/**\n * Query options.\n */\nexport interface QueryOptions<TVariables = OperationVariables, TData = any> {\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#query:member} */\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#variables:member} */\n  variables?: TVariables;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#errorPolicy:member} */\n  errorPolicy?: ErrorPolicy;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n  context?: DefaultContext;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#fetchPolicy:member} */\n  fetchPolicy?: FetchPolicy;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#pollInterval:member} */\n  pollInterval?: number;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#notifyOnNetworkStatusChange:member} */\n  notifyOnNetworkStatusChange?: boolean;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#returnPartialData:member} */\n  returnPartialData?: boolean;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#partialRefetch:member} */\n  partialRefetch?: boolean;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#canonizeResults:member} */\n  canonizeResults?: boolean;\n}\n\n/**\n * Watched query options.\n */\nexport interface WatchQueryOptions<\n  TVariables extends OperationVariables = OperationVariables,\n  TData = any,\n> extends SharedWatchQueryOptions<TVariables, TData> {\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#query:member} */\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n}\n\nexport interface SharedWatchQueryOptions<\n  TVariables extends OperationVariables,\n  TData,\n> {\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#fetchPolicy:member} */\n  fetchPolicy?: WatchQueryFetchPolicy;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#nextFetchPolicy:member} */\n  nextFetchPolicy?:\n    | WatchQueryFetchPolicy\n    | ((\n        this: WatchQueryOptions<TVariables, TData>,\n        currentFetchPolicy: WatchQueryFetchPolicy,\n        context: NextFetchPolicyContext<TData, TVariables>\n      ) => WatchQueryFetchPolicy);\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#initialFetchPolicy:member} */\n  initialFetchPolicy?: WatchQueryFetchPolicy;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#refetchWritePolicy:member} */\n  refetchWritePolicy?: RefetchWritePolicy;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#variables:member} */\n  variables?: TVariables;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#errorPolicy:member} */\n  errorPolicy?: ErrorPolicy;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n  context?: DefaultContext;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#pollInterval:member} */\n  pollInterval?: number;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#notifyOnNetworkStatusChange:member} */\n  notifyOnNetworkStatusChange?: boolean;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#returnPartialData:member} */\n  returnPartialData?: boolean;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#partialRefetch:member} */\n  partialRefetch?: boolean;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#canonizeResults:member} */\n  canonizeResults?: boolean;\n\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#skipPollAttempt:member} */\n  skipPollAttempt?: () => boolean;\n}\n\nexport interface NextFetchPolicyContext<\n  TData,\n  TVariables extends OperationVariables,\n> {\n  reason: \"after-fetch\" | \"variables-changed\";\n  observable: ObservableQuery<TData, TVariables>;\n  options: WatchQueryOptions<TVariables, TData>;\n  initialFetchPolicy: WatchQueryFetchPolicy;\n}\n\nexport interface FetchMoreQueryOptions<TVariables, TData = any> {\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#query:member} */\n  query?: DocumentNode | TypedDocumentNode<TData, TVariables>;\n  /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#variables:member} */\n  variables?: Partial<TVariables>;\n  context?: DefaultContext;\n}\n\n/**\n * @deprecated `UpdateQueryFn` is deprecated and will be removed or updated in a\n * future version of Apollo Client. Use `SubscribeToMoreUpdateQueryFn` instead\n * which provides a more type-safe result.\n */\nexport type UpdateQueryFn<\n  TData = any,\n  TSubscriptionVariables = OperationVariables,\n  TSubscriptionData = TData,\n> = (\n  previousQueryResult: Unmasked<TData>,\n  options: {\n    subscriptionData: { data: Unmasked<TSubscriptionData> };\n    variables?: TSubscriptionVariables;\n  }\n) => Unmasked<TData>;\n\nexport type UpdateQueryOptions<TData, TVariables> = {\n  variables?: TVariables;\n} & (\n  | {\n      /**\n       * Indicate if the previous query result has been found fully in the cache.\n       */\n      complete: true;\n      previousData: Unmasked<TData>;\n    }\n  | {\n      /**\n       * Indicate if the previous query result has not been found fully in the cache.\n       * Might have partial or missing data.\n       */\n      complete: false;\n      previousData: DeepPartial<Unmasked<TData>> | undefined;\n    }\n);\n\nexport interface UpdateQueryMapFn<\n  TData = any,\n  TVariables = OperationVariables,\n> {\n  (\n    /**\n     * @deprecated This value is not type-safe and may contain partial data. This\n     * argument will be removed in the next major version of Apollo Client. Use\n     * `options.previousData` instead for a more type-safe value.\n     */\n    unsafePreviousData: Unmasked<TData>,\n    options: UpdateQueryOptions<TData, TVariables>\n  ): Unmasked<TData> | void;\n}\n\nexport type SubscribeToMoreUpdateQueryFn<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n  TSubscriptionData = TData,\n> = {\n  (\n    /**\n     * @deprecated This value is not type-safe and may contain partial data. This\n     * argument will be removed in the next major version of Apollo Client. Use\n     * `options.previousData` instead for a more type-safe value.\n     */\n    unsafePreviousData: Unmasked<TData>,\n    options: UpdateQueryOptions<TData, TVariables> & {\n      subscriptionData: { data: Unmasked<TSubscriptionData> };\n    }\n  ): Unmasked<TData> | void;\n};\n\nexport interface SubscribeToMoreOptions<\n  TData = any,\n  TSubscriptionVariables extends OperationVariables = OperationVariables,\n  TSubscriptionData = TData,\n  TVariables extends OperationVariables = TSubscriptionVariables,\n> {\n  document:\n    | DocumentNode\n    | TypedDocumentNode<TSubscriptionData, TSubscriptionVariables>;\n  variables?: TSubscriptionVariables;\n  updateQuery?: SubscribeToMoreUpdateQueryFn<\n    TData,\n    TVariables,\n    TSubscriptionData\n  >;\n  onError?: (error: Error) => void;\n  context?: DefaultContext;\n}\n\nexport interface SubscribeToMoreFunction<\n  TData,\n  TVariables extends OperationVariables = OperationVariables,\n> {\n  <\n    TSubscriptionData = TData,\n    TSubscriptionVariables extends OperationVariables = TVariables,\n  >(\n    options: SubscribeToMoreOptions<\n      TData,\n      TSubscriptionVariables,\n      TSubscriptionData,\n      TVariables\n    >\n  ): () => void;\n}\n\nexport interface SubscriptionOptions<\n  TVariables = OperationVariables,\n  TData = any,\n> {\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#query:member} */\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#variables:member} */\n  variables?: TVariables;\n\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#fetchPolicy:member} */\n  fetchPolicy?: FetchPolicy;\n\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#errorPolicy:member} */\n  errorPolicy?: ErrorPolicy;\n\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#context:member} */\n  context?: DefaultContext;\n\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#extensions:member} */\n  extensions?: Record<string, any>;\n}\n\nexport interface MutationBaseOptions<\n  TData = any,\n  TVariables = OperationVariables,\n  TContext = DefaultContext,\n  TCache extends ApolloCache<any> = ApolloCache<any>,\n> {\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#optimisticResponse:member} */\n  optimisticResponse?:\n    | Unmasked<NoInfer<TData>>\n    | ((\n        vars: TVariables,\n        { IGNORE }: { IGNORE: IgnoreModifier }\n      ) => Unmasked<NoInfer<TData>> | IgnoreModifier);\n\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#updateQueries:member} */\n  updateQueries?: MutationQueryReducersMap<TData>;\n\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#refetchQueries:member} */\n  refetchQueries?:\n    | ((result: FetchResult<Unmasked<TData>>) => InternalRefetchQueriesInclude)\n    | InternalRefetchQueriesInclude;\n\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#awaitRefetchQueries:member} */\n  awaitRefetchQueries?: boolean;\n\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#update:member} */\n  update?: MutationUpdaterFunction<TData, TVariables, TContext, TCache>;\n\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#onQueryUpdated:member} */\n  onQueryUpdated?: OnQueryUpdated<any>;\n\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#errorPolicy:member} */\n  errorPolicy?: ErrorPolicy;\n\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#variables:member} */\n  variables?: TVariables;\n\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#context:member} */\n  context?: TContext;\n}\n\nexport interface MutationOptions<\n  TData = any,\n  TVariables = OperationVariables,\n  TContext = DefaultContext,\n  TCache extends ApolloCache<any> = ApolloCache<any>,\n> extends MutationSharedOptions<TData, TVariables, TContext, TCache> {\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#mutation:member} */\n  mutation: DocumentNode | TypedDocumentNode<TData, TVariables>;\n}\nexport interface MutationSharedOptions<\n  TData = any,\n  TVariables = OperationVariables,\n  TContext = DefaultContext,\n  TCache extends ApolloCache<any> = ApolloCache<any>,\n> extends MutationBaseOptions<TData, TVariables, TContext, TCache> {\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#fetchPolicy:member} */\n  fetchPolicy?: MutationFetchPolicy;\n\n  /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#keepRootFields:member} */\n  keepRootFields?: boolean;\n}\n"]}