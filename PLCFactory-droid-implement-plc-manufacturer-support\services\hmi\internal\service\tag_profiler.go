package service

import (
	"context"
	"fmt"
	"hmi/internal/models"
	"hmi/pkg/logger"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

// TagPerformanceMetrics holds performance data for a single tag.
type TagPerformanceMetrics struct {
	TagID             string
	ReadCount         uint64
	WriteCount        uint64
	ValueChangeCount  uint64
	LastReadTime      time.Time
	LastWriteTime     time.Time
	LastChangeTime    time.Time
	AvgReadLatency    time.Duration
	AvgWriteLatency   time.Duration
	PollingInterval   time.Duration // Current polling interval
	ChangeFrequency   float64       // Changes per minute
	UsageScore        float64       // Composite score for prioritization
	MemoryArea        string        // Memory area identifier for grouping
	AddressOffset     int           // Numeric offset within memory area
	GroupID           string        // Assigned group for bulk operations
	ConsecutiveStable int           // Count of consecutive polls with no change
}

// GlobalMetrics holds aggregated performance data for the entire system.
type GlobalMetrics struct {
	TotalReads           uint64
	TotalWrites          uint64
	TotalValueChanges    uint64
	AvgReadLatency       time.Duration
	AvgWriteLatency      time.Duration
	ActiveTags           int
	ActiveSubscribers    int
	CacheHitRate         float64
	PollingEfficiency    float64 // How well polling intervals match actual change rates
	BulkOperationRatio   float64 // Percentage of operations done in bulk
	OptimizationSavings  float64 // Estimated bandwidth/CPU savings from optimizations
	HighFrequencyTags    int     // Number of tags with high change frequency
	MediumFrequencyTags  int     // Number of tags with medium change frequency
	LowFrequencyTags     int     // Number of tags with low change frequency
	AverageBatchSize     float64 // Average number of tags in bulk operations
	LastAnalysisTime     time.Time
	AnalysisDuration     time.Duration
}

// TagGroup represents a group of tags that can be read or written in bulk.
type TagGroup struct {
	GroupID     string
	Tags        []string // Tag IDs
	MemoryArea  string
	StartOffset int
	EndOffset   int
	Size        int // Total size in bytes
	Priority    int // Priority for scheduling (higher = more important)
}

// TagReadEvent represents a single tag read operation.
type TagReadEvent struct {
	TagID     string
	Latency   time.Duration
	Success   bool
	Timestamp time.Time
}

// TagWriteEvent represents a single tag write operation.
type TagWriteEvent struct {
	TagID     string
	Latency   time.Duration
	Success   bool
	Timestamp time.Time
}

// TagChangeEvent represents a change in a tag's value.
type TagChangeEvent struct {
	TagID     string
	OldValue  interface{}
	NewValue  interface{}
	Timestamp time.Time
}

// TagProfiler provides functionality for analyzing tag usage, optimizing polling,
// and providing performance metrics for large tag sets.
type TagProfiler struct {
	tagService TagService
	// Stores performance metrics for each tag
	tagMetrics sync.Map // map[string]*TagPerformanceMetrics
	
	// Stores tag groups for bulk operations
	tagGroups      map[string]*TagGroup
	tagToGroupMap  map[string]string // Maps tagID to groupID
	groupsMutex    sync.RWMutex
	
	// Channels for receiving performance events
	readEventChan   chan TagReadEvent
	writeEventChan  chan TagWriteEvent
	changeEventChan chan TagChangeEvent
	
	// Control channels for background goroutines
	ctx         context.Context
	cancelFunc  context.CancelFunc
	wg          sync.WaitGroup
	
	// Configuration for profiling and optimization
	analysisInterval   time.Duration // How often to run analysis and optimization
	minPollingInterval time.Duration
	maxPollingInterval time.Duration
	
	// Thresholds for frequency classification
	highChangeThreshold  float64 // Changes per minute
	mediumChangeThreshold float64
	
	// Global metrics
	globalMetrics     GlobalMetrics
	globalMetricsMutex sync.RWMutex
	
	// Analysis history for trend detection
	metricsHistory []GlobalMetrics
	historySize    int
}

// NewTagProfiler creates a new TagProfiler instance.
func NewTagProfiler(
	tagService TagService,
	analysisInterval time.Duration,
	minPollingInterval time.Duration,
	maxPollingInterval time.Duration,
) *TagProfiler {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &TagProfiler{
		tagService:         tagService,
		readEventChan:      make(chan TagReadEvent, 1000),
		writeEventChan:     make(chan TagWriteEvent, 1000),
		changeEventChan:    make(chan TagChangeEvent, 1000),
		tagMetrics:         sync.Map{},
		tagGroups:          make(map[string]*TagGroup),
		tagToGroupMap:      make(map[string]string),
		ctx:                ctx,
		cancelFunc:         cancel,
		analysisInterval:   analysisInterval,
		minPollingInterval: minPollingInterval,
		maxPollingInterval: maxPollingInterval,
		highChangeThreshold: 10.0,   // 10 changes per minute
		mediumChangeThreshold: 1.0,  // 1 change per minute
		historySize: 24,             // Keep 24 analysis periods of history
	}
}

// Start begins the background profiling and optimization processes.
func (tp *TagProfiler) Start() {
	logger.Info("Starting TagProfiler...")
	
	// Start goroutine to process incoming events
	tp.wg.Add(1)
	go tp.eventProcessor()
	
	// Start goroutine for periodic analysis and optimization
	tp.wg.Add(1)
	go tp.periodicAnalyzer()
	
	logger.Info("TagProfiler started successfully")
}

// Stop gracefully shuts down the TagProfiler.
func (tp *TagProfiler) Stop() {
	logger.Info("Stopping TagProfiler...")
	tp.cancelFunc() // Signal goroutines to stop
	tp.wg.Wait()    // Wait for all goroutines to finish
	close(tp.readEventChan)
	close(tp.writeEventChan)
	close(tp.changeEventChan)
	logger.Info("TagProfiler stopped.")
}

// RecordReadEvent records a tag read event.
func (tp *TagProfiler) RecordReadEvent(event TagReadEvent) {
	select {
	case tp.readEventChan <- event:
	case <-tp.ctx.Done():
		logger.Warn("Profiler stopped, dropping read event.")
	default:
		logger.Warn("Read event channel full, dropping event.")
	}
}

// RecordWriteEvent records a tag write event.
func (tp *TagProfiler) RecordWriteEvent(event TagWriteEvent) {
	select {
	case tp.writeEventChan <- event:
	case <-tp.ctx.Done():
		logger.Warn("Profiler stopped, dropping write event.")
	default:
		logger.Warn("Write event channel full, dropping event.")
	}
}

// RecordChangeEvent records a tag value change event.
func (tp *TagProfiler) RecordChangeEvent(event TagChangeEvent) {
	select {
	case tp.changeEventChan <- event:
	case <-tp.ctx.Done():
		logger.Warn("Profiler stopped, dropping change event.")
	default:
		logger.Warn("Change event channel full, dropping event.")
	}
}

// eventProcessor processes incoming tag events and updates metrics.
func (tp *TagProfiler) eventProcessor() {
	defer tp.wg.Done()
	
	for {
		select {
		case event := <-tp.readEventChan:
			tp.processReadEvent(event)
		case event := <-tp.writeEventChan:
			tp.processWriteEvent(event)
		case event := <-tp.changeEventChan:
			tp.processChangeEvent(event)
		case <-tp.ctx.Done():
			logger.Info("Event processor stopping.")
			return
		}
	}
}

// processReadEvent processes a tag read event and updates metrics.
func (tp *TagProfiler) processReadEvent(event TagReadEvent) {
	metrics, _ := tp.tagMetrics.LoadOrStore(event.TagID, &TagPerformanceMetrics{TagID: event.TagID})
	m := metrics.(*TagPerformanceMetrics)
	
	m.ReadCount++
	m.LastReadTime = event.Timestamp
	
	// Update read latency with exponential moving average (weight recent values more)
	if m.ReadCount == 1 {
		m.AvgReadLatency = event.Latency
	} else {
		m.AvgReadLatency = (m.AvgReadLatency*9 + event.Latency) / 10
	}
	
	// Update global metrics
	tp.globalMetricsMutex.Lock()
	tp.globalMetrics.TotalReads++
	tp.globalMetricsMutex.Unlock()
}

// processWriteEvent processes a tag write event and updates metrics.
func (tp *TagProfiler) processWriteEvent(event TagWriteEvent) {
	metrics, _ := tp.tagMetrics.LoadOrStore(event.TagID, &TagPerformanceMetrics{TagID: event.TagID})
	m := metrics.(*TagPerformanceMetrics)
	
	m.WriteCount++
	m.LastWriteTime = event.Timestamp
	
	// Update write latency with exponential moving average
	if m.WriteCount == 1 {
		m.AvgWriteLatency = event.Latency
	} else {
		m.AvgWriteLatency = (m.AvgWriteLatency*9 + event.Latency) / 10
	}
	
	// Update global metrics
	tp.globalMetricsMutex.Lock()
	tp.globalMetrics.TotalWrites++
	tp.globalMetricsMutex.Unlock()
}

// processChangeEvent processes a tag value change event and updates metrics.
func (tp *TagProfiler) processChangeEvent(event TagChangeEvent) {
	metrics, _ := tp.tagMetrics.LoadOrStore(event.TagID, &TagPerformanceMetrics{TagID: event.TagID})
	m := metrics.(*TagPerformanceMetrics)
	
	m.ValueChangeCount++
	m.ConsecutiveStable = 0 // Reset stable count on change
	m.LastChangeTime = event.Timestamp
	
	// Update global metrics
	tp.globalMetricsMutex.Lock()
	tp.globalMetrics.TotalValueChanges++
	tp.globalMetricsMutex.Unlock()
}

// periodicAnalyzer runs analysis and optimization periodically.
func (tp *TagProfiler) periodicAnalyzer() {
	defer tp.wg.Done()
	ticker := time.NewTicker(tp.analysisInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			startTime := time.Now()
			tp.analyzeAndOptimize()
			duration := time.Since(startTime)
			
			// Update global metrics
			tp.globalMetricsMutex.Lock()
			tp.globalMetrics.LastAnalysisTime = startTime
			tp.globalMetrics.AnalysisDuration = duration
			tp.globalMetricsMutex.Unlock()
			
			logger.Infof("Tag analysis and optimization completed in %v", duration)
		case <-tp.ctx.Done():
			logger.Info("Periodic analyzer stopping.")
			return
		}
	}
}

// analyzeAndOptimize performs comprehensive analysis and applies optimizations.
func (tp *TagProfiler) analyzeAndOptimize() {
	logger.Debug("Running periodic tag analysis and optimization...")
	
	// 1. Analyze tag usage patterns
	tp.analyzeTagUsagePatterns()
	
	// 2. Optimize polling frequencies based on change patterns
	tp.optimizePollingFrequencies()
	
	// 3. Group tags for bulk operations
	tp.groupTagsForBulkOperations()
	
	// 4. Update global metrics
	tp.updateGlobalMetrics()
	
	// 5. Store metrics history for trend analysis
	tp.storeMetricsHistory()
}

// analyzeTagUsagePatterns analyzes tag usage patterns and calculates metrics.
func (tp *TagProfiler) analyzeTagUsagePatterns() {
	now := time.Now()
	minutesSinceLastAnalysis := tp.analysisInterval.Minutes()
	
	tp.tagMetrics.Range(func(key, value interface{}) bool {
		tagID := key.(string)
		metrics := value.(*TagPerformanceMetrics)
		
		// Calculate change frequency (changes per minute)
		if metrics.ValueChangeCount > 0 {
			metrics.ChangeFrequency = float64(metrics.ValueChangeCount) / minutesSinceLastAnalysis
		} else {
			// If no changes, calculate based on time since last change
			if !metrics.LastChangeTime.IsZero() {
				minutesSinceLastChange := now.Sub(metrics.LastChangeTime).Minutes()
				if minutesSinceLastChange > 0 {
					metrics.ChangeFrequency = 1.0 / minutesSinceLastChange // Estimate
				}
			}
		}
		
		// Calculate usage score (composite metric for prioritization)
		readWeight := 1.0
		writeWeight := 2.0
		changeWeight := 3.0
		
		readScore := float64(metrics.ReadCount) / minutesSinceLastAnalysis * readWeight
		writeScore := float64(metrics.WriteCount) / minutesSinceLastAnalysis * writeWeight
		changeScore := metrics.ChangeFrequency * changeWeight
		
		metrics.UsageScore = readScore + writeScore + changeScore
		
		// Parse memory area and address offset from tag address
		// This is a simplified example - in reality, you'd use the tag validators
		if tag, ok := tp.tagService.GetTagDefinition(tagID); ok {
			areaInfo := parseMemoryAreaFromAddress(tag.Address)
			metrics.MemoryArea = areaInfo.area
			metrics.AddressOffset = areaInfo.offset
		}
		
		// Update consecutive stable count if no changes
		if metrics.ValueChangeCount == 0 {
			metrics.ConsecutiveStable++
		}
		
		return true
	})
}

// optimizePollingFrequencies adjusts polling intervals based on tag activity.
func (tp *TagProfiler) optimizePollingFrequencies() {
	highActivity := make([]*TagPerformanceMetrics, 0)
	mediumActivity := make([]*TagPerformanceMetrics, 0)
	lowActivity := make([]*TagPerformanceMetrics, 0)
	
	// Categorize tags by activity level
	tp.tagMetrics.Range(func(key, value interface{}) bool {
		metrics := value.(*TagPerformanceMetrics)
		
		if metrics.ChangeFrequency >= tp.highChangeThreshold {
			highActivity = append(highActivity, metrics)
		} else if metrics.ChangeFrequency >= tp.mediumChangeThreshold {
			mediumActivity = append(mediumActivity, metrics)
		} else {
			lowActivity = append(lowActivity, metrics)
		}
		
		return true
	})
	
	// Update global metrics counters
	tp.globalMetricsMutex.Lock()
	tp.globalMetrics.HighFrequencyTags = len(highActivity)
	tp.globalMetrics.MediumFrequencyTags = len(mediumActivity)
	tp.globalMetrics.LowFrequencyTags = len(lowActivity)
	tp.globalMetricsMutex.Unlock()
	
	// Apply polling interval adjustments
	for _, metrics := range highActivity {
		newInterval := tp.minPollingInterval
		tp.updateTagPollingInterval(metrics.TagID, newInterval)
		metrics.PollingInterval = newInterval
	}
	
	for _, metrics := range mediumActivity {
		// Scale between min and max based on exact change frequency
		scaleFactor := (tp.highChangeThreshold - metrics.ChangeFrequency) / 
			(tp.highChangeThreshold - tp.mediumChangeThreshold)
		
		// Clamp between 0 and 1
		if scaleFactor < 0 {
			scaleFactor = 0
		} else if scaleFactor > 1 {
			scaleFactor = 1
		}
		
		// Calculate interval - closer to min for higher frequency
		intervalRange := tp.maxPollingInterval - tp.minPollingInterval
		newInterval := tp.minPollingInterval + time.Duration(float64(intervalRange) * scaleFactor * 0.5)
		
		tp.updateTagPollingInterval(metrics.TagID, newInterval)
		metrics.PollingInterval = newInterval
	}
	
	for _, metrics := range lowActivity {
		// For very stable tags (many consecutive polls with no change), 
		// gradually increase interval up to max
		var newInterval time.Duration
		
		if metrics.ConsecutiveStable > 10 {
			newInterval = tp.maxPollingInterval
		} else {
			// Scale based on consecutive stable count
			scaleFactor := float64(metrics.ConsecutiveStable) / 10.0
			if scaleFactor > 1 {
				scaleFactor = 1
			}
			
			intervalRange := tp.maxPollingInterval - tp.minPollingInterval
			newInterval = tp.minPollingInterval + time.Duration(float64(intervalRange) * scaleFactor)
		}
		
		tp.updateTagPollingInterval(metrics.TagID, newInterval)
		metrics.PollingInterval = newInterval
	}
	
	logger.Infof("Optimized polling frequencies - High: %d, Medium: %d, Low: %d tags", 
		len(highActivity), len(mediumActivity), len(lowActivity))
}

// updateTagPollingInterval updates the polling interval for a tag in the TagService.
func (tp *TagProfiler) updateTagPollingInterval(tagID string, interval time.Duration) {
	// This would call a method on TagService to update the polling interval
	// For now, just log the change
	logger.Debugf("Setting polling interval for tag %s to %v", tagID, interval)
	
	// In a real implementation, you would call something like:
	// tp.tagService.SetTagPollingInterval(tagID, interval)
}

// groupTagsForBulkOperations identifies groups of tags that can be read/written in bulk.
func (tp *TagProfiler) groupTagsForBulkOperations() {
	// Lock for modifying groups
	tp.groupsMutex.Lock()
	defer tp.groupsMutex.Unlock()
	
	// Clear existing groups
	tp.tagGroups = make(map[string]*TagGroup)
	tp.tagToGroupMap = make(map[string]string)
	
	// Group tags by memory area
	areaGroups := make(map[string][]*TagPerformanceMetrics)
	
	tp.tagMetrics.Range(func(key, value interface{}) bool {
		metrics := value.(*TagPerformanceMetrics)
		
		if metrics.MemoryArea != "" {
			if _, ok := areaGroups[metrics.MemoryArea]; !ok {
				areaGroups[metrics.MemoryArea] = make([]*TagPerformanceMetrics, 0)
			}
			areaGroups[metrics.MemoryArea] = append(areaGroups[metrics.MemoryArea], metrics)
		}
		
		return true
	})
	
	// For each memory area, sort by address and create contiguous groups
	groupCounter := 0
	
	for area, tags := range areaGroups {
		// Sort tags by address offset
		sort.Slice(tags, func(i, j int) bool {
			return tags[i].AddressOffset < tags[j].AddressOffset
		})
		
		// Find contiguous blocks (simplified algorithm)
		currentGroup := make([]*TagPerformanceMetrics, 0)
		startOffset := -1
		endOffset := -1
		
		for i, tag := range tags {
			if startOffset == -1 {
				// Start a new group
				startOffset = tag.AddressOffset
				endOffset = tag.AddressOffset + getTagSize(tag.TagID)
				currentGroup = append(currentGroup, tag)
			} else if tag.AddressOffset <= endOffset + 16 { // Allow small gaps (16 bytes)
				// Extend current group
				endOffset = tag.AddressOffset + getTagSize(tag.TagID)
				currentGroup = append(currentGroup, tag)
			} else {
				// Gap too large, finalize current group and start a new one
				if len(currentGroup) > 1 {
					createTagGroup(tp, area, currentGroup, groupCounter)
					groupCounter++
				}
				
				// Start a new group
				startOffset = tag.AddressOffset
				endOffset = tag.AddressOffset + getTagSize(tag.TagID)
				currentGroup = []*TagPerformanceMetrics{tag}
			}
			
			// Handle the last group
			if i == len(tags)-1 && len(currentGroup) > 1 {
				createTagGroup(tp, area, currentGroup, groupCounter)
				groupCounter++
			}
		}
	}
	
	logger.Infof("Created %d tag groups for bulk operations", len(tp.tagGroups))
}

// createTagGroup creates a new tag group and adds it to the profiler.
func createTagGroup(tp *TagProfiler, area string, tags []*TagPerformanceMetrics, counter int) {
	// Calculate group priority based on average usage score
	totalScore := 0.0
	for _, tag := range tags {
		totalScore += tag.UsageScore
	}
	avgScore := totalScore / float64(len(tags))
	
	// Find min and max offsets
	minOffset := tags[0].AddressOffset
	maxOffset := tags[0].AddressOffset
	for _, tag := range tags {
		if tag.AddressOffset < minOffset {
			minOffset = tag.AddressOffset
		}
		if tag.AddressOffset+getTagSize(tag.TagID) > maxOffset {
			maxOffset = tag.AddressOffset + getTagSize(tag.TagID)
		}
	}
	
	// Create group
	groupID := fmt.Sprintf("group_%s_%d", area, counter)
	tagIDs := make([]string, len(tags))
	for i, tag := range tags {
		tagIDs[i] = tag.TagID
		tag.GroupID = groupID
		tp.tagToGroupMap[tag.TagID] = groupID
	}
	
	tp.tagGroups[groupID] = &TagGroup{
		GroupID:     groupID,
		Tags:        tagIDs,
		MemoryArea:  area,
		StartOffset: minOffset,
		EndOffset:   maxOffset,
		Size:        maxOffset - minOffset,
		Priority:    int(avgScore * 100), // Scale for integer priority
	}
}

// getTagSize returns the size in bytes for a tag.
// This is a simplified function - in reality, you'd get this from the tag definition.
func getTagSize(tagID string) int {
	// Default size of 4 bytes (e.g., for DINT, REAL)
	return 4
}

// updateGlobalMetrics calculates and updates the global metrics.
func (tp *TagProfiler) updateGlobalMetrics() {
	tp.globalMetricsMutex.Lock()
	defer tp.globalMetricsMutex.Unlock()
	
	// Count active tags
	var activeTags int
	tp.tagMetrics.Range(func(_, _ interface{}) bool {
		activeTags++
		return true
	})
	tp.globalMetrics.ActiveTags = activeTags
	
	// Calculate average batch size
	if len(tp.tagGroups) > 0 {
		totalTags := 0
		for _, group := range tp.tagGroups {
			totalTags += len(group.Tags)
		}
		tp.globalMetrics.AverageBatchSize = float64(totalTags) / float64(len(tp.tagGroups))
	}
	
	// Calculate bulk operation ratio
	// This is an estimate based on how many tags are in groups vs. total tags
	tagsInGroups := 0
	tp.groupsMutex.RLock()
	for _, group := range tp.tagGroups {
		tagsInGroups += len(group.Tags)
	}
	tp.groupsMutex.RUnlock()
	
	if activeTags > 0 {
		tp.globalMetrics.BulkOperationRatio = float64(tagsInGroups) / float64(activeTags)
	}
	
	// Calculate polling efficiency
	// Higher is better - means we're polling at appropriate intervals
	var totalEfficiency float64
	var tagCount int
	
	tp.tagMetrics.Range(func(_, value interface{}) bool {
		metrics := value.(*TagPerformanceMetrics)
		
		// Calculate individual tag efficiency
		var efficiency float64
		
		if metrics.PollingInterval > 0 && metrics.ChangeFrequency > 0 {
			// Ideal polling interval would be slightly faster than change frequency
			idealInterval := time.Minute / time.Duration(metrics.ChangeFrequency*1.5)
			
			// Bound ideal interval to min/max
			if idealInterval < tp.minPollingInterval {
				idealInterval = tp.minPollingInterval
			} else if idealInterval > tp.maxPollingInterval {
				idealInterval = tp.maxPollingInterval
			}
			
			// Calculate ratio between actual and ideal (1.0 is perfect)
			ratio := metrics.PollingInterval.Seconds() / idealInterval.Seconds()
			
			// Invert if we're polling too slowly
			if ratio > 1.0 {
				ratio = 1.0 / ratio
			}
			
			efficiency = ratio
		} else {
			efficiency = 0.5 // Default middle value when we don't have enough data
		}
		
		totalEfficiency += efficiency
		tagCount++
		
		return true
	})
	
	if tagCount > 0 {
		tp.globalMetrics.PollingEfficiency = totalEfficiency / float64(tagCount)
	}
	
	// Calculate optimization savings
	// This is an estimate of bandwidth/CPU saved compared to polling all tags at min interval
	if activeTags > 0 {
		var totalIntervalRatio float64
		
		tp.tagMetrics.Range(func(_, value interface{}) bool {
			metrics := value.(*TagPerformanceMetrics)
			
			if metrics.PollingInterval > 0 {
				// Calculate ratio between actual interval and min interval
				ratio := metrics.PollingInterval.Seconds() / tp.minPollingInterval.Seconds()
				totalIntervalRatio += ratio
			} else {
				totalIntervalRatio += 1.0 // Default to 1.0 (no savings)
			}
			
			return true
		})
		
		// Calculate average interval ratio
		avgIntervalRatio := totalIntervalRatio / float64(activeTags)
		
		// Savings is how much less frequently we poll compared to min interval
		if avgIntervalRatio > 1.0 {
			tp.globalMetrics.OptimizationSavings = 1.0 - (1.0 / avgIntervalRatio)
		} else {
			tp.globalMetrics.OptimizationSavings = 0.0
		}
	}
}

// storeMetricsHistory stores the current metrics for trend analysis.
func (tp *TagProfiler) storeMetricsHistory() {
	tp.globalMetricsMutex.RLock()
	metricsCopy := tp.globalMetrics // Make a copy
	tp.globalMetricsMutex.RUnlock()
	
	// Add to history
	tp.metricsHistory = append(tp.metricsHistory, metricsCopy)
	
	// Trim history if it's too long
	if len(tp.metricsHistory) > tp.historySize {
		tp.metricsHistory = tp.metricsHistory[1:]
	}
}

// GetGlobalMetrics returns the current global metrics.
func (tp *TagProfiler) GetGlobalMetrics() GlobalMetrics {
	tp.globalMetricsMutex.RLock()
	defer tp.globalMetricsMutex.RUnlock()
	
	// Return a copy to avoid concurrent modification
	return tp.globalMetrics
}

// GetTagMetrics returns performance metrics for a specific tag.
func (tp *TagProfiler) GetTagMetrics(tagID string) *TagPerformanceMetrics {
	if metrics, ok := tp.tagMetrics.Load(tagID); ok {
		return metrics.(*TagPerformanceMetrics)
	}
	return nil
}

// GetTagGroups returns all tag groups for bulk operations.
func (tp *TagProfiler) GetTagGroups() []*TagGroup {
	tp.groupsMutex.RLock()
	defer tp.groupsMutex.RUnlock()
	
	groups := make([]*TagGroup, 0, len(tp.tagGroups))
	for _, group := range tp.tagGroups {
		groups = append(groups, group)
	}
	
	return groups
}

// GetTagGroup returns the group that a tag belongs to, if any.
func (tp *TagProfiler) GetTagGroup(tagID string) *TagGroup {
	tp.groupsMutex.RLock()
	defer tp.groupsMutex.RUnlock()
	
	if groupID, ok := tp.tagToGroupMap[tagID]; ok {
		return tp.tagGroups[groupID]
	}
	return nil
}

// GetMetricsHistory returns the stored metrics history.
func (tp *TagProfiler) GetMetricsHistory() []GlobalMetrics {
	// Return a copy to avoid concurrent modification
	history := make([]GlobalMetrics, len(tp.metricsHistory))
	copy(history, tp.metricsHistory)
	return history
}

// GeneratePerformanceReport generates a comprehensive performance report.
func (tp *TagProfiler) GeneratePerformanceReport() map[string]interface{} {
	tp.globalMetricsMutex.RLock()
	metrics := tp.globalMetrics
	tp.globalMetricsMutex.RUnlock()
	
	// Get top tags by usage score
	type tagScore struct {
		TagID string
		Score float64
	}
	
	topTags := make([]tagScore, 0)
	tp.tagMetrics.Range(func(key, value interface{}) bool {
		tagID := key.(string)
		metrics := value.(*TagPerformanceMetrics)
		
		topTags = append(topTags, tagScore{
			TagID: tagID,
			Score: metrics.UsageScore,
		})
		
		return true
	})
	
	// Sort by score descending
	sort.Slice(topTags, func(i, j int) bool {
		return topTags[i].Score > topTags[j].Score
	})
	
	// Limit to top 10
	if len(topTags) > 10 {
		topTags = topTags[:10]
	}
	
	// Convert to map of tag IDs
	topTagIDs := make([]string, len(topTags))
	for i, tag := range topTags {
		topTagIDs[i] = tag.TagID
	}
	
	// Build report
	report := map[string]interface{}{
		"timestamp":            time.Now(),
		"activeTags":           metrics.ActiveTags,
		"totalReads":           metrics.TotalReads,
		"totalWrites":          metrics.TotalWrites,
		"totalValueChanges":    metrics.TotalValueChanges,
		"avgReadLatency":       metrics.AvgReadLatency.String(),
		"avgWriteLatency":      metrics.AvgWriteLatency.String(),
		"pollingEfficiency":    metrics.PollingEfficiency,
		"bulkOperationRatio":   metrics.BulkOperationRatio,
		"optimizationSavings":  metrics.OptimizationSavings,
		"highFrequencyTags":    metrics.HighFrequencyTags,
		"mediumFrequencyTags":  metrics.MediumFrequencyTags,
		"lowFrequencyTags":     metrics.LowFrequencyTags,
		"averageBatchSize":     metrics.AverageBatchSize,
		"lastAnalysisTime":     metrics.LastAnalysisTime,
		"analysisDuration":     metrics.AnalysisDuration.String(),
		"topTags":              topTagIDs,
		"tagGroups":            len(tp.tagGroups),
	}
	
	return report
}

// MemoryAreaInfo holds parsed information about a tag's memory area.
type MemoryAreaInfo struct {
	area   string
	offset int
}

// parseMemoryAreaFromAddress parses a tag address to extract memory area and offset.
// This is a simplified implementation - in reality, you would use the tag validators.
func parseMemoryAreaFromAddress(address string) MemoryAreaInfo {
	info := MemoryAreaInfo{
		area:   "unknown",
		offset: 0,
	}
	
	// Simple parsing for common PLC address formats
	
	// Allen-Bradley format: Program:MainProgram.Tag
	if strings.Contains(address, "Program:") {
		parts := strings.Split(address, ".")
		if len(parts) > 0 {
			info.area = parts[0]
		}
		return info
	}
	
	// Siemens format: DB1.DBX0.0
	if strings.HasPrefix(address, "DB") {
		dbParts := strings.Split(address, ".")
		if len(dbParts) > 0 {
			info.area = dbParts[0]
			
			// Try to extract offset
			if len(dbParts) > 1 {
				offsetStr := strings.TrimPrefix(dbParts[1], "DBX")
				offsetStr = strings.TrimPrefix(offsetStr, "DBW")
				offsetStr = strings.TrimPrefix(offsetStr, "DBD")
				
				if offset, err := strconv.Atoi(offsetStr); err == nil {
					info.offset = offset
				}
			}
		}
		return info
	}
	
	// Modbus format: %MW100
	if strings.HasPrefix(address, "%M") {
		info.area = address[:3] // %MW, %MD, etc.
		
		// Try to extract offset
		offsetStr := address[3:]
		if offset, err := strconv.Atoi(offsetStr); err == nil {
			info.offset = offset
		}
		return info
	}
	
	// Generic format - try to find numeric part for offset
	re := regexp.MustCompile(`([A-Za-z]+)(\d+)`)
	matches := re.FindStringSubmatch(address)
	if len(matches) >= 3 {
		info.area = matches[1]
		if offset, err := strconv.Atoi(matches[2]); err == nil {
			info.offset = offset
		}
	}
	
	return info
}
