import {
  AutoCleanedStrongCache,
  AutoCleanedWeakCache,
  Concast,
  DEV,
  DeepMerger,
  DocumentTransform,
  Observable,
  addNonReactiveToNamedFragments,
  addTypenameToDocument,
  argumentsObjectFromField,
  asyncMap,
  buildQueryFromSelectionSet,
  cacheSizes,
  canUseAsyncIteratorSymbol,
  canUseDOM,
  canUseLayoutEffect,
  canUseSymbol,
  canUseWeakMap,
  canUseWeakSet,
  canonicalStringify,
  checkDocument,
  cloneDeep,
  compact,
  concatPagination,
  createFragmentMap,
  createFulfilledPromise,
  createRejectedPromise,
  fixObservableSubclass,
  getDefaultValues,
  getDirectiveNames,
  getFragmentDefinition,
  getFragmentDefinitions,
  getFragmentFromSelection,
  getFragmentMaskMode,
  getFragmentQueryDocument,
  getGraphQLErrorsFromResult,
  getInclusionDirectives,
  getMainDefinition,
  getOperationDefinition,
  getOperationName,
  getQueryDefinition,
  getStoreKeyName,
  getTypenameFromResult,
  graphQLResultHasError,
  hasAllDirectives,
  hasAnyDirectives,
  hasClientExports,
  hasDirectives,
  isApolloPayloadResult,
  isArray,
  isDocumentNode,
  isExecutionPatchIncrementalResult,
  isExecutionPatchInitialResult,
  isExecutionPatchResult,
  isField,
  isFullyUnmaskedOperation,
  isInlineFragment,
  isMutationOperation,
  isNonEmptyArray,
  isNonNullObject,
  isPlainObject,
  isQueryOperation,
  isReference,
  isStatefulPromise,
  isSubscriptionOperation,
  iterateObserversSafely,
  makeReference,
  makeUniqueId,
  maybe,
  maybeDeepFreeze,
  mergeDeep,
  mergeDeepArray,
  mergeIncrementalData,
  mergeOptions,
  offsetLimitPagination,
  omitDeep,
  preventUnhandledRejection,
  print,
  relayStylePagination,
  removeArgumentsFromDocument,
  removeClientSetsFromDocument,
  removeConnectionDirectiveFromDocument,
  removeDirectivesFromDocument,
  removeFragmentSpreadFromDocument,
  resultKeyNameFromField,
  shouldInclude,
  storeKeyNameFromField,
  stringifyForDisplay,
  stripTypename,
  valueToObjectRepresentation,
  wrapPromiseWithState
} from "./chunk-CQ4VASWP.js";
import "./chunk-ZB25FJBE.js";
import "./chunk-ZC22LKFR.js";
export {
  AutoCleanedStrongCache,
  AutoCleanedWeakCache,
  Concast,
  DEV,
  DeepMerger,
  DocumentTransform,
  Observable,
  addNonReactiveToNamedFragments,
  addTypenameToDocument,
  argumentsObjectFromField,
  asyncMap,
  buildQueryFromSelectionSet,
  cacheSizes,
  canUseAsyncIteratorSymbol,
  canUseDOM,
  canUseLayoutEffect,
  canUseSymbol,
  canUseWeakMap,
  canUseWeakSet,
  canonicalStringify,
  checkDocument,
  cloneDeep,
  compact,
  concatPagination,
  createFragmentMap,
  createFulfilledPromise,
  createRejectedPromise,
  fixObservableSubclass,
  getDefaultValues,
  getDirectiveNames,
  getFragmentDefinition,
  getFragmentDefinitions,
  getFragmentFromSelection,
  getFragmentMaskMode,
  getFragmentQueryDocument,
  getGraphQLErrorsFromResult,
  getInclusionDirectives,
  getMainDefinition,
  getOperationDefinition,
  getOperationName,
  getQueryDefinition,
  getStoreKeyName,
  getTypenameFromResult,
  graphQLResultHasError,
  hasAllDirectives,
  hasAnyDirectives,
  hasClientExports,
  hasDirectives,
  isApolloPayloadResult,
  isArray,
  isDocumentNode,
  isExecutionPatchIncrementalResult,
  isExecutionPatchInitialResult,
  isExecutionPatchResult,
  isField,
  isFullyUnmaskedOperation,
  isInlineFragment,
  isMutationOperation,
  isNonEmptyArray,
  isNonNullObject,
  isPlainObject,
  isQueryOperation,
  isReference,
  isStatefulPromise,
  isSubscriptionOperation,
  iterateObserversSafely,
  makeReference,
  makeUniqueId,
  maybe,
  maybeDeepFreeze,
  mergeDeep,
  mergeDeepArray,
  mergeIncrementalData,
  mergeOptions,
  offsetLimitPagination,
  omitDeep,
  preventUnhandledRejection,
  print,
  relayStylePagination,
  removeArgumentsFromDocument,
  removeClientSetsFromDocument,
  removeConnectionDirectiveFromDocument,
  removeDirectivesFromDocument,
  removeFragmentSpreadFromDocument,
  resultKeyNameFromField,
  shouldInclude,
  storeKeyNameFromField,
  stringifyForDisplay,
  stripTypename,
  valueToObjectRepresentation,
  wrapPromiseWithState
};
//# sourceMappingURL=@apollo_client_utilities.js.map
