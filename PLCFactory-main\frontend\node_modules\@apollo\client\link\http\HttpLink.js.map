{"version": 3, "file": "HttpLink.js", "sourceRoot": "", "sources": ["../../../src/link/http/HttpLink.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAE9C,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AAErD;IAA8B,4BAAU;IACtC,kBAAmB,OAAyB;QAAzB,wBAAA,EAAA,YAAyB;QAC1C,YAAA,MAAK,YAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAC;QADtB,aAAO,GAAP,OAAO,CAAkB;;IAE5C,CAAC;IACH,eAAC;AAAD,CAAC,AAJD,CAA8B,UAAU,GAIvC", "sourcesContent": ["import { ApolloLink } from \"../core/index.js\";\nimport type { HttpOptions } from \"./selectHttpOptionsAndBody.js\";\nimport { createHttpLink } from \"./createHttpLink.js\";\n\nexport class HttpLink extends ApolloLink {\n  constructor(public options: HttpOptions = {}) {\n    super(createHttpLink(options).request);\n  }\n}\n"]}