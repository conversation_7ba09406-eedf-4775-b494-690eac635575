import {
  require_react
} from "./chunk-K5YNGTCN.js";
import {
  __toESM
} from "./chunk-ZC22LKFR.js";

// node_modules/@mui/material/esm/List/ListContext.js
var React = __toESM(require_react(), 1);
var ListContext = React.createContext({});
if (true) {
  ListContext.displayName = "ListContext";
}
var ListContext_default = ListContext;

export {
  ListContext_default
};
//# sourceMappingURL=chunk-GXCSODXN.js.map
