{"version": 3, "file": "batchLink.js", "sourceRoot": "", "sources": ["../../../src/link/batch/batchLink.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAG9C,OAAO,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AAEjD,OAAO,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AAqCjD;IAA+B,6BAAU;IAGvC,mBAAY,WAA+B;QACzC,YAAA,MAAK,WAAE,SAAC;QAEF,IAAA,KAMF,WAAW,IAAI,EAAE,EALnB,aAAa,mBAAA,EACb,qBAAkB,EAAlB,aAAa,mBAAG,EAAE,KAAA,EAClB,gBAAY,EAAZ,QAAQ,mBAAG,CAAC,KAAA,EACZ,oBAAyB,EAAzB,YAAY,mBAAG,cAAM,OAAA,IAAI,EAAJ,CAAI,KAAA,EACzB,gBAAmB,EAAnB,QAAQ,mBAAG,cAAM,OAAA,EAAE,EAAF,CAAE,KACA,CAAC;QAEtB,KAAI,CAAC,OAAO,GAAG,IAAI,gBAAgB,CAAC;YAClC,aAAa,eAAA;YACb,aAAa,eAAA;YACb,QAAQ,UAAA;YACR,YAAY,cAAA;YACZ,QAAQ,UAAA;SACT,CAAC,CAAC;QAEH,4BAA4B;QAC5B,IAAI,WAAY,CAAC,YAAa,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC3C,KAAI,CAAC,OAAO,GAAG,UAAC,SAAS,IAAK,OAAA,KAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,SAAS,WAAA,EAAE,CAAC,EAA1C,CAA0C,CAAC;QAC3E,CAAC;;IACH,CAAC;IAEM,2BAAO,GAAd,UACE,SAAoB,EACpB,OAAkB;QAElB,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;YACjC,SAAS,WAAA;YACT,OAAO,SAAA;SACR,CAAC,CAAC;IACL,CAAC;IACH,gBAAC;AAAD,CAAC,AArCD,CAA+B,UAAU,GAqCxC", "sourcesContent": ["import type { Operation, FetchResult, NextLink } from \"../core/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport type { Observable } from \"../../utilities/index.js\";\nimport type { <PERSON><PERSON><PERSON>and<PERSON> } from \"./batching.js\";\nimport { OperationBatcher } from \"./batching.js\";\nexport type { BatchableRequest, BatchHandler } from \"./batching.js\";\nexport { OperationBatcher } from \"./batching.js\";\n\nexport namespace BatchLink {\n  export interface Options {\n    /**\n     * The interval at which to batch, in milliseconds.\n     *\n     * Defaults to 10.\n     */\n    batchInterval?: number;\n\n    /**\n     * \"batchInterval\" is a throttling behavior by default, if you instead wish\n     * to debounce outbound requests, set \"batchDebounce\" to true. More useful\n     * for mutations than queries.\n     */\n    batchDebounce?: boolean;\n\n    /**\n     * The maximum number of operations to include in one fetch.\n     *\n     * Defaults to 0 (infinite operations within the interval).\n     */\n    batchMax?: number;\n\n    /**\n     * The handler that should execute a batch of operations.\n     */\n    batchHandler?: BatchHandler;\n\n    /**\n     * creates the key for a batch\n     */\n    batchKey?: (operation: Operation) => string;\n  }\n}\n\nexport class BatchLink extends ApolloLink {\n  private batcher: OperationBatcher;\n\n  constructor(fetchParams?: BatchLink.Options) {\n    super();\n\n    const {\n      batchDebounce,\n      batchInterval = 10,\n      batchMax = 0,\n      batchHandler = () => null,\n      batchKey = () => \"\",\n    } = fetchParams || {};\n\n    this.batcher = new OperationBatcher({\n      batchDebounce,\n      batchInterval,\n      batchMax,\n      batchHandler,\n      batchKey,\n    });\n\n    //make this link terminating\n    if (fetchParams!.batchHandler!.length <= 1) {\n      this.request = (operation) => this.batcher.enqueueRequest({ operation });\n    }\n  }\n\n  public request(\n    operation: Operation,\n    forward?: NextLink\n  ): Observable<FetchResult> | null {\n    return this.batcher.enqueueRequest({\n      operation,\n      forward,\n    });\n  }\n}\n"]}