{"version": 3, "file": "types.bench.js", "sourceRoot": "", "sources": ["../../../src/masking/__benches__/types.bench.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAC5C,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAG3C,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAIpC,KAAK,CAAC;IACJ,eAAe,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;CACjC,CAAC,CAAC;AAEH,SAAS,IAAI,CAAC,IAAY,EAAE,EAA0B;IACpD,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;AAClB,CAAC;AAcD,IAAI,CAAC,iCAAiC,EAAE,UAAC,MAAM;IAgC7C,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE;QAC/B,OAAO,EAAsB,CAAC;IAChC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;QAC9B,YAAY,EAAoB,CAAC,aAAa,EAQ1C,CAAC;IACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,iCAAiC,EAAE,UAAC,MAAM;IAsC7C,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE;QAC/B,OAAO,EAAsB,CAAC;IAChC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;QAC9B,YAAY,EAAoB,CAAC,aAAa,EAa1C,CAAC;IACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,0CAA0C,EAAE,UAAC,MAAM;IA8CtD,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE;QAC/B,OAAO,EAAsB,CAAC;IAChC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;QAC9B,YAAY,EAAoB,CAAC,aAAa,EAoB1C,CAAC;IACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,2BAA2B,EAAE,UAAC,MAAM;IAmBvC,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE;QAC/B,OAAO,EAAsB,CAAC;IAChC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;QAC9B,YAAY,EAAoB,CAAC,aAAa,EAM1C,CAAC;IACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,4BAA4B,EAAE,UAAC,MAAM;IACxC,KAAK,CAAC,MAAM,GAAG,2BAA2B,EAAE;QAC1C,MAAM,EAAoB,CAAC;IAC7B,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAElC,KAAK,CAAC,MAAM,GAAG,0BAA0B,EAAE;QACzC,YAAY,EAAgB,CAAC,aAAa,EAAM,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,MAAM,GAAG,oCAAoC,EAAE;QACnD,MAAM,EAAsD,CAAC;IAC/D,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAElC,KAAK,CAAC,MAAM,GAAG,mCAAmC,EAAE;QAClD,YAAY,EAAiC,CAAC,aAAa,EAExD,CAAC;IACN,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,MAAM,GAAG,wBAAwB,EAAE;QACvC,MAAM,EAA8B,CAAC;IACvC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEjC,KAAK,CAAC,MAAM,GAAG,uBAAuB,EAAE;QACtC,YAAY,EAAqB,CAAC,WAAW,EAAE,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,MAAM,GAAG,oBAAoB,EAAE;QACnC,MAAM,EAAsB,CAAC;IAC/B,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEjC,KAAK,CAAC,MAAM,GAAG,mBAAmB,EAAE;QAClC,YAAY,EAAiB,CAAC,OAAO,EAAE,CAAC;IAC1C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,+BAA+B,EAAE,UAAC,MAAM;IAC3C,KAAK,CAAC,MAAM,GAAG,2BAA2B,EAAE;QAC1C,MAAM,EAAuB,CAAC;IAChC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEjC,KAAK,CAAC,MAAM,GAAG,0BAA0B,EAAE;QACzC,YAAY,EAAmB,CAAC,aAAa,EAAM,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,MAAM,GAAG,oCAAoC,EAAE;QACnD,MAAM,EAAyD,CAAC;IAClE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC,CAAC;IACjC,KAAK,CAAC,MAAM,GAAG,mCAAmC,EAAE;QAClD,YAAY,EAAoC,CAAC,aAAa,EAE3D,CAAC;IACN,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,MAAM,GAAG,wBAAwB,EAAE;QACvC,MAAM,EAAiC,CAAC;IAC1C,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC,CAAC;IACjC,KAAK,CAAC,MAAM,GAAG,uBAAuB,EAAE;QACtC,YAAY,EAAwB,CAAC,WAAW,EAAE,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,MAAM,GAAG,oBAAoB,EAAE;QACnC,MAAM,EAAyB,CAAC;IAClC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC,CAAC;IACjC,KAAK,CAAC,MAAM,GAAG,mBAAmB,EAAE;QAClC,YAAY,EAAoB,CAAC,OAAO,EAAE,CAAC;IAC7C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,oCAAoC,EAAE,UAAC,MAAM;IAChD,CAAC,SAAS,iBAAiB;QACzB,KAAK,CAAC,MAAM,GAAG,kDAAkD,EAAE;YACjE,MAAM,EAGH,CAAC;QACN,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,EAAE,CAAC;IAEL,CAAC,SAAS,kBAAkB;QAC1B,KAAK,CAAC,MAAM,GAAG,oCAAoC,EAAE;YACnD,MAAM,EAA2D,CAAC;QACpE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,EAAE,CAAC;AACP,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,mDAAmD,EAAE,UAAC,MAAM;IAoC/D,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE;QAC/B,OAAO,EAAsB,CAAC;IAChC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;;QAC9B,IAAM,CAAC,GAAG,EAAsB,CAAC;QACjC,2BAA2B;QAC3B,CAAC,CAAC,EAAE,CAAC;QACL,CAAC,CAAC,OAAO,CAAC;QACV,MAAA,MAAA,CAAC,CAAC,OAAO,0CAAG,CAAC,CAAC,0CAAE,EAAE,CAAC;QACnB,MAAA,MAAA,CAAC,CAAC,OAAO,0CAAG,CAAC,CAAC,0CAAE,SAAS,CAAC;QAC1B,MAAA,MAAA,CAAC,CAAC,OAAO,0CAAG,CAAC,CAAC,0CAAE,QAAQ,CAAC;QACzB,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAYjC,CAAC;IACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,4CAA4C,EAAE,UAAC,MAAM;IAkCxD,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE;QAC/B,OAAO,EAAsB,CAAC;IAChC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;QAC9B,IAAM,CAAC,GAAG,EAAsB,CAAC;QACjC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EA4BjC,CAAC;IACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,wDAAwD,EAAE,UAAC,MAAM;IAMpE,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE;QAC/B,OAAO,EAAyB,CAAC;IACnC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;QAC9B,IAAM,CAAC,GAAG,EAAyB,CAAC;QAEpC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAU,CAAC;IAClD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,qBAAqB,EAAE,UAAC,MAAM;IAKjC,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE;QAC/B,OAAO,EAAsB,CAAC;IAChC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;QAC9B,IAAM,CAAC,GAAG,EAAsB,CAAC;QAEjC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAEjC,CAAC;IACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,0DAA0D,EAAE,UAAC,MAAM;IAUtE,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE;QAC/B,OAAO,EAAyB,CAAC;IACnC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;QAC9B,IAAM,CAAC,GAAG,EAAyB,CAAC;QAEpC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAU,CAAC;IAClD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,gEAAgE,EAAE,UAAC,MAAM;IAO5E,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE;QAC/B,OAAO,EAAyB,CAAC;IACnC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;QAC9B,IAAM,CAAC,GAAG,EAAyB,CAAC;QAEpC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAU,CAAC;IAClD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,wDAAwD,EAAE,UAAC,MAAM;IAsBpE,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE;QAC/B,OAAO,EAAyB,CAAC;IACnC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;QAC9B,IAAM,CAAC,GAAG,EAAsB,CAAC;QACjC,IAAM,CAAC,GAAG,EAAmC,CAAC;QAE9C,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAMjC,CAAC;QACL,YAAY,CAAC,CAAC,CAAC,CAAC,aAAa,EAAQ,CAAC;IACxC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,2FAA2F,EAAE,UAAC,MAAM;IAIvG,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE;QAC/B,OAAO,EAAyB,CAAC;IACnC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;QAC9B,IAAM,CAAC,GAAG,EAAyB,CAAC;QAEpC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAU,CAAC;IAClD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,2EAA2E,EAAE,UAAC,MAAM;IACvF,SAAS,iBAAiB,CACxB,GAA6B;QAE7B,KAAK,CAAC,MAAM,GAAG,iCAAiC,EAAE;YAChD,IAAM,WAAW,GAA4B,GAAG,CAAC;YACjD,OAAO,WAAW,CAAC;QACrB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;QAEhC,KAAK,CAAC,MAAM,GAAG,gCAAgC,EAAE;YAC/C,IAAM,WAAW,GAA4B,GAAG,CAAC;YACjD,YAAY,CAAC,WAAW,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC;IACD,SAAS,mBAAmB,CAA8B,GAAM;QAC9D,KAAK,CAAC,MAAM,GAAG,iCAAiC,EAAE;YAChD,IAAM,WAAW,GAAmB,GAAG,CAAC;YACxC,OAAO,WAAW,CAAC;QACrB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;QAEhC,KAAK,CAAC,MAAM,GAAG,gCAAgC,EAAE;YAC/C,IAAM,WAAW,GAAmB,GAAG,CAAC;YACxC,0GAA0G;YAC1G,IAAM,IAAI,GAAM,WAAW,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IACD,iBAAiB,CAAC,EAAS,CAAC,CAAC;IAC7B,mBAAmB,CAAC,EAAS,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,0CAA0C,EAAE,UAAC,MAAM;IAiBtD,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE;QAC/B,OAAO,EAAsB,CAAC;IAChC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE;QAC9B,IAAM,CAAC,GAAG,EAAsB,CAAC;QAEjC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAKjC,CAAC;IACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import type { MaybeMasked, Unmasked } from \"../index.js\";\nimport { attest, bench } from \"@ark/attest\";\nimport { expectTypeOf } from \"expect-type\";\nimport type { DeepPartial } from \"../../utilities/index.js\";\n\nimport { setup } from \"@ark/attest\";\nimport type { ContainsFragmentsRefs } from \"../internal/types.js\";\nimport type { TypedDocumentNode } from \"../../index.js\";\n\nsetup({\n  updateSnapshots: !process.env.CI,\n});\n\nfunction test(name: string, fn: (name: string) => void) {\n  fn(name + \": \");\n}\n\ntype UnrelatedType = {\n  __typename: \"Unrelated\";\n} & { \" $fragmentName\"?: \"Unrelated\" } & {\n  \" $fragmentRefs\"?: {\n    Unrelated: {\n      __unrelated: boolean;\n    };\n  };\n};\n// @ts-ignore\ntype _TypeCacheWarmup = Unmasked<UnrelatedType> | MaybeMasked<UnrelatedType>;\n\ntest(\"unmasks deeply nested fragments\", (prefix) => {\n  type UserFieldsFragment = {\n    __typename: \"User\";\n    id: number;\n    age: number;\n  } & { \" $fragmentName\"?: \"UserFieldsFragment\" } & {\n    \" $fragmentRefs\"?: {\n      NameFieldsFragment: NameFieldsFragment;\n      JobFieldsFragment: JobFieldsFragment;\n    };\n  };\n\n  type NameFieldsFragment = {\n    __typename: \"User\";\n    firstName: string;\n    lastName: string;\n  } & { \" $fragmentName\"?: \"NameFieldsFragment\" };\n\n  type JobFieldsFragment = {\n    __typename: \"User\";\n    job: string;\n  } & { \" $fragmentName\"?: \"JobFieldsFragment\" } & {\n    \" $fragmentRefs\"?: { CareerFieldsFragment: CareerFieldsFragment };\n  };\n\n  type CareerFieldsFragment = {\n    __typename: \"User\";\n    position: string;\n  } & { \" $fragmentName\"?: \"CareerFieldsFragment\" };\n\n  type Source = UserFieldsFragment;\n\n  bench(prefix + \"instantiations\", () => {\n    return {} as Unmasked<Source>;\n  }).types([5, \"instantiations\"]);\n\n  bench(prefix + \"functionality\", () => {\n    expectTypeOf<Unmasked<Source>>().toEqualTypeOf<{\n      __typename: \"User\";\n      id: number;\n      age: number;\n      firstName: string;\n      lastName: string;\n      job: string;\n      position: string;\n    }>();\n  });\n});\n\ntest(\"unmasks deeply nested fragments\", (prefix) => {\n  type UserFieldsFragment = {\n    __typename: \"User\";\n    id: number;\n    age: number;\n    jobs: Array<\n      {\n        __typename: \"Job\";\n        id: string;\n        title: string;\n      } & { \" $fragmentRefs\"?: { JobFieldsFragment: JobFieldsFragment } }\n    >;\n  } & { \" $fragmentName\"?: \"UserFieldsFragment\" } & {\n    \" $fragmentRefs\"?: {\n      NameFieldsFragment: NameFieldsFragment;\n    };\n  };\n\n  type NameFieldsFragment = {\n    __typename: \"User\";\n    firstName: string;\n    lastName: string;\n  } & { \" $fragmentName\"?: \"NameFieldsFragment\" };\n\n  type JobFieldsFragment = {\n    __typename: \"Job\";\n    job: string;\n  } & { \" $fragmentName\"?: \"JobFieldsFragment\" } & {\n    \" $fragmentRefs\"?: { CareerFieldsFragment: CareerFieldsFragment };\n  };\n\n  type CareerFieldsFragment = {\n    __typename: \"Job\";\n    position: string;\n  } & { \" $fragmentName\"?: \"CareerFieldsFragment\" };\n\n  type Source = UserFieldsFragment;\n\n  bench(prefix + \"instantiations\", () => {\n    return {} as Unmasked<Source>;\n  }).types([5, \"instantiations\"]);\n\n  bench(prefix + \"functionality\", () => {\n    expectTypeOf<Unmasked<Source>>().toEqualTypeOf<{\n      __typename: \"User\";\n      id: number;\n      age: number;\n      firstName: string;\n      lastName: string;\n      jobs: Array<{\n        __typename: \"Job\";\n        id: string;\n        title: string;\n        job: string;\n        position: string;\n      }>;\n    }>();\n  });\n});\n\ntest(\"unmasks deeply nested nullable fragments\", (prefix) => {\n  type UserFieldsFragment = {\n    __typename: \"User\";\n    id: number;\n    age: number;\n    career:\n      | ({\n          __typename: \"Job\";\n          id: string;\n          title: string;\n        } & { \" $fragmentRefs\"?: { JobFieldsFragment: JobFieldsFragment } })\n      | null;\n    jobs: Array<\n      | ({\n          __typename: \"Job\";\n          id: string;\n          title: string;\n        } & { \" $fragmentRefs\"?: { JobFieldsFragment: JobFieldsFragment } })\n      | null\n    >;\n  } & { \" $fragmentName\"?: \"UserFieldsFragment\" } & {\n    \" $fragmentRefs\"?: {\n      NameFieldsFragment: NameFieldsFragment;\n    };\n  };\n\n  type NameFieldsFragment = {\n    __typename: \"User\";\n    firstName: string;\n    lastName: string;\n  } & { \" $fragmentName\"?: \"NameFieldsFragment\" };\n\n  type JobFieldsFragment = {\n    __typename: \"Job\";\n    job: string;\n  } & { \" $fragmentName\"?: \"JobFieldsFragment\" } & {\n    \" $fragmentRefs\"?: { CareerFieldsFragment: CareerFieldsFragment };\n  };\n\n  type CareerFieldsFragment = {\n    __typename: \"Job\";\n    position: string;\n  } & { \" $fragmentName\"?: \"CareerFieldsFragment\" };\n\n  type Source = UserFieldsFragment;\n\n  bench(prefix + \"instantiations\", () => {\n    return {} as Unmasked<Source>;\n  }).types([5, \"instantiations\"]);\n\n  bench(prefix + \"functionality\", () => {\n    expectTypeOf<Unmasked<Source>>().toEqualTypeOf<{\n      __typename: \"User\";\n      id: number;\n      age: number;\n      firstName: string;\n      lastName: string;\n      career: {\n        __typename: \"Job\";\n        id: string;\n        title: string;\n        job: string;\n        position: string;\n      } | null;\n      jobs: Array<{\n        __typename: \"Job\";\n        id: string;\n        title: string;\n        job: string;\n        position: string;\n      } | null>;\n    }>();\n  });\n});\n\ntest(\"unmasks DeepPartial types\", (prefix) => {\n  type UserFieldsFragment = {\n    __typename: \"User\";\n    id: number;\n    age: number;\n  } & { \" $fragmentName\"?: \"UserFieldsFragment\" } & {\n    \" $fragmentRefs\"?: {\n      NameFieldsFragment: NameFieldsFragment;\n    };\n  };\n\n  type NameFieldsFragment = {\n    __typename: \"User\";\n    firstName: string;\n    lastName: string;\n  } & { \" $fragmentName\"?: \"NameFieldsFragment\" };\n\n  type Source = DeepPartial<UserFieldsFragment>;\n\n  bench(prefix + \"instantiations\", () => {\n    return {} as Unmasked<Source>;\n  }).types([5, \"instantiations\"]);\n\n  bench(prefix + \"functionality\", () => {\n    expectTypeOf<Unmasked<Source>>().toEqualTypeOf<{\n      __typename?: \"User\";\n      id?: number;\n      age?: number;\n      firstName?: string;\n      lastName?: string;\n    }>();\n  });\n});\n\ntest(\"Unmasked handles odd types\", (prefix) => {\n  bench(prefix + \"empty type instantiations\", () => {\n    attest<{}, Unmasked<{}>>();\n  }).types([111, \"instantiations\"]);\n\n  bench(prefix + \"empty type functionality\", () => {\n    expectTypeOf<Unmasked<{}>>().toEqualTypeOf<{}>();\n  });\n\n  bench(prefix + \"generic record type instantiations\", () => {\n    attest<Record<string, any>, Unmasked<Record<string, any>>>();\n  }).types([115, \"instantiations\"]);\n\n  bench(prefix + \"generic record type functionality\", () => {\n    expectTypeOf<Unmasked<Record<string, any>>>().toEqualTypeOf<\n      Record<string, any>\n    >();\n  });\n\n  bench(prefix + \"unknown instantiations\", () => {\n    attest<unknown, Unmasked<unknown>>();\n  }).types([47, \"instantiations\"]);\n\n  bench(prefix + \"unknown functionality\", () => {\n    expectTypeOf<Unmasked<unknown>>().toBeUnknown();\n  });\n\n  bench(prefix + \"any instantiations\", () => {\n    attest<any, Unmasked<any>>();\n  }).types([48, \"instantiations\"]);\n\n  bench(prefix + \"any functionality\", () => {\n    expectTypeOf<Unmasked<any>>().toBeAny();\n  });\n});\n\ntest(\"MaybeMasked handles odd types\", (prefix) => {\n  bench(prefix + \"empty type instantiations\", () => {\n    attest<{}, MaybeMasked<{}>>();\n  }).types([41, \"instantiations\"]);\n\n  bench(prefix + \"empty type functionality\", () => {\n    expectTypeOf<MaybeMasked<{}>>().toEqualTypeOf<{}>();\n  });\n\n  bench(prefix + \"generic record type instantiations\", () => {\n    attest<Record<string, any>, MaybeMasked<Record<string, any>>>();\n  }).types([46, \"instantiations\"]);\n  bench(prefix + \"generic record type functionality\", () => {\n    expectTypeOf<MaybeMasked<Record<string, any>>>().toEqualTypeOf<\n      Record<string, any>\n    >();\n  });\n\n  bench(prefix + \"unknown instantiations\", () => {\n    attest<unknown, MaybeMasked<unknown>>();\n  }).types([41, \"instantiations\"]);\n  bench(prefix + \"unknown functionality\", () => {\n    expectTypeOf<MaybeMasked<unknown>>().toBeUnknown();\n  });\n\n  bench(prefix + \"any instantiations\", () => {\n    attest<any, MaybeMasked<any>>();\n  }).types([43, \"instantiations\"]);\n  bench(prefix + \"any functionality\", () => {\n    expectTypeOf<MaybeMasked<any>>().toBeAny();\n  });\n});\n\ntest(\"distributed members on MaybeMasked\", (prefix) => {\n  (function unresolvedGeneric<T>() {\n    bench(prefix + \"one unresolved generic mixed with null|undefined\", () => {\n      attest<\n        [MaybeMasked<T> | null | undefined],\n        [MaybeMasked<T | null | undefined>]\n      >();\n    }).types([49, \"instantiations\"]);\n  })();\n\n  (function unresolvedGenerics<T, V>() {\n    bench(prefix + \"two unresolved generics distribute\", () => {\n      attest<[MaybeMasked<T> | MaybeMasked<V>], [MaybeMasked<T | V>]>();\n    }).types([50, \"instantiations\"]);\n  })();\n});\n\ntest(\"deals with overlapping array from parent fragment\", (prefix) => {\n  type Source = {\n    __typename: \"Track\";\n    /** comment: id */\n    id: number;\n    /** comment: artists */\n    artists?: Array<{\n      __typename: \"Artist\";\n      /** comment: artists.id */\n      id: number;\n      \" $fragmentRefs\"?: {\n        ArtistFragment: ArtistFragment;\n      };\n    }> | null;\n    \" $fragmentRefs\"?: {\n      NestedTrackFragment: NestedTrackFragment;\n    };\n  };\n\n  type ArtistFragment = {\n    \" $fragmentName\"?: \"Fragment__Artist\";\n    __typename: \"Artist\";\n    /** comment: artists.birthday */\n    birthdate: string;\n  };\n\n  type NestedTrackFragment = {\n    \" $fragmentName\"?: \"Fragment__Track\";\n    __typename: \"Track\";\n    artists?: Array<{\n      __typename: \"Artist\";\n      /** comment: artists.lastname */\n      lastname: string;\n    }> | null;\n  };\n\n  bench(prefix + \"instantiations\", () => {\n    return {} as Unmasked<Source>;\n  }).types([5, \"instantiations\"]);\n\n  bench(prefix + \"functionality\", () => {\n    const x = {} as Unmasked<Source>;\n    // some fields for hovering\n    x.id;\n    x.artists;\n    x.artists?.[0]?.id;\n    x.artists?.[0]?.birthdate;\n    x.artists?.[0]?.lastname;\n    expectTypeOf(x).branded.toEqualTypeOf<{\n      __typename: \"Track\";\n      id: number;\n      artists?:\n        | Array<{\n            __typename: \"Artist\";\n            id: number;\n            birthdate: string;\n            lastname: string;\n          }>\n        | null\n        | undefined;\n    }>();\n  });\n});\n\ntest(\"base type, multiple fragments on sub-types\", (prefix) => {\n  type Source = {\n    __typename: \"Track\";\n    id: number;\n    artists?: Array<{\n      __typename: \"Person\" | \"Animatronic\" | \"CartoonCharacter\";\n      id: number;\n      name: string;\n      \" $fragmentRefs\"?: {\n        PersonFragment: PersonFragment;\n        AnimatronicFragment: AnimatronicFragment;\n        CartoonCharacterFragment: CartoonCharacterFragment;\n      };\n    }> | null;\n  };\n\n  type PersonFragment = {\n    \" $fragmentName\"?: \"Fragment__Person\";\n    __typename: \"Person\";\n    birthdate: string;\n  };\n  type AnimatronicFragment = {\n    \" $fragmentName\"?: \"Fragment__Animatronic\";\n    __typename: \"Animatronic\";\n    manufacturer: string;\n    warrantyEndDate: string;\n  };\n  type CartoonCharacterFragment = {\n    \" $fragmentName\"?: \"Fragment__CartoonCharacter\";\n    __typename: \"CartoonCharacter\";\n    animator: string;\n    voiceActor: string;\n  };\n\n  bench(prefix + \"instantiations\", () => {\n    return {} as Unmasked<Source>;\n  }).types([5, \"instantiations\"]);\n\n  bench(prefix + \"functionality\", () => {\n    const x = {} as Unmasked<Source>;\n    expectTypeOf(x).branded.toEqualTypeOf<{\n      __typename: \"Track\";\n      id: number;\n      artists?:\n        | Array<\n            | {\n                __typename: \"Person\";\n                id: number;\n                name: string;\n                birthdate: string;\n              }\n            | {\n                __typename: \"Animatronic\";\n                id: number;\n                name: string;\n                manufacturer: string;\n                warrantyEndDate: string;\n              }\n            | {\n                __typename: \"CartoonCharacter\";\n                id: number;\n                name: string;\n                animator: string;\n                voiceActor: string;\n              }\n          >\n        | null\n        | undefined;\n    }>();\n  });\n});\n\ntest(\"does not detect `$fragmentRefs` if type contains `any`\", (prefix) => {\n  interface Source {\n    foo: { bar: any[] };\n    \" $fragmentName\": \"foo\";\n  }\n\n  bench(prefix + \"instantiations\", () => {\n    return {} as MaybeMasked<Source>;\n  }).types([1, \"instantiations\"]);\n\n  bench(prefix + \"functionality\", () => {\n    const x = {} as MaybeMasked<Source>;\n\n    expectTypeOf(x).branded.toEqualTypeOf<Source>();\n  });\n});\n\ntest(\"leaves tuples alone\", (prefix) => {\n  interface Source {\n    coords: [long: number, lat: number];\n  }\n\n  bench(prefix + \"instantiations\", () => {\n    return {} as Unmasked<Source>;\n  }).types([5, \"instantiations\"]);\n\n  bench(prefix + \"functionality\", () => {\n    const x = {} as Unmasked<Source>;\n\n    expectTypeOf(x).branded.toEqualTypeOf<{\n      coords: [long: number, lat: number];\n    }>();\n  });\n});\n\ntest(\"does not detect `$fragmentRefs` if type is a record type\", (prefix) => {\n  interface MetadataItem {\n    foo: string;\n  }\n\n  interface Source {\n    metadata: Record<string, MetadataItem>;\n    \" $fragmentName\": \"Source\";\n  }\n\n  bench(prefix + \"instantiations\", () => {\n    return {} as MaybeMasked<Source>;\n  }).types([1, \"instantiations\"]);\n\n  bench(prefix + \"functionality\", () => {\n    const x = {} as MaybeMasked<Source>;\n\n    expectTypeOf(x).branded.toEqualTypeOf<Source>();\n  });\n});\n\ntest(\"does not detect `$fragmentRefs` on types with index signatures\", (prefix) => {\n  interface Source {\n    foo: string;\n    \" $fragmentName\": \"Source\";\n    [key: string]: string;\n  }\n\n  bench(prefix + \"instantiations\", () => {\n    return {} as MaybeMasked<Source>;\n  }).types([1, \"instantiations\"]);\n\n  bench(prefix + \"functionality\", () => {\n    const x = {} as MaybeMasked<Source>;\n\n    expectTypeOf(x).branded.toEqualTypeOf<Source>();\n  });\n});\n\ntest(\"detects `$fragmentRefs` on types with index signatures\", (prefix) => {\n  type Source = {\n    __typename: \"Foo\";\n    id: number;\n    metadata: Record<string, number>;\n    structuredMetadata: StructuredMetadata;\n  } & { \" $fragmentName\"?: \"UserFieldsFragment\" } & {\n    \" $fragmentRefs\"?: {\n      FooFragment: FooFragment;\n    };\n  };\n\n  interface StructuredMetadata {\n    bar: number;\n    [index: string]: number;\n  }\n\n  type FooFragment = {\n    __typename: \"Foo\";\n    foo: string;\n  } & { \" $fragmentName\"?: \"FooFragment\" };\n\n  bench(prefix + \"instantiations\", () => {\n    return {} as MaybeMasked<Source>;\n  }).types([1, \"instantiations\"]);\n\n  bench(prefix + \"functionality\", () => {\n    const x = {} as Unmasked<Source>;\n    const y = {} as ContainsFragmentsRefs<Source>;\n\n    expectTypeOf(x).branded.toEqualTypeOf<{\n      __typename: \"Foo\";\n      id: number;\n      metadata: Record<string, number>;\n      foo: string;\n      structuredMetadata: StructuredMetadata;\n    }>();\n    expectTypeOf(y).toEqualTypeOf<true>();\n  });\n});\n\ntest(\"recursive types: no error 'Type instantiation is excessively deep and possibly infinite.'\", (prefix) => {\n  // this type is self-recursive\n  type Source = import(\"graphql\").IntrospectionQuery;\n\n  bench(prefix + \"instantiations\", () => {\n    return {} as MaybeMasked<Source>;\n  }).types([1, \"instantiations\"]);\n\n  bench(prefix + \"functionality\", () => {\n    const x = {} as MaybeMasked<Source>;\n\n    expectTypeOf(x).branded.toEqualTypeOf<Source>();\n  });\n});\n\ntest(\"MaybeMasked can be called with a generic if `mode` is not set to `unmask`\", (prefix) => {\n  function withGenericResult<T extends { [key: string]: string }>(\n    arg: TypedDocumentNode<T, {}>\n  ) {\n    bench(prefix + \"Result generic - instantiations\", () => {\n      const maybeMasked: MaybeMasked<typeof arg> = arg;\n      return maybeMasked;\n    }).types([1, \"instantiations\"]);\n\n    bench(prefix + \"Result generic - functionality\", () => {\n      const maybeMasked: MaybeMasked<typeof arg> = arg;\n      expectTypeOf(maybeMasked).toEqualTypeOf(arg);\n    });\n  }\n  function withGenericDocument<T extends TypedDocumentNode>(arg: T) {\n    bench(prefix + \"Result generic - instantiations\", () => {\n      const maybeMasked: MaybeMasked<T> = arg;\n      return maybeMasked;\n    }).types([1, \"instantiations\"]);\n\n    bench(prefix + \"Result generic - functionality\", () => {\n      const maybeMasked: MaybeMasked<T> = arg;\n      // cannot use unresolved generic with `expectTypeOf` here so we just try an assignment the other way round\n      const test: T = maybeMasked;\n      return test;\n    });\n  }\n  withGenericResult({} as any);\n  withGenericDocument({} as any);\n});\n\ntest(\"Unmasked handles branded primitive types\", (prefix) => {\n  type Branded<T, Name extends string> = T & { __branded?: Name };\n  type UUID = Branded<string, \"UUID\">;\n  type Source = {\n    __typename: \"Foo\";\n    id: UUID;\n    name: string;\n  } & { \" $fragmentName\"?: \"UserFieldsFragment\" } & {\n    \" $fragmentRefs\"?: {\n      FooFragment: FooFragment;\n    };\n  };\n  type FooFragment = {\n    __typename: \"Foo\";\n    age: number;\n  } & { \" $fragmentName\"?: \"UserFieldsFragment\" };\n\n  bench(prefix + \"instantiations\", () => {\n    return {} as Unmasked<Source>;\n  }).types([5, \"instantiations\"]);\n\n  bench(prefix + \"functionality\", () => {\n    const x = {} as Unmasked<Source>;\n\n    expectTypeOf(x).branded.toEqualTypeOf<{\n      __typename: \"Foo\";\n      id: UUID;\n      name: string;\n      age: number;\n    }>();\n  });\n});\n"]}