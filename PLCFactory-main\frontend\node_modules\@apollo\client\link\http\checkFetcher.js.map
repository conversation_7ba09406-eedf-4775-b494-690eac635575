{"version": 3, "file": "checkFetcher.js", "sourceRoot": "", "sources": ["../../../src/link/http/checkFetcher.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AAErE,MAAM,CAAC,IAAM,YAAY,GAAG,UAAC,OAAiC;IAC5D,IAAI,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;QAC7C,MAAM,iBAAiB,CAAC,obAWvB,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC", "sourcesContent": ["import { newInvariantError } from \"../../utilities/globals/index.js\";\n\nexport const checkFetcher = (fetcher: typeof fetch | undefined) => {\n  if (!fetcher && typeof fetch === \"undefined\") {\n    throw newInvariantError(`\n\"fetch\" has not been found globally and no fetcher has been \\\nconfigured. To fix this, install a fetch package (like \\\nhttps://www.npmjs.com/package/cross-fetch), instantiate the \\\nfetcher, and pass it into your HttpLink constructor. For example:\n\nimport fetch from 'cross-fetch';\nimport { ApolloClient, HttpLink } from '@apollo/client';\nconst client = new ApolloClient({\n  link: new HttpLink({ uri: '/graphql', fetch })\n});\n    `);\n  }\n};\n"]}