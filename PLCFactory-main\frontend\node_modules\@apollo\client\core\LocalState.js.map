{"version": 3, "file": "LocalState.js", "sourceRoot": "", "sources": ["../../src/core/LocalState.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAe1D,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAIxD,OAAO,EACL,wBAAwB,EACxB,0BAA0B,EAC1B,iBAAiB,EACjB,sBAAsB,EACtB,iBAAiB,EACjB,aAAa,EACb,OAAO,EACP,gBAAgB,EAChB,SAAS,EACT,cAAc,EACd,4BAA4B,EAC5B,sBAAsB,EACtB,aAAa,GACd,MAAM,uBAAuB,CAAC;AAI/B,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAsC9C;IAUE,oBAAY,EAKqB;YAJ/B,KAAK,WAAA,EACL,MAAM,YAAA,EACN,SAAS,eAAA,EACT,eAAe,qBAAA;QATT,6BAAwB,GAAG,IAAI,OAAO,EAG3C,CAAC;QAQF,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC/B,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YACpB,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAEM,iCAAY,GAAnB,UAAoB,SAAkC;QAAtD,iBASC;QARC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;QACtC,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7B,SAAS,CAAC,OAAO,CAAC,UAAC,aAAa;gBAC9B,KAAI,CAAC,SAAS,GAAG,SAAS,CAAC,KAAI,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAEM,iCAAY,GAAnB,UAAoB,SAAkC;QACpD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;IAEM,iCAAY,GAAnB;QACE,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED,yEAAyE;IACzE,0EAA0E;IAC1E,iEAAiE;IACjE,yCAAyC;IAC5B,iCAAY,GAAzB;4DAAiC,EAYhC;gBAXC,QAAQ,cAAA,EACR,YAAY,kBAAA,EACZ,OAAO,aAAA,EACP,SAAS,eAAA,EACT,8BAA8B,EAA9B,sBAAsB,mBAAG,KAAK,KAAA;;gBAQ9B,IAAI,QAAQ,EAAE,CAAC;oBACb,sBAAO,IAAI,CAAC,eAAe,CACzB,QAAQ,EACR,YAAY,CAAC,IAAI,EACjB,OAAO,EACP,SAAS,EACT,IAAI,CAAC,eAAe,EACpB,sBAAsB,CACvB,CAAC,IAAI,CAAC,UAAC,WAAW,IAAK,OAAA,uBACnB,YAAY,KACf,IAAI,EAAE,WAAW,CAAC,MAAM,IACxB,EAHsB,CAGtB,CAAC,EAAC;gBACN,CAAC;gBAED,sBAAO,YAAY,EAAC;;;KACrB;IAEM,uCAAkB,GAAzB,UAA0B,eAAgC;QACxD,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IACzC,CAAC;IAEM,uCAAkB,GAAzB;QACE,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,2EAA2E;IAC3E,uBAAuB;IAChB,gCAAW,GAAlB,UAAmB,QAAsB;QACvC,IAAI,aAAa,CAAC,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC;YACxC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mEAAmE;IAC5D,gCAAW,GAAlB,UAAmB,QAAsB;QACvC,OAAO,4BAA4B,CAAC,QAAQ,CAAC,CAAC;IAChD,CAAC;IAEM,mCAAc,GAArB,UAAsB,OAA6B;QACzC,IAAA,KAAK,GAAK,IAAI,MAAT,CAAU;QACvB,6BACK,OAAO,KACV,KAAK,OAAA;YACL,oEAAoE;YACpE,WAAW,YAAC,GAAgB;gBAC1B,OAAO,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC7B,CAAC,IACD;IACJ,CAAC;IAED,0EAA0E;IAC1E,2EAA2E;IAC3E,mDAAmD;IACtC,yCAAoB,GAAjC;4DACE,QAAsB,EACtB,SAA8B,EAC9B,OAAY;YADZ,0BAAA,EAAA,YAAmB,EAAW;YAC9B,wBAAA,EAAA,YAAY;;gBAEZ,IAAI,QAAQ,EAAE,CAAC;oBACb,sBAAO,IAAI,CAAC,eAAe,CACzB,QAAQ,EACR,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE,EACvD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAC5B,SAAS,CACV,CAAC,IAAI,CAAC,UAAC,IAAI,IAAK,OAAA,uBACZ,SAAS,GACT,IAAI,CAAC,iBAAiB,EACzB,EAHe,CAGf,CAAC,EAAC;gBACN,CAAC;gBAED,mCACK,SAAS,GACZ;;;KACH;IAEM,yCAAoB,GAA3B,UAA4B,QAAiB;QAC3C,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,KAAK,CAAC,QAAQ,EAAE;YACd,SAAS,EAAE;gBACT,KAAK,YAAC,IAAI;oBACR,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;wBACnD,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAClC,UAAC,GAAG;4BACF,OAAA,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ;gCAC3B,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,cAAc;gCACjC,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;wBAFxB,CAEwB,CAC3B,CAAC;wBACF,IAAI,cAAc,EAAE,CAAC;4BACnB,OAAO,KAAK,CAAC;wBACf,CAAC;oBACH,CAAC;gBACH,CAAC;aACF;SACF,CAAC,CAAC;QACH,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,4CAA4C;IACpC,4CAAuB,GAA/B,UACE,QAAsB,EACtB,SAA+B;QAE/B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACrB,KAAK,EAAE,0BAA0B,CAAC,QAAQ,CAAC;YAC3C,SAAS,WAAA;YACT,iBAAiB,EAAE,IAAI;YACvB,UAAU,EAAE,KAAK;SAClB,CAAC,CAAC,MAAM,CAAC;IACZ,CAAC;IAEa,oCAAe,GAA7B;4DACE,QAAsB,EACtB,SAAgB,EAChB,OAAiB,EACjB,SAA2B,EAC3B,eAA6C,EAC7C,sBAAuC;;YAHvC,wBAAA,EAAA,YAAiB;YACjB,0BAAA,EAAA,cAA2B;YAC3B,gCAAA,EAAA,gCAAyC,OAAA,IAAI,EAAJ,CAAI;YAC7C,uCAAA,EAAA,8BAAuC;;gBAEjC,cAAc,GAAG,iBAAiB,CACtC,QAAQ,CACkB,CAAC;gBACvB,SAAS,GAAG,sBAAsB,CAAC,QAAQ,CAAC,CAAC;gBAC7C,WAAW,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC;gBAC3C,mBAAmB,GAAG,IAAI,CAAC,0BAA0B,CACzD,cAAc,EACd,WAAW,CACZ,CAAC;gBAEI,mBAAmB,GAAG,cAAc,CAAC,SAAS,CAAC;gBAE/C,oBAAoB,GACxB,mBAAmB,CAAC,CAAC;oBACnB,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;wBAC3C,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC9B,CAAC,CAAC,OAAO,CAAC;gBAEN,KAAoB,IAAI,EAAtB,KAAK,WAAA,EAAE,MAAM,YAAA,CAAU;gBACzB,WAAW,GAAgB;oBAC/B,WAAW,aAAA;oBACX,OAAO,wBACF,OAAO,KACV,KAAK,OAAA,EACL,MAAM,QAAA,GACP;oBACD,SAAS,WAAA;oBACT,eAAe,iBAAA;oBACf,oBAAoB,sBAAA;oBACpB,iBAAiB,EAAE,EAAE;oBACrB,mBAAmB,qBAAA;oBACnB,sBAAsB,wBAAA;iBACvB,CAAC;gBACI,uBAAuB,GAAG,KAAK,CAAC;gBAEtC,sBAAO,IAAI,CAAC,mBAAmB,CAC7B,cAAc,CAAC,YAAY,EAC3B,uBAAuB,EACvB,SAAS,EACT,WAAW,CACZ,CAAC,IAAI,CAAC,UAAC,MAAM,IAAK,OAAA,CAAC;wBAClB,MAAM,QAAA;wBACN,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;qBACjD,CAAC,EAHiB,CAGjB,CAAC,EAAC;;;KACL;IAEa,wCAAmB,GAAjC,UACE,YAA8B,EAC9B,uBAAgC,EAChC,SAAgB,EAChB,WAAwB;;;;;gBAEhB,WAAW,GAAyB,WAAW,YAApC,EAAE,OAAO,GAAgB,WAAW,QAA3B,EAAE,SAAS,GAAK,WAAW,UAAhB,CAAiB;gBAClD,cAAc,GAAY,CAAC,SAAS,CAAC,CAAC;gBAEtC,OAAO,GAAG,UAAO,SAAwB;;;wBAC7C,IACE,CAAC,uBAAuB;4BACxB,CAAC,WAAW,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,EAC/C,CAAC;4BACD,6CAA6C;4BAC7C,8FAA8F;4BAC9F,sBAAO;wBACT,CAAC;wBACD,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC;4BACzC,sBAAsB;4BACtB,sBAAO;wBACT,CAAC;wBAED,IAAI,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;4BACvB,sBAAO,IAAI,CAAC,YAAY,CACtB,SAAS,EACT,uBAAuB,EACvB,SAAS,EACT,WAAW,CACZ,CAAC,IAAI,CAAC,UAAC,WAAW;;oCACjB,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE,CAAC;wCACvC,cAAc,CAAC,IAAI,CAAC,CAAA;4CAClB,GAAC,sBAAsB,CAAC,SAAS,CAAC,IAAG,WAAW;8CACxC,CAAA,CAAC,CAAC;oCACd,CAAC;gCACH,CAAC,CAAC,EAAC;wBACL,CAAC;wBAID,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC;4BAChC,QAAQ,GAAG,SAAS,CAAC;wBACvB,CAAC;6BAAM,CAAC;4BACN,4BAA4B;4BAC5B,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;4BAC7C,SAAS,CAAC,QAAQ,EAAE,sBAAsB,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACpE,CAAC;wBAED,IAAI,QAAQ,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;4BACjC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;4BACxD,IAAI,WAAW,CAAC,eAAe,CAAC,SAAS,EAAE,aAAa,EAAE,OAAO,CAAC,EAAE,CAAC;gCACnE,sBAAO,IAAI,CAAC,mBAAmB,CAC7B,QAAQ,CAAC,YAAY,EACrB,uBAAuB,EACvB,SAAS,EACT,WAAW,CACZ,CAAC,IAAI,CAAC,UAAC,cAAc;wCACpB,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;oCACtC,CAAC,CAAC,EAAC;4BACL,CAAC;wBACH,CAAC;;;qBACF,CAAC;gBAEF,sBAAO,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;wBAC5D,OAAO,cAAc,CAAC,cAAc,CAAC,CAAC;oBACxC,CAAC,CAAC,EAAC;;;KACJ;IAEa,iCAAY,GAA1B,UACE,KAAgB,EAChB,uBAAgC,EAChC,SAAc,EACd,WAAwB;;;;;gBAExB,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,sBAAO,IAAI,EAAC;gBACd,CAAC;gBAEO,SAAS,GAAK,WAAW,UAAhB,CAAiB;gBAC5B,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC7B,gBAAgB,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;gBACjD,SAAS,GAAG,SAAS,KAAK,gBAAgB,CAAC;gBAC3C,aAAa,GAAG,SAAS,CAAC,gBAAgB,CAAC,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC;gBACtE,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;gBAEnD,qEAAqE;gBACrE,mEAAmE;gBACnE,uDAAuD;gBACvD,0EAA0E;gBAC1E,IACE,CAAC,WAAW,CAAC,sBAAsB;oBACnC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAChC,CAAC;oBACK,YAAY,GAChB,SAAS,CAAC,UAAU,IAAI,WAAW,CAAC,oBAAoB,CAAC;oBACrD,WAAW,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;oBACnE,IAAI,WAAW,EAAE,CAAC;wBACV,OAAO,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC;wBACtE,IAAI,OAAO,EAAE,CAAC;4BACZ,aAAa,GAAG,OAAO,CAAC,OAAO;4BAC7B,4DAA4D;4BAC5D,+CAA+C;4BAC/C,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE;gCACvC,SAAS;gCACT,wBAAwB,CAAC,KAAK,EAAE,SAAS,CAAC;gCAC1C,WAAW,CAAC,OAAO;gCACnB,EAAE,KAAK,OAAA,EAAE,WAAW,EAAE,WAAW,CAAC,WAAW,EAAE;6BAChD,CAAC,CACH,CAAC;wBACJ,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,sBAAO,aAAa,CAAC,IAAI,CAAC,UAAC,MAAsB;;wBAAtB,uBAAA,EAAA,sBAAsB;wBAC/C,sEAAsE;wBACtE,kEAAkE;wBAClE,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;4BACrB,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS;gCACjC,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;oCAC7D,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,GAAG;wCAC9B,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;4CAChE,WAAW,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;wCAC1D,CAAC;oCACH,CAAC,CAAC,CAAC;gCACL,CAAC;4BACH,CAAC,CAAC,CAAC;wBACL,CAAC;wBAED,gCAAgC;wBAChC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;4BACxB,OAAO,MAAM,CAAC;wBAChB,CAAC;wBAED,yEAAyE;wBACzE,gCAAgC;wBAChC,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;4BACnB,oEAAoE;4BACpE,OAAO,MAAM,CAAC;wBAChB,CAAC;wBAED,IAAM,aAAa,GACjB,MAAA,MAAA,KAAK,CAAC,UAAU,0CAAE,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAzB,CAAyB,CAAC,mCAAI,KAAK,CAAC;wBAEpE,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;4BAC1B,OAAO,KAAI,CAAC,uBAAuB,CACjC,KAAK,EACL,uBAAuB,IAAI,aAAa,EACxC,MAAM,EACN,WAAW,CACZ,CAAC;wBACJ,CAAC;wBAED,2EAA2E;wBAC3E,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;4BACvB,OAAO,KAAI,CAAC,mBAAmB,CAC7B,KAAK,CAAC,YAAY,EAClB,uBAAuB,IAAI,aAAa,EACxC,MAAM,EACN,WAAW,CACZ,CAAC;wBACJ,CAAC;oBACH,CAAC,CAAC,EAAC;;;KACJ;IAEO,4CAAuB,GAA/B,UACE,KAAgB,EAChB,uBAAgC,EAChC,MAAa,EACb,WAAwB;QAJ1B,iBAiCC;QA3BC,OAAO,OAAO,CAAC,GAAG,CAChB,MAAM,CAAC,GAAG,CAAC,UAAC,IAAI;YACd,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;gBAClB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,mCAAmC;YACnC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACxB,OAAO,KAAI,CAAC,uBAAuB,CACjC,KAAK,EACL,uBAAuB,EACvB,IAAI,EACJ,WAAW,CACZ,CAAC;YACJ,CAAC;YAED,kDAAkD;YAClD,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;gBACvB,OAAO,KAAI,CAAC,mBAAmB,CAC7B,KAAK,CAAC,YAAY,EAClB,uBAAuB,EACvB,IAAI,EACJ,WAAW,CACZ,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,sFAAsF;IACtF,gEAAgE;IAChE,gEAAgE;IACxD,+CAA0B,GAAlC,UACE,cAAuC,EACvC,WAAwB;QAExB,IAAM,eAAe,GAAG,UACtB,IAAkC,IACd,OAAA,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAApB,CAAoB,CAAC;QAC3C,IAAM,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAE/D,SAAS,mBAAmB,CAC1B,cAAwC;YAExC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;gBAClD,IAAM,SAAO,GAAG,IAAI,GAAG,EAAiB,CAAC;gBACzC,wBAAwB,CAAC,GAAG,CAAC,cAAc,EAAE,SAAO,CAAC,CAAC;gBAEtD,KAAK,CAAC,cAAc,EAAE;oBACpB,SAAS,YAAC,IAAmB,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS;wBAClD,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;4BACjC,SAAS,CAAC,OAAO,CAAC,UAAC,IAAI;gCACrB,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;oCACnD,SAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gCACpB,CAAC;4BACH,CAAC,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;oBACD,cAAc,YAAC,MAA0B,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS;wBAC9D,IAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAChD,SAAS,CAAC,QAAQ,EAAE,sBAAsB,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAE/D,IAAM,kBAAkB,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;wBACzD,IAAI,kBAAkB,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;4BAChC,wFAAwF;4BACxF,2FAA2F;4BAC3F,SAAS,CAAC,OAAO,CAAC,UAAC,IAAI;gCACrB,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;oCACnD,SAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gCACpB,CAAC;4BACH,CAAC,CAAC,CAAC;4BACH,SAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;4BACpB,kBAAkB,CAAC,OAAO,CAAC,UAAC,SAAS;gCACnC,SAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;4BACzB,CAAC,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;YACD,OAAO,wBAAwB,CAAC,GAAG,CAAC,cAAc,CAAE,CAAC;QACvD,CAAC;QACD,OAAO,mBAAmB,CAAC,cAAc,CAAC,CAAC;IAC7C,CAAC;IACH,iBAAC;AAAD,CAAC,AAxeD,IAweC", "sourcesContent": ["import { invariant } from \"../utilities/globals/index.js\";\n\nimport type {\n  DocumentNode,\n  OperationDefinitionNode,\n  SelectionSetNode,\n  SelectionNode,\n  InlineFragmentNode,\n  FragmentDefinitionNode,\n  FieldNode,\n  ASTNode,\n  DirectiveNode,\n  FragmentSpreadNode,\n  ExecutableDefinitionNode,\n} from \"graphql\";\nimport { visit, BREAK, isSelectionNode } from \"graphql\";\n\nimport type { ApolloCache } from \"../cache/index.js\";\nimport type { FragmentMap, StoreObject } from \"../utilities/index.js\";\nimport {\n  argumentsObjectFromField,\n  buildQueryFromSelectionSet,\n  createFragmentMap,\n  getFragmentDefinitions,\n  getMainDefinition,\n  hasDirectives,\n  isField,\n  isInlineFragment,\n  mergeDeep,\n  mergeDeepArray,\n  removeClientSetsFromDocument,\n  resultKeyNameFromField,\n  shouldInclude,\n} from \"../utilities/index.js\";\nimport type { ApolloClient } from \"./ApolloClient.js\";\nimport type { Resolvers, OperationVariables } from \"./types.js\";\nimport type { FetchResult } from \"../link/core/index.js\";\nimport { cacheSlot } from \"../cache/index.js\";\n\nexport type Resolver = (\n  rootValue?: any,\n  args?: any,\n  context?: any,\n  info?: {\n    field: FieldNode;\n    fragmentMap: FragmentMap;\n  }\n) => any;\n\nexport type VariableMap = { [name: string]: any };\n\nexport type FragmentMatcher = (\n  rootValue: any,\n  typeCondition: string,\n  context: any\n) => boolean;\n\nexport type ExecContext = {\n  fragmentMap: FragmentMap;\n  context: any;\n  variables: VariableMap;\n  fragmentMatcher: FragmentMatcher;\n  defaultOperationType: string;\n  exportedVariables: Record<string, any>;\n  onlyRunForcedResolvers: boolean;\n  selectionsToResolve: Set<SelectionNode>;\n};\n\nexport type LocalStateOptions<TCacheShape> = {\n  cache: ApolloCache<TCacheShape>;\n  client?: ApolloClient<TCacheShape>;\n  resolvers?: Resolvers | Resolvers[];\n  fragmentMatcher?: FragmentMatcher;\n};\n\nexport class LocalState<TCacheShape> {\n  private cache: ApolloCache<TCacheShape>;\n  private client?: ApolloClient<TCacheShape>;\n  private resolvers?: Resolvers;\n  private fragmentMatcher?: FragmentMatcher;\n  private selectionsToResolveCache = new WeakMap<\n    ExecutableDefinitionNode,\n    Set<SelectionNode>\n  >();\n\n  constructor({\n    cache,\n    client,\n    resolvers,\n    fragmentMatcher,\n  }: LocalStateOptions<TCacheShape>) {\n    this.cache = cache;\n\n    if (client) {\n      this.client = client;\n    }\n\n    if (resolvers) {\n      this.addResolvers(resolvers);\n    }\n\n    if (fragmentMatcher) {\n      this.setFragmentMatcher(fragmentMatcher);\n    }\n  }\n\n  public addResolvers(resolvers: Resolvers | Resolvers[]) {\n    this.resolvers = this.resolvers || {};\n    if (Array.isArray(resolvers)) {\n      resolvers.forEach((resolverGroup) => {\n        this.resolvers = mergeDeep(this.resolvers, resolverGroup);\n      });\n    } else {\n      this.resolvers = mergeDeep(this.resolvers, resolvers);\n    }\n  }\n\n  public setResolvers(resolvers: Resolvers | Resolvers[]) {\n    this.resolvers = {};\n    this.addResolvers(resolvers);\n  }\n\n  public getResolvers() {\n    return this.resolvers || {};\n  }\n\n  // Run local client resolvers against the incoming query and remote data.\n  // Locally resolved field values are merged with the incoming remote data,\n  // and returned. Note that locally resolved fields will overwrite\n  // remote data using the same field name.\n  public async runResolvers<TData>({\n    document,\n    remoteResult,\n    context,\n    variables,\n    onlyRunForcedResolvers = false,\n  }: {\n    document: DocumentNode | null;\n    remoteResult: FetchResult<TData>;\n    context?: Record<string, any>;\n    variables?: Record<string, any>;\n    onlyRunForcedResolvers?: boolean;\n  }): Promise<FetchResult<TData>> {\n    if (document) {\n      return this.resolveDocument(\n        document,\n        remoteResult.data,\n        context,\n        variables,\n        this.fragmentMatcher,\n        onlyRunForcedResolvers\n      ).then((localResult) => ({\n        ...remoteResult,\n        data: localResult.result,\n      }));\n    }\n\n    return remoteResult;\n  }\n\n  public setFragmentMatcher(fragmentMatcher: FragmentMatcher) {\n    this.fragmentMatcher = fragmentMatcher;\n  }\n\n  public getFragmentMatcher(): FragmentMatcher | undefined {\n    return this.fragmentMatcher;\n  }\n\n  // Client queries contain everything in the incoming document (if a @client\n  // directive is found).\n  public clientQuery(document: DocumentNode) {\n    if (hasDirectives([\"client\"], document)) {\n      if (this.resolvers) {\n        return document;\n      }\n    }\n    return null;\n  }\n\n  // Server queries are stripped of all @client based selection sets.\n  public serverQuery(document: DocumentNode) {\n    return removeClientSetsFromDocument(document);\n  }\n\n  public prepareContext(context?: Record<string, any>) {\n    const { cache } = this;\n    return {\n      ...context,\n      cache,\n      // Getting an entry's cache key is useful for local state resolvers.\n      getCacheKey(obj: StoreObject) {\n        return cache.identify(obj);\n      },\n    };\n  }\n\n  // To support `@client @export(as: \"someVar\")` syntax, we'll first resolve\n  // @client @export fields locally, then pass the resolved values back to be\n  // used alongside the original operation variables.\n  public async addExportedVariables<TVars extends OperationVariables>(\n    document: DocumentNode,\n    variables: TVars = {} as TVars,\n    context = {}\n  ): /* returns at least the variables that were passed in */ Promise<TVars> {\n    if (document) {\n      return this.resolveDocument(\n        document,\n        this.buildRootValueFromCache(document, variables) || {},\n        this.prepareContext(context),\n        variables\n      ).then((data) => ({\n        ...variables,\n        ...data.exportedVariables,\n      }));\n    }\n\n    return {\n      ...variables,\n    };\n  }\n\n  public shouldForceResolvers(document: ASTNode) {\n    let forceResolvers = false;\n    visit(document, {\n      Directive: {\n        enter(node) {\n          if (node.name.value === \"client\" && node.arguments) {\n            forceResolvers = node.arguments.some(\n              (arg) =>\n                arg.name.value === \"always\" &&\n                arg.value.kind === \"BooleanValue\" &&\n                arg.value.value === true\n            );\n            if (forceResolvers) {\n              return BREAK;\n            }\n          }\n        },\n      },\n    });\n    return forceResolvers;\n  }\n\n  // Query the cache and return matching data.\n  private buildRootValueFromCache(\n    document: DocumentNode,\n    variables?: Record<string, any>\n  ) {\n    return this.cache.diff({\n      query: buildQueryFromSelectionSet(document),\n      variables,\n      returnPartialData: true,\n      optimistic: false,\n    }).result;\n  }\n\n  private async resolveDocument<TData>(\n    document: DocumentNode,\n    rootValue: TData,\n    context: any = {},\n    variables: VariableMap = {},\n    fragmentMatcher: FragmentMatcher = () => true,\n    onlyRunForcedResolvers: boolean = false\n  ) {\n    const mainDefinition = getMainDefinition(\n      document\n    ) as OperationDefinitionNode;\n    const fragments = getFragmentDefinitions(document);\n    const fragmentMap = createFragmentMap(fragments);\n    const selectionsToResolve = this.collectSelectionsToResolve(\n      mainDefinition,\n      fragmentMap\n    );\n\n    const definitionOperation = mainDefinition.operation;\n\n    const defaultOperationType =\n      definitionOperation ?\n        definitionOperation.charAt(0).toUpperCase() +\n        definitionOperation.slice(1)\n      : \"Query\";\n\n    const { cache, client } = this;\n    const execContext: ExecContext = {\n      fragmentMap,\n      context: {\n        ...context,\n        cache,\n        client,\n      },\n      variables,\n      fragmentMatcher,\n      defaultOperationType,\n      exportedVariables: {},\n      selectionsToResolve,\n      onlyRunForcedResolvers,\n    };\n    const isClientFieldDescendant = false;\n\n    return this.resolveSelectionSet(\n      mainDefinition.selectionSet,\n      isClientFieldDescendant,\n      rootValue,\n      execContext\n    ).then((result) => ({\n      result,\n      exportedVariables: execContext.exportedVariables,\n    }));\n  }\n\n  private async resolveSelectionSet<TData>(\n    selectionSet: SelectionSetNode,\n    isClientFieldDescendant: boolean,\n    rootValue: TData,\n    execContext: ExecContext\n  ) {\n    const { fragmentMap, context, variables } = execContext;\n    const resultsToMerge: TData[] = [rootValue];\n\n    const execute = async (selection: SelectionNode): Promise<void> => {\n      if (\n        !isClientFieldDescendant &&\n        !execContext.selectionsToResolve.has(selection)\n      ) {\n        // Skip selections without @client directives\n        // (still processing if one of the ancestors or one of the child fields has @client directive)\n        return;\n      }\n      if (!shouldInclude(selection, variables)) {\n        // Skip this entirely.\n        return;\n      }\n\n      if (isField(selection)) {\n        return this.resolveField(\n          selection,\n          isClientFieldDescendant,\n          rootValue,\n          execContext\n        ).then((fieldResult) => {\n          if (typeof fieldResult !== \"undefined\") {\n            resultsToMerge.push({\n              [resultKeyNameFromField(selection)]: fieldResult,\n            } as TData);\n          }\n        });\n      }\n\n      let fragment: InlineFragmentNode | FragmentDefinitionNode;\n\n      if (isInlineFragment(selection)) {\n        fragment = selection;\n      } else {\n        // This is a named fragment.\n        fragment = fragmentMap[selection.name.value];\n        invariant(fragment, `No fragment named %s`, selection.name.value);\n      }\n\n      if (fragment && fragment.typeCondition) {\n        const typeCondition = fragment.typeCondition.name.value;\n        if (execContext.fragmentMatcher(rootValue, typeCondition, context)) {\n          return this.resolveSelectionSet(\n            fragment.selectionSet,\n            isClientFieldDescendant,\n            rootValue,\n            execContext\n          ).then((fragmentResult) => {\n            resultsToMerge.push(fragmentResult);\n          });\n        }\n      }\n    };\n\n    return Promise.all(selectionSet.selections.map(execute)).then(function () {\n      return mergeDeepArray(resultsToMerge);\n    });\n  }\n\n  private async resolveField(\n    field: FieldNode,\n    isClientFieldDescendant: boolean,\n    rootValue: any,\n    execContext: ExecContext\n  ): Promise<any> {\n    if (!rootValue) {\n      return null;\n    }\n\n    const { variables } = execContext;\n    const fieldName = field.name.value;\n    const aliasedFieldName = resultKeyNameFromField(field);\n    const aliasUsed = fieldName !== aliasedFieldName;\n    const defaultResult = rootValue[aliasedFieldName] || rootValue[fieldName];\n    let resultPromise = Promise.resolve(defaultResult);\n\n    // Usually all local resolvers are run when passing through here, but\n    // if we've specifically identified that we only want to run forced\n    // resolvers (that is, resolvers for fields marked with\n    // `@client(always: true)`), then we'll skip running non-forced resolvers.\n    if (\n      !execContext.onlyRunForcedResolvers ||\n      this.shouldForceResolvers(field)\n    ) {\n      const resolverType =\n        rootValue.__typename || execContext.defaultOperationType;\n      const resolverMap = this.resolvers && this.resolvers[resolverType];\n      if (resolverMap) {\n        const resolve = resolverMap[aliasUsed ? fieldName : aliasedFieldName];\n        if (resolve) {\n          resultPromise = Promise.resolve(\n            // In case the resolve function accesses reactive variables,\n            // set cacheSlot to the current cache instance.\n            cacheSlot.withValue(this.cache, resolve, [\n              rootValue,\n              argumentsObjectFromField(field, variables),\n              execContext.context,\n              { field, fragmentMap: execContext.fragmentMap },\n            ])\n          );\n        }\n      }\n    }\n\n    return resultPromise.then((result = defaultResult) => {\n      // If an @export directive is associated with the current field, store\n      // the `as` export variable name and current result for later use.\n      if (field.directives) {\n        field.directives.forEach((directive) => {\n          if (directive.name.value === \"export\" && directive.arguments) {\n            directive.arguments.forEach((arg) => {\n              if (arg.name.value === \"as\" && arg.value.kind === \"StringValue\") {\n                execContext.exportedVariables[arg.value.value] = result;\n              }\n            });\n          }\n        });\n      }\n\n      // Handle all scalar types here.\n      if (!field.selectionSet) {\n        return result;\n      }\n\n      // From here down, the field has a selection set, which means it's trying\n      // to query a GraphQLObjectType.\n      if (result == null) {\n        // Basically any field in a GraphQL response can be null, or missing\n        return result;\n      }\n\n      const isClientField =\n        field.directives?.some((d) => d.name.value === \"client\") ?? false;\n\n      if (Array.isArray(result)) {\n        return this.resolveSubSelectedArray(\n          field,\n          isClientFieldDescendant || isClientField,\n          result,\n          execContext\n        );\n      }\n\n      // Returned value is an object, and the query has a sub-selection. Recurse.\n      if (field.selectionSet) {\n        return this.resolveSelectionSet(\n          field.selectionSet,\n          isClientFieldDescendant || isClientField,\n          result,\n          execContext\n        );\n      }\n    });\n  }\n\n  private resolveSubSelectedArray(\n    field: FieldNode,\n    isClientFieldDescendant: boolean,\n    result: any[],\n    execContext: ExecContext\n  ): any {\n    return Promise.all(\n      result.map((item) => {\n        if (item === null) {\n          return null;\n        }\n\n        // This is a nested array, recurse.\n        if (Array.isArray(item)) {\n          return this.resolveSubSelectedArray(\n            field,\n            isClientFieldDescendant,\n            item,\n            execContext\n          );\n        }\n\n        // This is an object, run the selection set on it.\n        if (field.selectionSet) {\n          return this.resolveSelectionSet(\n            field.selectionSet,\n            isClientFieldDescendant,\n            item,\n            execContext\n          );\n        }\n      })\n    );\n  }\n\n  // Collect selection nodes on paths from document root down to all @client directives.\n  // This function takes into account transitive fragment spreads.\n  // Complexity equals to a single `visit` over the full document.\n  private collectSelectionsToResolve(\n    mainDefinition: OperationDefinitionNode,\n    fragmentMap: FragmentMap\n  ): Set<SelectionNode> {\n    const isSingleASTNode = (\n      node: ASTNode | readonly ASTNode[]\n    ): node is ASTNode => !Array.isArray(node);\n    const selectionsToResolveCache = this.selectionsToResolveCache;\n\n    function collectByDefinition(\n      definitionNode: ExecutableDefinitionNode\n    ): Set<SelectionNode> {\n      if (!selectionsToResolveCache.has(definitionNode)) {\n        const matches = new Set<SelectionNode>();\n        selectionsToResolveCache.set(definitionNode, matches);\n\n        visit(definitionNode, {\n          Directive(node: DirectiveNode, _, __, ___, ancestors) {\n            if (node.name.value === \"client\") {\n              ancestors.forEach((node) => {\n                if (isSingleASTNode(node) && isSelectionNode(node)) {\n                  matches.add(node);\n                }\n              });\n            }\n          },\n          FragmentSpread(spread: FragmentSpreadNode, _, __, ___, ancestors) {\n            const fragment = fragmentMap[spread.name.value];\n            invariant(fragment, `No fragment named %s`, spread.name.value);\n\n            const fragmentSelections = collectByDefinition(fragment);\n            if (fragmentSelections.size > 0) {\n              // Fragment for this spread contains @client directive (either directly or transitively)\n              // Collect selection nodes on paths from the root down to fields with the @client directive\n              ancestors.forEach((node) => {\n                if (isSingleASTNode(node) && isSelectionNode(node)) {\n                  matches.add(node);\n                }\n              });\n              matches.add(spread);\n              fragmentSelections.forEach((selection) => {\n                matches.add(selection);\n              });\n            }\n          },\n        });\n      }\n      return selectionsToResolveCache.get(definitionNode)!;\n    }\n    return collectByDefinition(mainDefinition);\n  }\n}\n"]}