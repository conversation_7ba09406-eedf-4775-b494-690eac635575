{"version": 3, "sources": ["../../lib0/broadcastchannel.js", "../../y-protocols/sync.js", "../../y-protocols/auth.js", "../../y-protocols/awareness.js", "../../lib0/url.js", "../../y-websocket/src/y-websocket.js"], "sourcesContent": ["/* eslint-env browser */\n\n/**\n * Helpers for cross-tab communication using broadcastchannel with LocalStorage fallback.\n *\n * ```js\n * // In browser window A:\n * broadcastchannel.subscribe('my events', data => console.log(data))\n * broadcastchannel.publish('my events', 'Hello world!') // => A: 'Hello world!' fires synchronously in same tab\n *\n * // In browser window B:\n * broadcastchannel.publish('my events', 'hello from tab B') // => A: 'hello from tab B'\n * ```\n *\n * @module broadcastchannel\n */\n\n// @todo before next major: use Uint8Array instead as buffer object\n\nimport * as map from './map.js'\nimport * as set from './set.js'\nimport * as buffer from './buffer.js'\nimport * as storage from './storage.js'\n\n/**\n * @typedef {Object} Channel\n * @property {Set<function(any, any):any>} Channel.subs\n * @property {any} Channel.bc\n */\n\n/**\n * @type {Map<string, Channel>}\n */\nconst channels = new Map()\n\n/* c8 ignore start */\nclass LocalStoragePolyfill {\n  /**\n   * @param {string} room\n   */\n  constructor (room) {\n    this.room = room\n    /**\n     * @type {null|function({data:ArrayBuffer}):void}\n     */\n    this.onmessage = null\n    /**\n     * @param {any} e\n     */\n    this._onChange = e => e.key === room && this.onmessage !== null && this.onmessage({ data: buffer.fromBase64(e.newValue || '') })\n    storage.onChange(this._onChange)\n  }\n\n  /**\n   * @param {ArrayBuffer} buf\n   */\n  postMessage (buf) {\n    storage.varStorage.setItem(this.room, buffer.toBase64(buffer.createUint8ArrayFromArrayBuffer(buf)))\n  }\n\n  close () {\n    storage.offChange(this._onChange)\n  }\n}\n/* c8 ignore stop */\n\n// Use BroadcastChannel or Polyfill\n/* c8 ignore next */\nconst BC = typeof BroadcastChannel === 'undefined' ? LocalStoragePolyfill : BroadcastChannel\n\n/**\n * @param {string} room\n * @return {Channel}\n */\nconst getChannel = room =>\n  map.setIfUndefined(channels, room, () => {\n    const subs = set.create()\n    const bc = new BC(room)\n    /**\n     * @param {{data:ArrayBuffer}} e\n     */\n    /* c8 ignore next */\n    bc.onmessage = e => subs.forEach(sub => sub(e.data, 'broadcastchannel'))\n    return {\n      bc, subs\n    }\n  })\n\n/**\n * Subscribe to global `publish` events.\n *\n * @function\n * @param {string} room\n * @param {function(any, any):any} f\n */\nexport const subscribe = (room, f) => {\n  getChannel(room).subs.add(f)\n  return f\n}\n\n/**\n * Unsubscribe from `publish` global events.\n *\n * @function\n * @param {string} room\n * @param {function(any, any):any} f\n */\nexport const unsubscribe = (room, f) => {\n  const channel = getChannel(room)\n  const unsubscribed = channel.subs.delete(f)\n  if (unsubscribed && channel.subs.size === 0) {\n    channel.bc.close()\n    channels.delete(room)\n  }\n  return unsubscribed\n}\n\n/**\n * Publish data to all subscribers (including subscribers on this tab)\n *\n * @function\n * @param {string} room\n * @param {any} data\n * @param {any} [origin]\n */\nexport const publish = (room, data, origin = null) => {\n  const c = getChannel(room)\n  c.bc.postMessage(data)\n  c.subs.forEach(sub => sub(data, origin))\n}\n", "/**\n * @module sync-protocol\n */\n\nimport * as encoding from 'lib0/encoding'\nimport * as decoding from 'lib0/decoding'\nimport * as Y from 'yjs'\n\n/**\n * @typedef {Map<number, number>} StateMap\n */\n\n/**\n * Core Yjs defines two message types:\n * • YjsSyncStep1: Includes the State Set of the sending client. When received, the client should reply with YjsSyncStep2.\n * • YjsSyncStep2: Includes all missing structs and the complete delete set. When received, the client is assured that it\n *   received all information from the remote client.\n *\n * In a peer-to-peer network, you may want to introduce a SyncDone message type. Both parties should initiate the connection\n * with SyncStep1. When a client received SyncStep2, it should reply with SyncDone. When the local client received both\n * SyncStep2 and SyncDone, it is assured that it is synced to the remote client.\n *\n * In a client-server model, you want to handle this differently: The client should initiate the connection with SyncStep1.\n * When the server receives SyncStep1, it should reply with SyncStep2 immediately followed by SyncStep1. The client replies\n * with SyncStep2 when it receives SyncStep1. Optionally the server may send a SyncDone after it received SyncStep2, so the\n * client knows that the sync is finished.  There are two reasons for this more elaborated sync model: 1. This protocol can\n * easily be implemented on top of http and websockets. 2. The server should only reply to requests, and not initiate them.\n * Therefore it is necessary that the client initiates the sync.\n *\n * Construction of a message:\n * [messageType : varUint, message definition..]\n *\n * Note: A message does not include information about the room name. This must to be handled by the upper layer protocol!\n *\n * stringify[messageType] stringifies a message definition (messageType is already read from the bufffer)\n */\n\nexport const messageYjsSyncStep1 = 0\nexport const messageYjsSyncStep2 = 1\nexport const messageYjsUpdate = 2\n\n/**\n * Create a sync step 1 message based on the state of the current shared document.\n *\n * @param {encoding.Encoder} encoder\n * @param {Y.Doc} doc\n */\nexport const writeSyncStep1 = (encoder, doc) => {\n  encoding.writeVarUint(encoder, messageYjsSyncStep1)\n  const sv = Y.encodeStateVector(doc)\n  encoding.writeVarUint8Array(encoder, sv)\n}\n\n/**\n * @param {encoding.Encoder} encoder\n * @param {Y.Doc} doc\n * @param {Uint8Array} [encodedStateVector]\n */\nexport const writeSyncStep2 = (encoder, doc, encodedStateVector) => {\n  encoding.writeVarUint(encoder, messageYjsSyncStep2)\n  encoding.writeVarUint8Array(encoder, Y.encodeStateAsUpdate(doc, encodedStateVector))\n}\n\n/**\n * Read SyncStep1 message and reply with SyncStep2.\n *\n * @param {decoding.Decoder} decoder The reply to the received message\n * @param {encoding.Encoder} encoder The received message\n * @param {Y.Doc} doc\n */\nexport const readSyncStep1 = (decoder, encoder, doc) =>\n  writeSyncStep2(encoder, doc, decoding.readVarUint8Array(decoder))\n\n/**\n * Read and apply Structs and then DeleteStore to a y instance.\n *\n * @param {decoding.Decoder} decoder\n * @param {Y.Doc} doc\n * @param {any} transactionOrigin\n */\nexport const readSyncStep2 = (decoder, doc, transactionOrigin) => {\n  try {\n    Y.applyUpdate(doc, decoding.readVarUint8Array(decoder), transactionOrigin)\n  } catch (error) {\n    // This catches errors that are thrown by event handlers\n    console.error('Caught error while handling a Yjs update', error)\n  }\n}\n\n/**\n * @param {encoding.Encoder} encoder\n * @param {Uint8Array} update\n */\nexport const writeUpdate = (encoder, update) => {\n  encoding.writeVarUint(encoder, messageYjsUpdate)\n  encoding.writeVarUint8Array(encoder, update)\n}\n\n/**\n * Read and apply Structs and then DeleteStore to a y instance.\n *\n * @param {decoding.Decoder} decoder\n * @param {Y.Doc} doc\n * @param {any} transactionOrigin\n */\nexport const readUpdate = readSyncStep2\n\n/**\n * @param {decoding.Decoder} decoder A message received from another client\n * @param {encoding.Encoder} encoder The reply message. Does not need to be sent if empty.\n * @param {Y.Doc} doc\n * @param {any} transactionOrigin\n */\nexport const readSyncMessage = (decoder, encoder, doc, transactionOrigin) => {\n  const messageType = decoding.readVarUint(decoder)\n  switch (messageType) {\n    case messageYjsSyncStep1:\n      readSyncStep1(decoder, encoder, doc)\n      break\n    case messageYjsSyncStep2:\n      readSyncStep2(decoder, doc, transactionOrigin)\n      break\n    case messageYjsUpdate:\n      readUpdate(decoder, doc, transactionOrigin)\n      break\n    default:\n      throw new Error('Unknown message type')\n  }\n  return messageType\n}\n", "\nimport * as Y from 'yjs' // eslint-disable-line\nimport * as encoding from 'lib0/encoding'\nimport * as decoding from 'lib0/decoding'\n\nexport const messagePermissionDenied = 0\n\n/**\n * @param {encoding.Encoder} encoder\n * @param {string} reason\n */\nexport const writePermissionDenied = (encoder, reason) => {\n  encoding.writeVarUint(encoder, messagePermissionDenied)\n  encoding.writeVarString(encoder, reason)\n}\n\n/**\n * @callback PermissionDeniedHandler\n * @param {any} y\n * @param {string} reason\n */\n\n/**\n *\n * @param {decoding.Decoder} decoder\n * @param {Y.Doc} y\n * @param {PermissionDeniedHandler} permissionDeniedHandler\n */\nexport const readAuthMessage = (decoder, y, permissionDeniedHandler) => {\n  switch (decoding.readVarUint(decoder)) {\n    case messagePermissionDenied: permissionDeniedHandler(y, decoding.readVarString(decoder))\n  }\n}\n", "/**\n * @module awareness-protocol\n */\n\nimport * as encoding from 'lib0/encoding'\nimport * as decoding from 'lib0/decoding'\nimport * as time from 'lib0/time'\nimport * as math from 'lib0/math'\nimport { Observable } from 'lib0/observable'\nimport * as f from 'lib0/function'\nimport * as Y from 'yjs' // eslint-disable-line\n\nexport const outdatedTimeout = 30000\n\n/**\n * @typedef {Object} MetaClientState\n * @property {number} MetaClientState.clock\n * @property {number} MetaClientState.lastUpdated unix timestamp\n */\n\n/**\n * The Awareness class implements a simple shared state protocol that can be used for non-persistent data like awareness information\n * (cursor, username, status, ..). Each client can update its own local state and listen to state changes of\n * remote clients. Every client may set a state of a remote peer to `null` to mark the client as offline.\n *\n * Each client is identified by a unique client id (something we borrow from `doc.clientID`). A client can override\n * its own state by propagating a message with an increasing timestamp (`clock`). If such a message is received, it is\n * applied if the known state of that client is older than the new state (`clock < newClock`). If a client thinks that\n * a remote client is offline, it may propagate a message with\n * `{ clock: currentClientClock, state: null, client: remoteClient }`. If such a\n * message is received, and the known clock of that client equals the received clock, it will override the state with `null`.\n *\n * Before a client disconnects, it should propagate a `null` state with an updated clock.\n *\n * Awareness states must be updated every 30 seconds. Otherwise the Awareness instance will delete the client state.\n *\n * @extends {Observable<string>}\n */\nexport class Awareness extends Observable {\n  /**\n   * @param {Y.Doc} doc\n   */\n  constructor (doc) {\n    super()\n    this.doc = doc\n    /**\n     * @type {number}\n     */\n    this.clientID = doc.clientID\n    /**\n     * Maps from client id to client state\n     * @type {Map<number, Object<string, any>>}\n     */\n    this.states = new Map()\n    /**\n     * @type {Map<number, MetaClientState>}\n     */\n    this.meta = new Map()\n    this._checkInterval = /** @type {any} */ (setInterval(() => {\n      const now = time.getUnixTime()\n      if (this.getLocalState() !== null && (outdatedTimeout / 2 <= now - /** @type {{lastUpdated:number}} */ (this.meta.get(this.clientID)).lastUpdated)) {\n        // renew local clock\n        this.setLocalState(this.getLocalState())\n      }\n      /**\n       * @type {Array<number>}\n       */\n      const remove = []\n      this.meta.forEach((meta, clientid) => {\n        if (clientid !== this.clientID && outdatedTimeout <= now - meta.lastUpdated && this.states.has(clientid)) {\n          remove.push(clientid)\n        }\n      })\n      if (remove.length > 0) {\n        removeAwarenessStates(this, remove, 'timeout')\n      }\n    }, math.floor(outdatedTimeout / 10)))\n    doc.on('destroy', () => {\n      this.destroy()\n    })\n    this.setLocalState({})\n  }\n\n  destroy () {\n    this.emit('destroy', [this])\n    this.setLocalState(null)\n    super.destroy()\n    clearInterval(this._checkInterval)\n  }\n\n  /**\n   * @return {Object<string,any>|null}\n   */\n  getLocalState () {\n    return this.states.get(this.clientID) || null\n  }\n\n  /**\n   * @param {Object<string,any>|null} state\n   */\n  setLocalState (state) {\n    const clientID = this.clientID\n    const currLocalMeta = this.meta.get(clientID)\n    const clock = currLocalMeta === undefined ? 0 : currLocalMeta.clock + 1\n    const prevState = this.states.get(clientID)\n    if (state === null) {\n      this.states.delete(clientID)\n    } else {\n      this.states.set(clientID, state)\n    }\n    this.meta.set(clientID, {\n      clock,\n      lastUpdated: time.getUnixTime()\n    })\n    const added = []\n    const updated = []\n    const filteredUpdated = []\n    const removed = []\n    if (state === null) {\n      removed.push(clientID)\n    } else if (prevState == null) {\n      if (state != null) {\n        added.push(clientID)\n      }\n    } else {\n      updated.push(clientID)\n      if (!f.equalityDeep(prevState, state)) {\n        filteredUpdated.push(clientID)\n      }\n    }\n    if (added.length > 0 || filteredUpdated.length > 0 || removed.length > 0) {\n      this.emit('change', [{ added, updated: filteredUpdated, removed }, 'local'])\n    }\n    this.emit('update', [{ added, updated, removed }, 'local'])\n  }\n\n  /**\n   * @param {string} field\n   * @param {any} value\n   */\n  setLocalStateField (field, value) {\n    const state = this.getLocalState()\n    if (state !== null) {\n      this.setLocalState({\n        ...state,\n        [field]: value\n      })\n    }\n  }\n\n  /**\n   * @return {Map<number,Object<string,any>>}\n   */\n  getStates () {\n    return this.states\n  }\n}\n\n/**\n * Mark (remote) clients as inactive and remove them from the list of active peers.\n * This change will be propagated to remote clients.\n *\n * @param {Awareness} awareness\n * @param {Array<number>} clients\n * @param {any} origin\n */\nexport const removeAwarenessStates = (awareness, clients, origin) => {\n  const removed = []\n  for (let i = 0; i < clients.length; i++) {\n    const clientID = clients[i]\n    if (awareness.states.has(clientID)) {\n      awareness.states.delete(clientID)\n      if (clientID === awareness.clientID) {\n        const curMeta = /** @type {MetaClientState} */ (awareness.meta.get(clientID))\n        awareness.meta.set(clientID, {\n          clock: curMeta.clock + 1,\n          lastUpdated: time.getUnixTime()\n        })\n      }\n      removed.push(clientID)\n    }\n  }\n  if (removed.length > 0) {\n    awareness.emit('change', [{ added: [], updated: [], removed }, origin])\n    awareness.emit('update', [{ added: [], updated: [], removed }, origin])\n  }\n}\n\n/**\n * @param {Awareness} awareness\n * @param {Array<number>} clients\n * @return {Uint8Array}\n */\nexport const encodeAwarenessUpdate = (awareness, clients, states = awareness.states) => {\n  const len = clients.length\n  const encoder = encoding.createEncoder()\n  encoding.writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    const clientID = clients[i]\n    const state = states.get(clientID) || null\n    const clock = /** @type {MetaClientState} */ (awareness.meta.get(clientID)).clock\n    encoding.writeVarUint(encoder, clientID)\n    encoding.writeVarUint(encoder, clock)\n    encoding.writeVarString(encoder, JSON.stringify(state))\n  }\n  return encoding.toUint8Array(encoder)\n}\n\n/**\n * Modify the content of an awareness update before re-encoding it to an awareness update.\n *\n * This might be useful when you have a central server that wants to ensure that clients\n * cant hijack somebody elses identity.\n *\n * @param {Uint8Array} update\n * @param {function(any):any} modify\n * @return {Uint8Array}\n */\nexport const modifyAwarenessUpdate = (update, modify) => {\n  const decoder = decoding.createDecoder(update)\n  const encoder = encoding.createEncoder()\n  const len = decoding.readVarUint(decoder)\n  encoding.writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    const clientID = decoding.readVarUint(decoder)\n    const clock = decoding.readVarUint(decoder)\n    const state = JSON.parse(decoding.readVarString(decoder))\n    const modifiedState = modify(state)\n    encoding.writeVarUint(encoder, clientID)\n    encoding.writeVarUint(encoder, clock)\n    encoding.writeVarString(encoder, JSON.stringify(modifiedState))\n  }\n  return encoding.toUint8Array(encoder)\n}\n\n/**\n * @param {Awareness} awareness\n * @param {Uint8Array} update\n * @param {any} origin This will be added to the emitted change event\n */\nexport const applyAwarenessUpdate = (awareness, update, origin) => {\n  const decoder = decoding.createDecoder(update)\n  const timestamp = time.getUnixTime()\n  const added = []\n  const updated = []\n  const filteredUpdated = []\n  const removed = []\n  const len = decoding.readVarUint(decoder)\n  for (let i = 0; i < len; i++) {\n    const clientID = decoding.readVarUint(decoder)\n    let clock = decoding.readVarUint(decoder)\n    const state = JSON.parse(decoding.readVarString(decoder))\n    const clientMeta = awareness.meta.get(clientID)\n    const prevState = awareness.states.get(clientID)\n    const currClock = clientMeta === undefined ? 0 : clientMeta.clock\n    if (currClock < clock || (currClock === clock && state === null && awareness.states.has(clientID))) {\n      if (state === null) {\n        // never let a remote client remove this local state\n        if (clientID === awareness.clientID && awareness.getLocalState() != null) {\n          // remote client removed the local state. Do not remote state. Broadcast a message indicating\n          // that this client still exists by increasing the clock\n          clock++\n        } else {\n          awareness.states.delete(clientID)\n        }\n      } else {\n        awareness.states.set(clientID, state)\n      }\n      awareness.meta.set(clientID, {\n        clock,\n        lastUpdated: timestamp\n      })\n      if (clientMeta === undefined && state !== null) {\n        added.push(clientID)\n      } else if (clientMeta !== undefined && state === null) {\n        removed.push(clientID)\n      } else if (state !== null) {\n        if (!f.equalityDeep(state, prevState)) {\n          filteredUpdated.push(clientID)\n        }\n        updated.push(clientID)\n      }\n    }\n  }\n  if (added.length > 0 || filteredUpdated.length > 0 || removed.length > 0) {\n    awareness.emit('change', [{\n      added, updated: filteredUpdated, removed\n    }, origin])\n  }\n  if (added.length > 0 || updated.length > 0 || removed.length > 0) {\n    awareness.emit('update', [{\n      added, updated, removed\n    }, origin])\n  }\n}\n", "/**\n * Utility module to work with urls.\n *\n * @module url\n */\n\nimport * as object from './object.js'\n\n/**\n * Parse query parameters from an url.\n *\n * @param {string} url\n * @return {Object<string,string>}\n */\nexport const decodeQueryParams = url => {\n  /**\n   * @type {Object<string,string>}\n   */\n  const query = {}\n  const urlQuerySplit = url.split('?')\n  const pairs = urlQuerySplit[urlQuerySplit.length - 1].split('&')\n  for (let i = 0; i < pairs.length; i++) {\n    const item = pairs[i]\n    if (item.length > 0) {\n      const pair = item.split('=')\n      query[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '')\n    }\n  }\n  return query\n}\n\n/**\n * @param {Object<string,string>} params\n * @return {string}\n */\nexport const encodeQueryParams = params =>\n  object.map(params, (val, key) => `${encodeURIComponent(key)}=${encodeURIComponent(val)}`).join('&')\n", "/**\n * @module provider/websocket\n */\n\n/* eslint-env browser */\n\nimport * as Y from 'yjs' // eslint-disable-line\nimport * as bc from 'lib0/broadcastchannel'\nimport * as time from 'lib0/time'\nimport * as encoding from 'lib0/encoding'\nimport * as decoding from 'lib0/decoding'\nimport * as syncProtocol from 'y-protocols/sync'\nimport * as authProtocol from 'y-protocols/auth'\nimport * as awarenessProtocol from 'y-protocols/awareness'\nimport { ObservableV2 } from 'lib0/observable'\nimport * as math from 'lib0/math'\nimport * as url from 'lib0/url'\nimport * as env from 'lib0/environment'\n\nexport const messageSync = 0\nexport const messageQueryAwareness = 3\nexport const messageAwareness = 1\nexport const messageAuth = 2\n\n/**\n *                       encoder,          decoder,          provider,          emitSynced, messageType\n * @type {Array<function(encoding.Encoder, decoding.Decoder, WebsocketProvider, boolean,    number):void>}\n */\nconst messageHandlers = []\n\nmessageHandlers[messageSync] = (\n  encoder,\n  decoder,\n  provider,\n  emitSynced,\n  _messageType\n) => {\n  encoding.writeVarUint(encoder, messageSync)\n  const syncMessageType = syncProtocol.readSyncMessage(\n    decoder,\n    encoder,\n    provider.doc,\n    provider\n  )\n  if (\n    emitSynced && syncMessageType === syncProtocol.messageYjsSyncStep2 &&\n    !provider.synced\n  ) {\n    provider.synced = true\n  }\n}\n\nmessageHandlers[messageQueryAwareness] = (\n  encoder,\n  _decoder,\n  provider,\n  _emitSynced,\n  _messageType\n) => {\n  encoding.writeVarUint(encoder, messageAwareness)\n  encoding.writeVarUint8Array(\n    encoder,\n    awarenessProtocol.encodeAwarenessUpdate(\n      provider.awareness,\n      Array.from(provider.awareness.getStates().keys())\n    )\n  )\n}\n\nmessageHandlers[messageAwareness] = (\n  _encoder,\n  decoder,\n  provider,\n  _emitSynced,\n  _messageType\n) => {\n  awarenessProtocol.applyAwarenessUpdate(\n    provider.awareness,\n    decoding.readVarUint8Array(decoder),\n    provider\n  )\n}\n\nmessageHandlers[messageAuth] = (\n  _encoder,\n  decoder,\n  provider,\n  _emitSynced,\n  _messageType\n) => {\n  authProtocol.readAuthMessage(\n    decoder,\n    provider.doc,\n    (_ydoc, reason) => permissionDeniedHandler(provider, reason)\n  )\n}\n\n// @todo - this should depend on awareness.outdatedTime\nconst messageReconnectTimeout = 30000\n\n/**\n * @param {WebsocketProvider} provider\n * @param {string} reason\n */\nconst permissionDeniedHandler = (provider, reason) =>\n  console.warn(`Permission denied to access ${provider.url}.\\n${reason}`)\n\n/**\n * @param {WebsocketProvider} provider\n * @param {Uint8Array} buf\n * @param {boolean} emitSynced\n * @return {encoding.Encoder}\n */\nconst readMessage = (provider, buf, emitSynced) => {\n  const decoder = decoding.createDecoder(buf)\n  const encoder = encoding.createEncoder()\n  const messageType = decoding.readVarUint(decoder)\n  const messageHandler = provider.messageHandlers[messageType]\n  if (/** @type {any} */ (messageHandler)) {\n    messageHandler(encoder, decoder, provider, emitSynced, messageType)\n  } else {\n    console.error('Unable to compute message')\n  }\n  return encoder\n}\n\n/**\n * Outsource this function so that a new websocket connection is created immediately.\n * I suspect that the `ws.onclose` event is not always fired if there are network issues.\n *\n * @param {WebsocketProvider} provider\n * @param {WebSocket} ws\n * @param {CloseEvent | null} event\n */\nconst closeWebsocketConnection = (provider, ws, event) => {\n  if (ws === provider.ws) {\n    provider.emit('connection-close', [event, provider])\n    provider.ws = null\n    ws.close()\n    provider.wsconnecting = false\n    if (provider.wsconnected) {\n      provider.wsconnected = false\n      provider.synced = false\n      // update awareness (all users except local left)\n      awarenessProtocol.removeAwarenessStates(\n        provider.awareness,\n        Array.from(provider.awareness.getStates().keys()).filter((client) =>\n          client !== provider.doc.clientID\n        ),\n        provider\n      )\n      provider.emit('status', [{\n        status: 'disconnected'\n      }])\n    } else {\n      provider.wsUnsuccessfulReconnects++\n    }\n    // Start with no reconnect timeout and increase timeout by\n    // using exponential backoff starting with 100ms\n    setTimeout(\n      setupWS,\n      math.min(\n        math.pow(2, provider.wsUnsuccessfulReconnects) * 100,\n        provider.maxBackoffTime\n      ),\n      provider\n    )\n  }\n}\n\n/**\n * @param {WebsocketProvider} provider\n */\nconst setupWS = (provider) => {\n  if (provider.shouldConnect && provider.ws === null) {\n    const websocket = new provider._WS(provider.url, provider.protocols)\n    websocket.binaryType = 'arraybuffer'\n    provider.ws = websocket\n    provider.wsconnecting = true\n    provider.wsconnected = false\n    provider.synced = false\n\n    websocket.onmessage = (event) => {\n      provider.wsLastMessageReceived = time.getUnixTime()\n      const encoder = readMessage(provider, new Uint8Array(event.data), true)\n      if (encoding.length(encoder) > 1) {\n        websocket.send(encoding.toUint8Array(encoder))\n      }\n    }\n    websocket.onerror = (event) => {\n      provider.emit('connection-error', [event, provider])\n    }\n    websocket.onclose = (event) => {\n      closeWebsocketConnection(provider, websocket, event)\n    }\n    websocket.onopen = () => {\n      provider.wsLastMessageReceived = time.getUnixTime()\n      provider.wsconnecting = false\n      provider.wsconnected = true\n      provider.wsUnsuccessfulReconnects = 0\n      provider.emit('status', [{\n        status: 'connected'\n      }])\n      // always send sync step 1 when connected\n      const encoder = encoding.createEncoder()\n      encoding.writeVarUint(encoder, messageSync)\n      syncProtocol.writeSyncStep1(encoder, provider.doc)\n      websocket.send(encoding.toUint8Array(encoder))\n      // broadcast local awareness state\n      if (provider.awareness.getLocalState() !== null) {\n        const encoderAwarenessState = encoding.createEncoder()\n        encoding.writeVarUint(encoderAwarenessState, messageAwareness)\n        encoding.writeVarUint8Array(\n          encoderAwarenessState,\n          awarenessProtocol.encodeAwarenessUpdate(provider.awareness, [\n            provider.doc.clientID\n          ])\n        )\n        websocket.send(encoding.toUint8Array(encoderAwarenessState))\n      }\n    }\n    provider.emit('status', [{\n      status: 'connecting'\n    }])\n  }\n}\n\n/**\n * @param {WebsocketProvider} provider\n * @param {ArrayBuffer} buf\n */\nconst broadcastMessage = (provider, buf) => {\n  const ws = provider.ws\n  if (provider.wsconnected && ws && ws.readyState === ws.OPEN) {\n    ws.send(buf)\n  }\n  if (provider.bcconnected) {\n    bc.publish(provider.bcChannel, buf, provider)\n  }\n}\n\n/**\n * Websocket Provider for Yjs. Creates a websocket connection to sync the shared document.\n * The document name is attached to the provided url. I.e. the following example\n * creates a websocket connection to http://localhost:1234/my-document-name\n *\n * @example\n *   import * as Y from 'yjs'\n *   import { WebsocketProvider } from 'y-websocket'\n *   const doc = new Y.Doc()\n *   const provider = new WebsocketProvider('http://localhost:1234', 'my-document-name', doc)\n *\n * @extends {ObservableV2<{ 'connection-close': (event: CloseEvent | null,  provider: WebsocketProvider) => any, 'status': (event: { status: 'connected' | 'disconnected' | 'connecting' }) => any, 'connection-error': (event: Event, provider: WebsocketProvider) => any, 'sync': (state: boolean) => any }>}\n */\nexport class WebsocketProvider extends ObservableV2 {\n  /**\n   * @param {string} serverUrl\n   * @param {string} roomname\n   * @param {Y.Doc} doc\n   * @param {object} opts\n   * @param {boolean} [opts.connect]\n   * @param {awarenessProtocol.Awareness} [opts.awareness]\n   * @param {Object<string,string>} [opts.params] specify url parameters\n   * @param {Array<string>} [opts.protocols] specify websocket protocols\n   * @param {typeof WebSocket} [opts.WebSocketPolyfill] Optionall provide a WebSocket polyfill\n   * @param {number} [opts.resyncInterval] Request server state every `resyncInterval` milliseconds\n   * @param {number} [opts.maxBackoffTime] Maximum amount of time to wait before trying to reconnect (we try to reconnect using exponential backoff)\n   * @param {boolean} [opts.disableBc] Disable cross-tab BroadcastChannel communication\n   */\n  constructor (serverUrl, roomname, doc, {\n    connect = true,\n    awareness = new awarenessProtocol.Awareness(doc),\n    params = {},\n    protocols = [],\n    WebSocketPolyfill = WebSocket,\n    resyncInterval = -1,\n    maxBackoffTime = 2500,\n    disableBc = false\n  } = {}) {\n    super()\n    // ensure that serverUrl does not end with /\n    while (serverUrl[serverUrl.length - 1] === '/') {\n      serverUrl = serverUrl.slice(0, serverUrl.length - 1)\n    }\n    this.serverUrl = serverUrl\n    this.bcChannel = serverUrl + '/' + roomname\n    this.maxBackoffTime = maxBackoffTime\n    /**\n     * The specified url parameters. This can be safely updated. The changed parameters will be used\n     * when a new connection is established.\n     * @type {Object<string,string>}\n     */\n    this.params = params\n    this.protocols = protocols\n    this.roomname = roomname\n    this.doc = doc\n    this._WS = WebSocketPolyfill\n    this.awareness = awareness\n    this.wsconnected = false\n    this.wsconnecting = false\n    this.bcconnected = false\n    this.disableBc = disableBc\n    this.wsUnsuccessfulReconnects = 0\n    this.messageHandlers = messageHandlers.slice()\n    /**\n     * @type {boolean}\n     */\n    this._synced = false\n    /**\n     * @type {WebSocket?}\n     */\n    this.ws = null\n    this.wsLastMessageReceived = 0\n    /**\n     * Whether to connect to other peers or not\n     * @type {boolean}\n     */\n    this.shouldConnect = connect\n\n    /**\n     * @type {number}\n     */\n    this._resyncInterval = 0\n    if (resyncInterval > 0) {\n      this._resyncInterval = /** @type {any} */ (setInterval(() => {\n        if (this.ws && this.ws.readyState === WebSocket.OPEN) {\n          // resend sync step 1\n          const encoder = encoding.createEncoder()\n          encoding.writeVarUint(encoder, messageSync)\n          syncProtocol.writeSyncStep1(encoder, doc)\n          this.ws.send(encoding.toUint8Array(encoder))\n        }\n      }, resyncInterval))\n    }\n\n    /**\n     * @param {ArrayBuffer} data\n     * @param {any} origin\n     */\n    this._bcSubscriber = (data, origin) => {\n      if (origin !== this) {\n        const encoder = readMessage(this, new Uint8Array(data), false)\n        if (encoding.length(encoder) > 1) {\n          bc.publish(this.bcChannel, encoding.toUint8Array(encoder), this)\n        }\n      }\n    }\n    /**\n     * Listens to Yjs updates and sends them to remote peers (ws and broadcastchannel)\n     * @param {Uint8Array} update\n     * @param {any} origin\n     */\n    this._updateHandler = (update, origin) => {\n      if (origin !== this) {\n        const encoder = encoding.createEncoder()\n        encoding.writeVarUint(encoder, messageSync)\n        syncProtocol.writeUpdate(encoder, update)\n        broadcastMessage(this, encoding.toUint8Array(encoder))\n      }\n    }\n    this.doc.on('update', this._updateHandler)\n    /**\n     * @param {any} changed\n     * @param {any} _origin\n     */\n    this._awarenessUpdateHandler = ({ added, updated, removed }, _origin) => {\n      const changedClients = added.concat(updated).concat(removed)\n      const encoder = encoding.createEncoder()\n      encoding.writeVarUint(encoder, messageAwareness)\n      encoding.writeVarUint8Array(\n        encoder,\n        awarenessProtocol.encodeAwarenessUpdate(awareness, changedClients)\n      )\n      broadcastMessage(this, encoding.toUint8Array(encoder))\n    }\n    this._exitHandler = () => {\n      awarenessProtocol.removeAwarenessStates(\n        this.awareness,\n        [doc.clientID],\n        'app closed'\n      )\n    }\n    if (env.isNode && typeof process !== 'undefined') {\n      process.on('exit', this._exitHandler)\n    }\n    awareness.on('update', this._awarenessUpdateHandler)\n    this._checkInterval = /** @type {any} */ (setInterval(() => {\n      if (\n        this.wsconnected &&\n        messageReconnectTimeout <\n          time.getUnixTime() - this.wsLastMessageReceived\n      ) {\n        // no message received in a long time - not even your own awareness\n        // updates (which are updated every 15 seconds)\n        closeWebsocketConnection(this, /** @type {WebSocket} */ (this.ws), null)\n      }\n    }, messageReconnectTimeout / 10))\n    if (connect) {\n      this.connect()\n    }\n  }\n\n  get url () {\n    const encodedParams = url.encodeQueryParams(this.params)\n    return this.serverUrl + '/' + this.roomname +\n      (encodedParams.length === 0 ? '' : '?' + encodedParams)\n  }\n\n  /**\n   * @type {boolean}\n   */\n  get synced () {\n    return this._synced\n  }\n\n  set synced (state) {\n    if (this._synced !== state) {\n      this._synced = state\n      // @ts-ignore\n      this.emit('synced', [state])\n      this.emit('sync', [state])\n    }\n  }\n\n  destroy () {\n    if (this._resyncInterval !== 0) {\n      clearInterval(this._resyncInterval)\n    }\n    clearInterval(this._checkInterval)\n    this.disconnect()\n    if (env.isNode && typeof process !== 'undefined') {\n      process.off('exit', this._exitHandler)\n    }\n    this.awareness.off('update', this._awarenessUpdateHandler)\n    this.doc.off('update', this._updateHandler)\n    super.destroy()\n  }\n\n  connectBc () {\n    if (this.disableBc) {\n      return\n    }\n    if (!this.bcconnected) {\n      bc.subscribe(this.bcChannel, this._bcSubscriber)\n      this.bcconnected = true\n    }\n    // send sync step1 to bc\n    // write sync step 1\n    const encoderSync = encoding.createEncoder()\n    encoding.writeVarUint(encoderSync, messageSync)\n    syncProtocol.writeSyncStep1(encoderSync, this.doc)\n    bc.publish(this.bcChannel, encoding.toUint8Array(encoderSync), this)\n    // broadcast local state\n    const encoderState = encoding.createEncoder()\n    encoding.writeVarUint(encoderState, messageSync)\n    syncProtocol.writeSyncStep2(encoderState, this.doc)\n    bc.publish(this.bcChannel, encoding.toUint8Array(encoderState), this)\n    // write queryAwareness\n    const encoderAwarenessQuery = encoding.createEncoder()\n    encoding.writeVarUint(encoderAwarenessQuery, messageQueryAwareness)\n    bc.publish(\n      this.bcChannel,\n      encoding.toUint8Array(encoderAwarenessQuery),\n      this\n    )\n    // broadcast local awareness state\n    const encoderAwarenessState = encoding.createEncoder()\n    encoding.writeVarUint(encoderAwarenessState, messageAwareness)\n    encoding.writeVarUint8Array(\n      encoderAwarenessState,\n      awarenessProtocol.encodeAwarenessUpdate(this.awareness, [\n        this.doc.clientID\n      ])\n    )\n    bc.publish(\n      this.bcChannel,\n      encoding.toUint8Array(encoderAwarenessState),\n      this\n    )\n  }\n\n  disconnectBc () {\n    // broadcast message with local awareness state set to null (indicating disconnect)\n    const encoder = encoding.createEncoder()\n    encoding.writeVarUint(encoder, messageAwareness)\n    encoding.writeVarUint8Array(\n      encoder,\n      awarenessProtocol.encodeAwarenessUpdate(this.awareness, [\n        this.doc.clientID\n      ], new Map())\n    )\n    broadcastMessage(this, encoding.toUint8Array(encoder))\n    if (this.bcconnected) {\n      bc.unsubscribe(this.bcChannel, this._bcSubscriber)\n      this.bcconnected = false\n    }\n  }\n\n  disconnect () {\n    this.shouldConnect = false\n    this.disconnectBc()\n    if (this.ws !== null) {\n      closeWebsocketConnection(this, this.ws, null)\n    }\n  }\n\n  connect () {\n    this.shouldConnect = true\n    if (!this.wsconnected && this.ws === null) {\n      setupWS(this)\n      this.connectBc()\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,IAAM,WAAW,oBAAI,IAAI;AAGzB,IAAM,uBAAN,MAA2B;AAAA;AAAA;AAAA;AAAA,EAIzB,YAAa,MAAM;AACjB,SAAK,OAAO;AAIZ,SAAK,YAAY;AAIjB,SAAK,YAAY,OAAK,EAAE,QAAQ,QAAQ,KAAK,cAAc,QAAQ,KAAK,UAAU,EAAE,MAAa,WAAW,EAAE,YAAY,EAAE,EAAE,CAAC;AAC/H,IAAQ,SAAS,KAAK,SAAS;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKA,YAAa,KAAK;AAChB,IAAQ,WAAW,QAAQ,KAAK,MAAa,SAAgB,gCAAgC,GAAG,CAAC,CAAC;AAAA,EACpG;AAAA,EAEA,QAAS;AACP,IAAQ,UAAU,KAAK,SAAS;AAAA,EAClC;AACF;AAKA,IAAM,KAAK,OAAO,qBAAqB,cAAc,uBAAuB;AAM5E,IAAM,aAAa,UACb,eAAe,UAAU,MAAM,MAAM;AACvC,QAAM,OAAW,OAAO;AACxB,QAAM,KAAK,IAAI,GAAG,IAAI;AAKtB,KAAG,YAAY,OAAK,KAAK,QAAQ,SAAO,IAAI,EAAE,MAAM,kBAAkB,CAAC;AACvE,SAAO;AAAA,IACL;AAAA,IAAI;AAAA,EACN;AACF,CAAC;AASI,IAAM,YAAY,CAAC,MAAM,MAAM;AACpC,aAAW,IAAI,EAAE,KAAK,IAAI,CAAC;AAC3B,SAAO;AACT;AASO,IAAM,cAAc,CAAC,MAAM,MAAM;AACtC,QAAM,UAAU,WAAW,IAAI;AAC/B,QAAM,eAAe,QAAQ,KAAK,OAAO,CAAC;AAC1C,MAAI,gBAAgB,QAAQ,KAAK,SAAS,GAAG;AAC3C,YAAQ,GAAG,MAAM;AACjB,aAAS,OAAO,IAAI;AAAA,EACtB;AACA,SAAO;AACT;AAUO,IAAM,UAAU,CAAC,MAAM,MAAM,SAAS,SAAS;AACpD,QAAM,IAAI,WAAW,IAAI;AACzB,IAAE,GAAG,YAAY,IAAI;AACrB,IAAE,KAAK,QAAQ,SAAO,IAAI,MAAM,MAAM,CAAC;AACzC;;;AC5FO,IAAM,sBAAsB;AAC5B,IAAM,sBAAsB;AAC5B,IAAM,mBAAmB;AAQzB,IAAM,iBAAiB,CAAC,SAAS,QAAQ;AAC9C,EAAS,aAAa,SAAS,mBAAmB;AAClD,QAAM,KAAO,kBAAkB,GAAG;AAClC,EAAS,mBAAmB,SAAS,EAAE;AACzC;AAOO,IAAM,iBAAiB,CAAC,SAAS,KAAK,uBAAuB;AAClE,EAAS,aAAa,SAAS,mBAAmB;AAClD,EAAS,mBAAmB,SAAW,oBAAoB,KAAK,kBAAkB,CAAC;AACrF;AASO,IAAM,gBAAgB,CAAC,SAAS,SAAS,QAC9C,eAAe,SAAS,KAAc,kBAAkB,OAAO,CAAC;AAS3D,IAAM,gBAAgB,CAAC,SAAS,KAAK,sBAAsB;AAChE,MAAI;AACF,IAAE,YAAY,KAAc,kBAAkB,OAAO,GAAG,iBAAiB;AAAA,EAC3E,SAAS,OAAO;AAEd,YAAQ,MAAM,4CAA4C,KAAK;AAAA,EACjE;AACF;AAMO,IAAM,cAAc,CAAC,SAAS,WAAW;AAC9C,EAAS,aAAa,SAAS,gBAAgB;AAC/C,EAAS,mBAAmB,SAAS,MAAM;AAC7C;AASO,IAAM,aAAa;AAQnB,IAAM,kBAAkB,CAAC,SAAS,SAAS,KAAK,sBAAsB;AAC3E,QAAM,cAAuB,YAAY,OAAO;AAChD,UAAQ,aAAa;AAAA,IACnB,KAAK;AACH,oBAAc,SAAS,SAAS,GAAG;AACnC;AAAA,IACF,KAAK;AACH,oBAAc,SAAS,KAAK,iBAAiB;AAC7C;AAAA,IACF,KAAK;AACH,iBAAW,SAAS,KAAK,iBAAiB;AAC1C;AAAA,IACF;AACE,YAAM,IAAI,MAAM,sBAAsB;AAAA,EAC1C;AACA,SAAO;AACT;;;AC5HO,IAAM,0BAA0B;AAuBhC,IAAM,kBAAkB,CAAC,SAAS,GAAGA,6BAA4B;AACtE,UAAiB,YAAY,OAAO,GAAG;AAAA,IACrC,KAAK;AAAyB,MAAAA,yBAAwB,GAAY,cAAc,OAAO,CAAC;AAAA,EAC1F;AACF;;;ACpBO,IAAM,kBAAkB;AA0BxB,IAAM,YAAN,cAAwB,WAAW;AAAA;AAAA;AAAA;AAAA,EAIxC,YAAa,KAAK;AAChB,UAAM;AACN,SAAK,MAAM;AAIX,SAAK,WAAW,IAAI;AAKpB,SAAK,SAAS,oBAAI,IAAI;AAItB,SAAK,OAAO,oBAAI,IAAI;AACpB,SAAK;AAAA,IAAqC,YAAY,MAAM;AAC1D,YAAM,MAAW,YAAY;AAC7B,UAAI,KAAK,cAAc,MAAM,QAAS,kBAAkB,KAAK;AAAA,MAA2C,KAAK,KAAK,IAAI,KAAK,QAAQ,EAAG,aAAc;AAElJ,aAAK,cAAc,KAAK,cAAc,CAAC;AAAA,MACzC;AAIA,YAAM,SAAS,CAAC;AAChB,WAAK,KAAK,QAAQ,CAAC,MAAM,aAAa;AACpC,YAAI,aAAa,KAAK,YAAY,mBAAmB,MAAM,KAAK,eAAe,KAAK,OAAO,IAAI,QAAQ,GAAG;AACxG,iBAAO,KAAK,QAAQ;AAAA,QACtB;AAAA,MACF,CAAC;AACD,UAAI,OAAO,SAAS,GAAG;AACrB,8BAAsB,MAAM,QAAQ,SAAS;AAAA,MAC/C;AAAA,IACF,GAAQ,MAAM,kBAAkB,EAAE,CAAC;AACnC,QAAI,GAAG,WAAW,MAAM;AACtB,WAAK,QAAQ;AAAA,IACf,CAAC;AACD,SAAK,cAAc,CAAC,CAAC;AAAA,EACvB;AAAA,EAEA,UAAW;AACT,SAAK,KAAK,WAAW,CAAC,IAAI,CAAC;AAC3B,SAAK,cAAc,IAAI;AACvB,UAAM,QAAQ;AACd,kBAAc,KAAK,cAAc;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAiB;AACf,WAAO,KAAK,OAAO,IAAI,KAAK,QAAQ,KAAK;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,cAAe,OAAO;AACpB,UAAM,WAAW,KAAK;AACtB,UAAM,gBAAgB,KAAK,KAAK,IAAI,QAAQ;AAC5C,UAAM,QAAQ,kBAAkB,SAAY,IAAI,cAAc,QAAQ;AACtE,UAAM,YAAY,KAAK,OAAO,IAAI,QAAQ;AAC1C,QAAI,UAAU,MAAM;AAClB,WAAK,OAAO,OAAO,QAAQ;AAAA,IAC7B,OAAO;AACL,WAAK,OAAO,IAAI,UAAU,KAAK;AAAA,IACjC;AACA,SAAK,KAAK,IAAI,UAAU;AAAA,MACtB;AAAA,MACA,aAAkB,YAAY;AAAA,IAChC,CAAC;AACD,UAAM,QAAQ,CAAC;AACf,UAAM,UAAU,CAAC;AACjB,UAAM,kBAAkB,CAAC;AACzB,UAAM,UAAU,CAAC;AACjB,QAAI,UAAU,MAAM;AAClB,cAAQ,KAAK,QAAQ;AAAA,IACvB,WAAW,aAAa,MAAM;AAC5B,UAAI,SAAS,MAAM;AACjB,cAAM,KAAK,QAAQ;AAAA,MACrB;AAAA,IACF,OAAO;AACL,cAAQ,KAAK,QAAQ;AACrB,UAAI,CAAG,aAAa,WAAW,KAAK,GAAG;AACrC,wBAAgB,KAAK,QAAQ;AAAA,MAC/B;AAAA,IACF;AACA,QAAI,MAAM,SAAS,KAAK,gBAAgB,SAAS,KAAK,QAAQ,SAAS,GAAG;AACxE,WAAK,KAAK,UAAU,CAAC,EAAE,OAAO,SAAS,iBAAiB,QAAQ,GAAG,OAAO,CAAC;AAAA,IAC7E;AACA,SAAK,KAAK,UAAU,CAAC,EAAE,OAAO,SAAS,QAAQ,GAAG,OAAO,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAoB,OAAO,OAAO;AAChC,UAAM,QAAQ,KAAK,cAAc;AACjC,QAAI,UAAU,MAAM;AAClB,WAAK,cAAc;AAAA,QACjB,GAAG;AAAA,QACH,CAAC,KAAK,GAAG;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,YAAa;AACX,WAAO,KAAK;AAAA,EACd;AACF;AAUO,IAAM,wBAAwB,CAAC,WAAW,SAAS,WAAW;AACnE,QAAM,UAAU,CAAC;AACjB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,WAAW,QAAQ,CAAC;AAC1B,QAAI,UAAU,OAAO,IAAI,QAAQ,GAAG;AAClC,gBAAU,OAAO,OAAO,QAAQ;AAChC,UAAI,aAAa,UAAU,UAAU;AACnC,cAAM;AAAA;AAAA,UAA0C,UAAU,KAAK,IAAI,QAAQ;AAAA;AAC3E,kBAAU,KAAK,IAAI,UAAU;AAAA,UAC3B,OAAO,QAAQ,QAAQ;AAAA,UACvB,aAAkB,YAAY;AAAA,QAChC,CAAC;AAAA,MACH;AACA,cAAQ,KAAK,QAAQ;AAAA,IACvB;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,GAAG;AACtB,cAAU,KAAK,UAAU,CAAC,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC,GAAG,QAAQ,GAAG,MAAM,CAAC;AACtE,cAAU,KAAK,UAAU,CAAC,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC,GAAG,QAAQ,GAAG,MAAM,CAAC;AAAA,EACxE;AACF;AAOO,IAAM,wBAAwB,CAAC,WAAW,SAAS,SAAS,UAAU,WAAW;AACtF,QAAM,MAAM,QAAQ;AACpB,QAAM,UAAmB,cAAc;AACvC,EAAS,aAAa,SAAS,GAAG;AAClC,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,WAAW,QAAQ,CAAC;AAC1B,UAAM,QAAQ,OAAO,IAAI,QAAQ,KAAK;AACtC,UAAM;AAAA;AAAA,MAAwC,UAAU,KAAK,IAAI,QAAQ,EAAG;AAAA;AAC5E,IAAS,aAAa,SAAS,QAAQ;AACvC,IAAS,aAAa,SAAS,KAAK;AACpC,IAAS,eAAe,SAAS,KAAK,UAAU,KAAK,CAAC;AAAA,EACxD;AACA,SAAgB,aAAa,OAAO;AACtC;AAkCO,IAAM,uBAAuB,CAAC,WAAW,QAAQ,WAAW;AACjE,QAAM,UAAmB,cAAc,MAAM;AAC7C,QAAM,YAAiB,YAAY;AACnC,QAAM,QAAQ,CAAC;AACf,QAAM,UAAU,CAAC;AACjB,QAAM,kBAAkB,CAAC;AACzB,QAAM,UAAU,CAAC;AACjB,QAAM,MAAe,YAAY,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,WAAoB,YAAY,OAAO;AAC7C,QAAI,QAAiB,YAAY,OAAO;AACxC,UAAM,QAAQ,KAAK,MAAe,cAAc,OAAO,CAAC;AACxD,UAAM,aAAa,UAAU,KAAK,IAAI,QAAQ;AAC9C,UAAM,YAAY,UAAU,OAAO,IAAI,QAAQ;AAC/C,UAAM,YAAY,eAAe,SAAY,IAAI,WAAW;AAC5D,QAAI,YAAY,SAAU,cAAc,SAAS,UAAU,QAAQ,UAAU,OAAO,IAAI,QAAQ,GAAI;AAClG,UAAI,UAAU,MAAM;AAElB,YAAI,aAAa,UAAU,YAAY,UAAU,cAAc,KAAK,MAAM;AAGxE;AAAA,QACF,OAAO;AACL,oBAAU,OAAO,OAAO,QAAQ;AAAA,QAClC;AAAA,MACF,OAAO;AACL,kBAAU,OAAO,IAAI,UAAU,KAAK;AAAA,MACtC;AACA,gBAAU,KAAK,IAAI,UAAU;AAAA,QAC3B;AAAA,QACA,aAAa;AAAA,MACf,CAAC;AACD,UAAI,eAAe,UAAa,UAAU,MAAM;AAC9C,cAAM,KAAK,QAAQ;AAAA,MACrB,WAAW,eAAe,UAAa,UAAU,MAAM;AACrD,gBAAQ,KAAK,QAAQ;AAAA,MACvB,WAAW,UAAU,MAAM;AACzB,YAAI,CAAG,aAAa,OAAO,SAAS,GAAG;AACrC,0BAAgB,KAAK,QAAQ;AAAA,QAC/B;AACA,gBAAQ,KAAK,QAAQ;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AACA,MAAI,MAAM,SAAS,KAAK,gBAAgB,SAAS,KAAK,QAAQ,SAAS,GAAG;AACxE,cAAU,KAAK,UAAU,CAAC;AAAA,MACxB;AAAA,MAAO,SAAS;AAAA,MAAiB;AAAA,IACnC,GAAG,MAAM,CAAC;AAAA,EACZ;AACA,MAAI,MAAM,SAAS,KAAK,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG;AAChE,cAAU,KAAK,UAAU,CAAC;AAAA,MACxB;AAAA,MAAO;AAAA,MAAS;AAAA,IAClB,GAAG,MAAM,CAAC;AAAA,EACZ;AACF;;;ACnQO,IAAM,oBAAoB,YACxB,IAAI,QAAQ,CAAC,KAAK,QAAQ,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,GAAG,CAAC,EAAE,EAAE,KAAK,GAAG;;;ACjB7F,IAAM,cAAc;AACpB,IAAM,wBAAwB;AAC9B,IAAM,mBAAmB;AACzB,IAAM,cAAc;AAM3B,IAAM,kBAAkB,CAAC;AAEzB,gBAAgB,WAAW,IAAI,CAC7B,SACA,SACA,UACA,YACA,iBACG;AACH,EAAS,aAAa,SAAS,WAAW;AAC1C,QAAM,kBAA+B;AAAA,IACnC;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,EACF;AACA,MACE,cAAc,oBAAiC,uBAC/C,CAAC,SAAS,QACV;AACA,aAAS,SAAS;AAAA,EACpB;AACF;AAEA,gBAAgB,qBAAqB,IAAI,CACvC,SACA,UACA,UACA,aACA,iBACG;AACH,EAAS,aAAa,SAAS,gBAAgB;AAC/C,EAAS;AAAA,IACP;AAAA,IACkB;AAAA,MAChB,SAAS;AAAA,MACT,MAAM,KAAK,SAAS,UAAU,UAAU,EAAE,KAAK,CAAC;AAAA,IAClD;AAAA,EACF;AACF;AAEA,gBAAgB,gBAAgB,IAAI,CAClC,UACA,SACA,UACA,aACA,iBACG;AACH,EAAkB;AAAA,IAChB,SAAS;AAAA,IACA,kBAAkB,OAAO;AAAA,IAClC;AAAA,EACF;AACF;AAEA,gBAAgB,WAAW,IAAI,CAC7B,UACA,SACA,UACA,aACA,iBACG;AACH,EAAa;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT,CAAC,OAAO,WAAW,wBAAwB,UAAU,MAAM;AAAA,EAC7D;AACF;AAGA,IAAM,0BAA0B;AAMhC,IAAM,0BAA0B,CAAC,UAAU,WACzC,QAAQ,KAAK,+BAA+B,SAAS,GAAG;AAAA,EAAM,MAAM,EAAE;AAQxE,IAAM,cAAc,CAAC,UAAU,KAAK,eAAe;AACjD,QAAM,UAAmB,cAAc,GAAG;AAC1C,QAAM,UAAmB,cAAc;AACvC,QAAM,cAAuB,YAAY,OAAO;AAChD,QAAM,iBAAiB,SAAS,gBAAgB,WAAW;AAC3D;AAAA;AAAA,IAAwB;AAAA,IAAiB;AACvC,mBAAe,SAAS,SAAS,UAAU,YAAY,WAAW;AAAA,EACpE,OAAO;AACL,YAAQ,MAAM,2BAA2B;AAAA,EAC3C;AACA,SAAO;AACT;AAUA,IAAM,2BAA2B,CAAC,UAAU,IAAI,UAAU;AACxD,MAAI,OAAO,SAAS,IAAI;AACtB,aAAS,KAAK,oBAAoB,CAAC,OAAO,QAAQ,CAAC;AACnD,aAAS,KAAK;AACd,OAAG,MAAM;AACT,aAAS,eAAe;AACxB,QAAI,SAAS,aAAa;AACxB,eAAS,cAAc;AACvB,eAAS,SAAS;AAElB,MAAkB;AAAA,QAChB,SAAS;AAAA,QACT,MAAM,KAAK,SAAS,UAAU,UAAU,EAAE,KAAK,CAAC,EAAE;AAAA,UAAO,CAAC,WACxD,WAAW,SAAS,IAAI;AAAA,QAC1B;AAAA,QACA;AAAA,MACF;AACA,eAAS,KAAK,UAAU,CAAC;AAAA,QACvB,QAAQ;AAAA,MACV,CAAC,CAAC;AAAA,IACJ,OAAO;AACL,eAAS;AAAA,IACX;AAGA;AAAA,MACE;AAAA,MACK;AAAA,QACE,IAAI,GAAG,SAAS,wBAAwB,IAAI;AAAA,QACjD,SAAS;AAAA,MACX;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAKA,IAAM,UAAU,CAAC,aAAa;AAC5B,MAAI,SAAS,iBAAiB,SAAS,OAAO,MAAM;AAClD,UAAM,YAAY,IAAI,SAAS,IAAI,SAAS,KAAK,SAAS,SAAS;AACnE,cAAU,aAAa;AACvB,aAAS,KAAK;AACd,aAAS,eAAe;AACxB,aAAS,cAAc;AACvB,aAAS,SAAS;AAElB,cAAU,YAAY,CAAC,UAAU;AAC/B,eAAS,wBAA6B,YAAY;AAClD,YAAM,UAAU,YAAY,UAAU,IAAI,WAAW,MAAM,IAAI,GAAG,IAAI;AACtE,UAAa,OAAO,OAAO,IAAI,GAAG;AAChC,kBAAU,KAAc,aAAa,OAAO,CAAC;AAAA,MAC/C;AAAA,IACF;AACA,cAAU,UAAU,CAAC,UAAU;AAC7B,eAAS,KAAK,oBAAoB,CAAC,OAAO,QAAQ,CAAC;AAAA,IACrD;AACA,cAAU,UAAU,CAAC,UAAU;AAC7B,+BAAyB,UAAU,WAAW,KAAK;AAAA,IACrD;AACA,cAAU,SAAS,MAAM;AACvB,eAAS,wBAA6B,YAAY;AAClD,eAAS,eAAe;AACxB,eAAS,cAAc;AACvB,eAAS,2BAA2B;AACpC,eAAS,KAAK,UAAU,CAAC;AAAA,QACvB,QAAQ;AAAA,MACV,CAAC,CAAC;AAEF,YAAM,UAAmB,cAAc;AACvC,MAAS,aAAa,SAAS,WAAW;AAC1C,MAAa,eAAe,SAAS,SAAS,GAAG;AACjD,gBAAU,KAAc,aAAa,OAAO,CAAC;AAE7C,UAAI,SAAS,UAAU,cAAc,MAAM,MAAM;AAC/C,cAAM,wBAAiC,cAAc;AACrD,QAAS,aAAa,uBAAuB,gBAAgB;AAC7D,QAAS;AAAA,UACP;AAAA,UACkB,sBAAsB,SAAS,WAAW;AAAA,YAC1D,SAAS,IAAI;AAAA,UACf,CAAC;AAAA,QACH;AACA,kBAAU,KAAc,aAAa,qBAAqB,CAAC;AAAA,MAC7D;AAAA,IACF;AACA,aAAS,KAAK,UAAU,CAAC;AAAA,MACvB,QAAQ;AAAA,IACV,CAAC,CAAC;AAAA,EACJ;AACF;AAMA,IAAM,mBAAmB,CAAC,UAAU,QAAQ;AAC1C,QAAM,KAAK,SAAS;AACpB,MAAI,SAAS,eAAe,MAAM,GAAG,eAAe,GAAG,MAAM;AAC3D,OAAG,KAAK,GAAG;AAAA,EACb;AACA,MAAI,SAAS,aAAa;AACxB,IAAG,QAAQ,SAAS,WAAW,KAAK,QAAQ;AAAA,EAC9C;AACF;AAeO,IAAM,oBAAN,cAAgC,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAelD,YAAa,WAAW,UAAU,KAAK;AAAA,IACrC,UAAU;AAAA,IACV,YAAY,IAAsB,UAAU,GAAG;AAAA,IAC/C,SAAS,CAAC;AAAA,IACV,YAAY,CAAC;AAAA,IACb,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,YAAY;AAAA,EACd,IAAI,CAAC,GAAG;AACN,UAAM;AAEN,WAAO,UAAU,UAAU,SAAS,CAAC,MAAM,KAAK;AAC9C,kBAAY,UAAU,MAAM,GAAG,UAAU,SAAS,CAAC;AAAA,IACrD;AACA,SAAK,YAAY;AACjB,SAAK,YAAY,YAAY,MAAM;AACnC,SAAK,iBAAiB;AAMtB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,2BAA2B;AAChC,SAAK,kBAAkB,gBAAgB,MAAM;AAI7C,SAAK,UAAU;AAIf,SAAK,KAAK;AACV,SAAK,wBAAwB;AAK7B,SAAK,gBAAgB;AAKrB,SAAK,kBAAkB;AACvB,QAAI,iBAAiB,GAAG;AACtB,WAAK;AAAA,MAAsC,YAAY,MAAM;AAC3D,YAAI,KAAK,MAAM,KAAK,GAAG,eAAe,UAAU,MAAM;AAEpD,gBAAM,UAAmB,cAAc;AACvC,UAAS,aAAa,SAAS,WAAW;AAC1C,UAAa,eAAe,SAAS,GAAG;AACxC,eAAK,GAAG,KAAc,aAAa,OAAO,CAAC;AAAA,QAC7C;AAAA,MACF,GAAG,cAAc;AAAA,IACnB;AAMA,SAAK,gBAAgB,CAAC,MAAM,WAAW;AACrC,UAAI,WAAW,MAAM;AACnB,cAAM,UAAU,YAAY,MAAM,IAAI,WAAW,IAAI,GAAG,KAAK;AAC7D,YAAa,OAAO,OAAO,IAAI,GAAG;AAChC,UAAG,QAAQ,KAAK,WAAoB,aAAa,OAAO,GAAG,IAAI;AAAA,QACjE;AAAA,MACF;AAAA,IACF;AAMA,SAAK,iBAAiB,CAAC,QAAQ,WAAW;AACxC,UAAI,WAAW,MAAM;AACnB,cAAM,UAAmB,cAAc;AACvC,QAAS,aAAa,SAAS,WAAW;AAC1C,QAAa,YAAY,SAAS,MAAM;AACxC,yBAAiB,MAAe,aAAa,OAAO,CAAC;AAAA,MACvD;AAAA,IACF;AACA,SAAK,IAAI,GAAG,UAAU,KAAK,cAAc;AAKzC,SAAK,0BAA0B,CAAC,EAAE,OAAO,SAAS,QAAQ,GAAG,YAAY;AACvE,YAAM,iBAAiB,MAAM,OAAO,OAAO,EAAE,OAAO,OAAO;AAC3D,YAAM,UAAmB,cAAc;AACvC,MAAS,aAAa,SAAS,gBAAgB;AAC/C,MAAS;AAAA,QACP;AAAA,QACkB,sBAAsB,WAAW,cAAc;AAAA,MACnE;AACA,uBAAiB,MAAe,aAAa,OAAO,CAAC;AAAA,IACvD;AACA,SAAK,eAAe,MAAM;AACxB,MAAkB;AAAA,QAChB,KAAK;AAAA,QACL,CAAC,IAAI,QAAQ;AAAA,QACb;AAAA,MACF;AAAA,IACF;AACA,QAAQ,UAAU,OAAO,YAAY,aAAa;AAChD,cAAQ,GAAG,QAAQ,KAAK,YAAY;AAAA,IACtC;AACA,cAAU,GAAG,UAAU,KAAK,uBAAuB;AACnD,SAAK;AAAA,IAAqC,YAAY,MAAM;AAC1D,UACE,KAAK,eACL,0BACO,YAAY,IAAI,KAAK,uBAC5B;AAGA;AAAA,UAAyB;AAAA;AAAA,UAAgC,KAAK;AAAA,UAAK;AAAA,QAAI;AAAA,MACzE;AAAA,IACF,GAAG,0BAA0B,EAAE;AAC/B,QAAI,SAAS;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EAEA,IAAI,MAAO;AACT,UAAM,gBAAoB,kBAAkB,KAAK,MAAM;AACvD,WAAO,KAAK,YAAY,MAAM,KAAK,YAChC,cAAc,WAAW,IAAI,KAAK,MAAM;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,OAAQ,OAAO;AACjB,QAAI,KAAK,YAAY,OAAO;AAC1B,WAAK,UAAU;AAEf,WAAK,KAAK,UAAU,CAAC,KAAK,CAAC;AAC3B,WAAK,KAAK,QAAQ,CAAC,KAAK,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EAEA,UAAW;AACT,QAAI,KAAK,oBAAoB,GAAG;AAC9B,oBAAc,KAAK,eAAe;AAAA,IACpC;AACA,kBAAc,KAAK,cAAc;AACjC,SAAK,WAAW;AAChB,QAAQ,UAAU,OAAO,YAAY,aAAa;AAChD,cAAQ,IAAI,QAAQ,KAAK,YAAY;AAAA,IACvC;AACA,SAAK,UAAU,IAAI,UAAU,KAAK,uBAAuB;AACzD,SAAK,IAAI,IAAI,UAAU,KAAK,cAAc;AAC1C,UAAM,QAAQ;AAAA,EAChB;AAAA,EAEA,YAAa;AACX,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,aAAa;AACrB,MAAG,UAAU,KAAK,WAAW,KAAK,aAAa;AAC/C,WAAK,cAAc;AAAA,IACrB;AAGA,UAAM,cAAuB,cAAc;AAC3C,IAAS,aAAa,aAAa,WAAW;AAC9C,IAAa,eAAe,aAAa,KAAK,GAAG;AACjD,IAAG,QAAQ,KAAK,WAAoB,aAAa,WAAW,GAAG,IAAI;AAEnE,UAAM,eAAwB,cAAc;AAC5C,IAAS,aAAa,cAAc,WAAW;AAC/C,IAAa,eAAe,cAAc,KAAK,GAAG;AAClD,IAAG,QAAQ,KAAK,WAAoB,aAAa,YAAY,GAAG,IAAI;AAEpE,UAAM,wBAAiC,cAAc;AACrD,IAAS,aAAa,uBAAuB,qBAAqB;AAClE,IAAG;AAAA,MACD,KAAK;AAAA,MACI,aAAa,qBAAqB;AAAA,MAC3C;AAAA,IACF;AAEA,UAAM,wBAAiC,cAAc;AACrD,IAAS,aAAa,uBAAuB,gBAAgB;AAC7D,IAAS;AAAA,MACP;AAAA,MACkB,sBAAsB,KAAK,WAAW;AAAA,QACtD,KAAK,IAAI;AAAA,MACX,CAAC;AAAA,IACH;AACA,IAAG;AAAA,MACD,KAAK;AAAA,MACI,aAAa,qBAAqB;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AAAA,EAEA,eAAgB;AAEd,UAAM,UAAmB,cAAc;AACvC,IAAS,aAAa,SAAS,gBAAgB;AAC/C,IAAS;AAAA,MACP;AAAA,MACkB,sBAAsB,KAAK,WAAW;AAAA,QACtD,KAAK,IAAI;AAAA,MACX,GAAG,oBAAI,IAAI,CAAC;AAAA,IACd;AACA,qBAAiB,MAAe,aAAa,OAAO,CAAC;AACrD,QAAI,KAAK,aAAa;AACpB,MAAG,YAAY,KAAK,WAAW,KAAK,aAAa;AACjD,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,aAAc;AACZ,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,QAAI,KAAK,OAAO,MAAM;AACpB,+BAAyB,MAAM,KAAK,IAAI,IAAI;AAAA,IAC9C;AAAA,EACF;AAAA,EAEA,UAAW;AACT,SAAK,gBAAgB;AACrB,QAAI,CAAC,KAAK,eAAe,KAAK,OAAO,MAAM;AACzC,cAAQ,IAAI;AACZ,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AACF;", "names": ["permissionDeniedHandler"]}