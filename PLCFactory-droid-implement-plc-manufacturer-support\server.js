const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// PLC Manufacturer validation logic (JavaScript port of Go code)
class TagValidator {
  constructor(manufacturer) {
    this.manufacturer = manufacturer;
    this.patterns = this.getPatterns();
    this.supportedDataTypes = this.getSupportedDataTypes();
  }

  getPatterns() {
    const patterns = {
      allen_bradley: {
        simple: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
        program: /^Program:[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*$/,
        array: /^[a-zA-Z_][a-zA-Z0-9_]*\[\d+\]$/,
        struct: /^[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*$/
      },
      siemens: {
        db: /^DB(\d+)\.(DBX|DBW|DBD)(\d+)(\.(\d+))?$/,
        area: /^(M|I|Q)(\d+)(\.(\d+))?$/
      },
      mitsubishi: {
        bit: /^(X|Y|M|SM|L|F|V|B|SB|DX|DY)([0-9A-F]+)$/,
        word: /^(D|SD|Z|R|ZR|W|SW|TN|CN|STN|SCN|LTN|LCN|LZ|RD)([0-9]+)$/,
        indexed: /^(D|SD|Z|R|ZR|W|SW)([0-9]+)\.([0-9]+)$/
      },
      schneider: {
        modicon: /^%([MIQ])([XBWDF])(\d+)(\.(\d+))?$/
      },
      omron: {
        cio: /^CIO(\d+)(\.(\d+))?$/,
        work: /^W(\d+)(\.(\d+))?$/,
        holding: /^H(\d+)(\.(\d+))?$/,
        aux: /^A(\d+)(\.(\d+))?$/,
        dm: /^D(\d+)(\.(\d+))?$/,
        tc: /^(T|C)(\d+)$/
      }
    };
    return patterns[this.manufacturer] || {};
  }

  getSupportedDataTypes() {
    const dataTypes = {
      allen_bradley: ['BOOL', 'SINT', 'INT', 'DINT', 'LINT', 'USINT', 'UINT', 'UDINT', 'ULINT', 'REAL', 'LREAL', 'STRING', 'TIMER', 'COUNTER', 'CONTROL'],
      siemens: ['BOOL', 'BYTE', 'WORD', 'DWORD', 'CHAR', 'SINT', 'INT', 'DINT', 'USINT', 'UINT', 'UDINT', 'REAL', 'STRING'],
      mitsubishi: ['BOOL', 'WORD', 'DWORD', 'INT', 'DINT', 'UINT', 'UDINT', 'FLOAT', 'STRING'],
      schneider: ['BOOL', 'BYTE', 'WORD', 'DWORD', 'SINT', 'INT', 'DINT', 'USINT', 'UINT', 'UDINT', 'REAL', 'FLOAT', 'STRING'],
      omron: ['BOOL', 'WORD', 'DWORD', 'INT', 'DINT', 'UINT', 'UDINT', 'REAL', 'STRING']
    };
    return dataTypes[this.manufacturer] || [];
  }

  validate(address, dataType) {
    if (!address) {
      return { valid: false, error: 'Address cannot be empty' };
    }

    // Check if data type is supported
    if (!this.supportedDataTypes.some(t => t.toLowerCase() === dataType.toLowerCase())) {
      return { 
        valid: false, 
        error: `Data type not supported (supported types: ${this.supportedDataTypes.join(', ')})` 
      };
    }

    return this.validateAddress(address, dataType);
  }

  validateAddress(address, dataType) {
    switch (this.manufacturer) {
      case 'allen_bradley':
        return this.validateAllenBradley(address, dataType);
      case 'siemens':
        return this.validateSiemens(address, dataType);
      case 'mitsubishi':
        return this.validateMitsubishi(address, dataType);
      case 'schneider':
        return this.validateSchneider(address, dataType);
      case 'omron':
        return this.validateOmron(address, dataType);
      default:
        return { valid: false, error: 'Unsupported manufacturer' };
    }
  }

  validateAllenBradley(address, dataType) {
    const patterns = this.patterns;
    
    if (address.startsWith('Program:')) {
      return { valid: patterns.program.test(address) };
    }
    if (address.includes('[') && address.includes(']')) {
      return { valid: patterns.array.test(address) };
    }
    if (address.includes('.')) {
      return { valid: patterns.struct.test(address) };
    }
    return { valid: patterns.simple.test(address) };
  }

  validateSiemens(address, dataType) {
    const patterns = this.patterns;
    
    if (patterns.db.test(address)) {
      const match = address.match(patterns.db);
      const memType = match[2];
      const hasbit = match[5] !== undefined;
      
      // Validate data type compatibility
      if (dataType.toLowerCase() === 'bool' && (memType !== 'DBX' || !hasbit)) {
        return { valid: false, error: 'BOOL type requires DBX with bit offset' };
      }
      return { valid: true };
    }
    
    if (patterns.area.test(address)) {
      const match = address.match(patterns.area);
      const hasbit = match[4] !== undefined;
      
      if (dataType.toLowerCase() === 'bool' && !hasbit) {
        return { valid: false, error: 'BOOL type requires bit offset' };
      }
      return { valid: true };
    }
    
    return { valid: false, error: 'Invalid Siemens address format' };
  }

  validateMitsubishi(address, dataType) {
    const patterns = this.patterns;
    
    if (patterns.bit.test(address)) {
      if (dataType.toLowerCase() !== 'bool') {
        return { valid: false, error: 'Bit devices should be used with BOOL data type' };
      }
      return { valid: true };
    }
    
    if (patterns.word.test(address)) {
      if (dataType.toLowerCase() === 'bool') {
        return { valid: false, error: 'Word devices should not be used with BOOL data type' };
      }
      return { valid: true };
    }
    
    if (patterns.indexed.test(address)) {
      if (dataType.toLowerCase() !== 'bool') {
        return { valid: false, error: 'Indexed bit access should be used with BOOL data type' };
      }
      return { valid: true };
    }
    
    return { valid: false, error: 'Invalid Mitsubishi address format' };
  }

  validateSchneider(address, dataType) {
    const patterns = this.patterns;
    
    if (patterns.modicon.test(address)) {
      const match = address.match(patterns.modicon);
      const size = match[2];
      
      // Validate size and data type compatibility
      if (size === 'X' && dataType.toLowerCase() !== 'bool') {
        return { valid: false, error: 'X (bit) size should be used with BOOL data type' };
      }
      return { valid: true };
    }
    
    return { valid: false, error: 'Invalid Schneider address format' };
  }

  validateOmron(address, dataType) {
    const patterns = this.patterns;
    
    for (const [key, pattern] of Object.entries(patterns)) {
      if (pattern.test(address)) {
        const match = address.match(pattern);
        const hasbit = match[2] !== undefined || match[3] !== undefined;
        
        if (key === 'tc') {
          if (dataType.toLowerCase() !== 'bool') {
            return { valid: false, error: 'T/C addresses should be used with BOOL data type' };
          }
        } else {
          if (hasbit && dataType.toLowerCase() !== 'bool') {
            return { valid: false, error: 'When bit index is specified, data type should be BOOL' };
          }
          if (!hasbit && dataType.toLowerCase() === 'bool') {
            return { valid: false, error: 'BOOL data type requires a bit index' };
          }
        }
        return { valid: true };
      }
    }
    
    return { valid: false, error: 'Invalid Omron address format' };
  }

  parse(address, dataType) {
    const validation = this.validate(address, dataType);
    if (!validation.valid) {
      return validation;
    }

    const components = { address: address, dataType: dataType };
    
    // Add manufacturer-specific parsing logic here
    switch (this.manufacturer) {
      case 'allen_bradley':
        if (address.startsWith('Program:')) {
          const parts = address.substring(8).split('.');
          components.programName = parts[0];
          components.tagName = parts[1];
          components.isProgramTag = true;
        } else if (address.includes('[')) {
          const match = address.match(/^([^[]+)\[(\d+)\]$/);
          if (match) {
            components.tagName = match[1];
            components.arrayIndex = parseInt(match[2]);
            components.isArrayTag = true;
          }
        } else if (address.includes('.')) {
          const parts = address.split('.');
          components.tagName = parts[0];
          components.memberName = parts[1];
          components.isStructTag = true;
        } else {
          components.tagName = address;
          components.isSimpleTag = true;
        }
        break;
      // Add more parsing logic for other manufacturers as needed
    }
    
    return { valid: true, components: components };
  }
}

// API Routes
app.get('/api/v1/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'PLC Manufacturer Support API',
    version: '1.0.0',
    supported_manufacturers: ['allen_bradley', 'siemens', 'mitsubishi', 'schneider', 'omron']
  });
});

app.get('/api/v1/manufacturers', (req, res) => {
  const manufacturers = [
    { name: 'allen_bradley', supportedDataTypes: new TagValidator('allen_bradley').getSupportedDataTypes() },
    { name: 'siemens', supportedDataTypes: new TagValidator('siemens').getSupportedDataTypes() },
    { name: 'mitsubishi', supportedDataTypes: new TagValidator('mitsubishi').getSupportedDataTypes() },
    { name: 'schneider', supportedDataTypes: new TagValidator('schneider').getSupportedDataTypes() },
    { name: 'omron', supportedDataTypes: new TagValidator('omron').getSupportedDataTypes() }
  ];
  res.json(manufacturers);
});

app.get('/api/v1/manufacturers/:manufacturer/datatypes', (req, res) => {
  const manufacturer = req.params.manufacturer;
  
  try {
    const validator = new TagValidator(manufacturer);
    const dataTypes = validator.getSupportedDataTypes();
    res.json(dataTypes);
  } catch (error) {
    res.status(400).json({ error: 'Unsupported manufacturer' });
  }
});

app.post('/api/v1/validate', (req, res) => {
  const { manufacturer, address, datatype } = req.body;
  
  if (!manufacturer || !address || !datatype) {
    return res.status(400).json({ 
      valid: false, 
      error: 'Missing required fields: manufacturer, address, datatype' 
    });
  }
  
  try {
    const validator = new TagValidator(manufacturer);
    const result = validator.validate(address, datatype);
    res.json(result);
  } catch (error) {
    res.status(400).json({ 
      valid: false, 
      error: `Unsupported manufacturer: ${manufacturer}` 
    });
  }
});

app.post('/api/v1/parse', (req, res) => {
  const { manufacturer, address, datatype } = req.body;
  
  if (!manufacturer || !address || !datatype) {
    return res.status(400).json({ 
      valid: false, 
      error: 'Missing required fields: manufacturer, address, datatype' 
    });
  }
  
  try {
    const validator = new TagValidator(manufacturer);
    const result = validator.parse(address, datatype);
    res.json(result);
  } catch (error) {
    res.status(400).json({ 
      valid: false, 
      error: `Unsupported manufacturer: ${manufacturer}` 
    });
  }
});

// Serve static files from public directory
app.use(express.static('public'));

// Serve the PLC Studio interface
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: './public' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 PLC Manufacturer Support Server starting...');
  console.log(`📡 Server running on http://localhost:${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/api/v1/health`);
  console.log(`🌐 Web interface: http://localhost:${PORT}`);
  console.log('✅ Ready to handle requests!');
}); 