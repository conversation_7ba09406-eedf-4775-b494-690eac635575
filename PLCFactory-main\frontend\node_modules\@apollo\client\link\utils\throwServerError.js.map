{"version": 3, "file": "throwServerError.js", "sourceRoot": "", "sources": ["../../../src/link/utils/throwServerError.ts"], "names": [], "mappings": "AAMA,MAAM,CAAC,IAAM,gBAAgB,GAAG,UAC9B,QAAkB,EAClB,MAAW,EACX,OAAe;IAEf,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAgB,CAAC;IAChD,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC;IAC3B,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC1B,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;IACnC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;IACtB,MAAM,KAAK,CAAC;AACd,CAAC,CAAC", "sourcesContent": ["export type ServerError = Error & {\n  response: Response;\n  result: Record<string, any> | string;\n  statusCode: number;\n};\n\nexport const throwServerError = (\n  response: Response,\n  result: any,\n  message: string\n) => {\n  const error = new Error(message) as ServerError;\n  error.name = \"ServerError\";\n  error.response = response;\n  error.statusCode = response.status;\n  error.result = result;\n  throw error;\n};\n"]}