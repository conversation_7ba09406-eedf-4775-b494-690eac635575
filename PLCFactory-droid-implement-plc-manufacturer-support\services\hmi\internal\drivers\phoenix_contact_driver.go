package drivers

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"hmi/internal/models"
	"hmi/pkg/logger"

	// Placeholder for real protocol libraries
	"gologix" // Mock for EtherNet/IP
	"gomodbus" // Mock for Modbus TCP
)

// PhoenixContactProtocolType defines the communication protocol for Phoenix Contact PLCs.
type PhoenixContactProtocolType string

const (
	ProtocolEthernetIP PhoenixContactProtocolType = "ethernet_ip"
	ProtocolModbusTCP  PhoenixContactProtocolType = "modbus_tcp"
)

// PhoenixContactDriver implements the PLCDriver interface for Phoenix Contact PLCs.
// It supports both EtherNet/IP (for PLCnext) and Modbus TCP protocols.
type PhoenixContactDriver struct {
	endpoint    string // IP address or hostname of the PLC
	port        int    // Port number (e.g., 44818 for EtherNet/IP, 502 for Modbus TCP)
	protocol    PhoenixContactProtocolType
	connected   bool
	mu          sync.Mutex // Mutex to protect connected status and connection pool
	connPool    sync.Pool  // Pool of client connections (either gologix.Client or gomodbus.Client)
	poolSize    int
	activeConns int
	cancelCtx   context.Context
	cancelFunc  context.CancelFunc
}

// NewPhoenixContactDriver creates a new PhoenixContactDriver instance.
// endpoint: IP address or hostname of the PLC.
// port: The port number for the chosen protocol.
// protocol: The communication protocol to use (EthernetIP or ModbusTCP).
// poolSize: The maximum number of connections to maintain in the pool.
func NewPhoenixContactDriver(endpoint string, port int, protocol PhoenixContactProtocolType, poolSize int) *PhoenixContactDriver {
	ctx, cancel := context.WithCancel(context.Background())
	driver := &PhoenixContactDriver{
		endpoint:   endpoint,
		port:       port,
		protocol:   protocol,
		poolSize:   poolSize,
		cancelCtx:  ctx,
		cancelFunc: cancel,
	}

	switch protocol {
	case ProtocolEthernetIP:
		driver.connPool = sync.Pool{
			New: func() interface{} {
				// This function creates a new gologix client when needed
				client := gologix.NewClient(endpoint)
				// Assuming PLCnext uses default slot 0 or similar for EtherNet/IP
				client.PLC.ProcessorSlot = 0
				return client
			},
		}
		logger.Infof("Initialized Phoenix Contact EtherNet/IP driver for %s:%d, pool size %d", endpoint, port, poolSize)
	case ProtocolModbusTCP:
		driver.connPool = sync.Pool{
			New: func() interface{} {
				return gomodbus.NewClient(endpoint, port)
			},
		}
		logger.Infof("Initialized Phoenix Contact Modbus TCP driver for %s:%d, pool size %d", endpoint, port, poolSize)
	default:
		logger.Errorf("Unsupported protocol for Phoenix Contact driver: %s", protocol)
	}

	return driver
}

// Connect establishes a connection to the PLC.
// It attempts to get a connection from the pool and verifies it.
func (d *PhoenixContactDriver) Connect(ctx context.Context) *DriverError {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.connected {
		return nil // Already connected
	}

	logger.Infof("Attempting to connect to Phoenix Contact PLC at %s:%d using %s...", d.endpoint, d.port, d.protocol)
	client, err := d.getClient(ctx)
	if err != nil {
		return NewDriverError(ErrCodeConnectionFailed, fmt.Sprintf("failed to get initial connection: %v", err), err)
	}
	d.releaseClient(client) // Release it back to the pool after verification

	d.connected = true
	logger.Info("Successfully connected to Phoenix Contact PLC.")
	return nil
}

// Disconnect closes the connection to the PLC.
func (d *PhoenixContactDriver) Disconnect(ctx context.Context) *DriverError {
	d.mu.Lock()
	defer d.mu.Unlock()

	if !d.connected {
		return nil // Already disconnected
	}

	logger.Info("Disconnecting from Phoenix Contact PLC...")
	d.cancelFunc() // Signal all background operations to stop
	d.Close()      // Close all pooled connections

	d.connected = false
	logger.Info("Disconnected from Phoenix Contact PLC.")
	return nil
}

// IsConnected returns true if the driver is currently connected to the PLC.
func (d *PhoenixContactDriver) IsConnected() bool {
	d.mu.Lock()
	defer d.mu.Unlock()
	return d.connected
}

// ReadTag reads the current value of a single PLC tag.
func (d *PhoenixContactDriver) ReadTag(ctx context.Context, tag models.PLCTag) (interface{}, *DriverError) {
	if !d.IsConnected() {
		return nil, NewDriverError(ErrCodeNotConnected, "driver not connected", nil)
	}

	client, err := d.getClient(ctx)
	if err != nil {
		return nil, NewDriverError(ErrCodeReadFailed, fmt.Sprintf("failed to get client for read: %v", err), err)
	}
	defer d.releaseClient(client)

	logger.Debugf("Reading tag: %s (%s) from %s", tag.Name, tag.Address, d.endpoint)

	var value interface{}
	var readErr error

	switch d.protocol {
	case ProtocolEthernetIP:
		// Assuming PLCnext uses EtherNet/IP for symbolic addressing
		goType, typeErr := abDataTypeToGoType(tag.DataType) // Reuse AB type mapping
		if typeErr != nil {
			return nil, NewDriverError(ErrCodeInvalidAddress, typeErr.Error(), typeErr)
		}
		value, readErr = client.(*gologix.Client).Read(tag.Address, goType)
	case ProtocolModbusTCP:
		modbusAddress, isCoil, numRegisters, parseErr := parseSchneiderAddress(tag.Address, tag.DataType) // Reuse Schneider Modbus parsing
		if parseErr != nil {
			return nil, NewDriverError(ErrCodeInvalidAddress, parseErr.Error(), parseErr)
		}
		if isCoil {
			coils, err := client.(*gomodbus.Client).ReadCoils(modbusAddress, 1)
			if err != nil {
				readErr = err
			} else {
				value = coils[0]
			}
		} else {
			registers, err := client.(*gomodbus.Client).ReadHoldingRegisters(modbusAddress, numRegisters)
			if err != nil {
				readErr = err
			} else {
				value, readErr = schneiderBytesToGoType(registers, tag.DataType)
			}
		}
	}

	if readErr != nil {
		return nil, NewDriverError(ErrCodeReadFailed, fmt.Sprintf("failed to read tag %s: %v", tag.Address, readErr), readErr)
	}

	logger.Debugf("Read tag %s: %v", tag.Address, value)
	return value, nil
}

// WriteTag writes a value to a single PLC tag.
func (d *PhoenixContactDriver) WriteTag(ctx context.Context, tag models.PLCTag, value interface{}) *DriverError {
	if !d.IsConnected() {
		return NewDriverError(ErrCodeNotConnected, "driver not connected", nil)
	}

	client, err := d.getClient(ctx)
	if err != nil {
		return NewDriverError(ErrCodeWriteFailed, fmt.Sprintf("failed to get client for write: %v", err), err)
	}
	defer d.releaseClient(client)

	logger.Debugf("Writing value %v to tag: %s (%s) on %s", value, tag.Name, tag.Address, d.endpoint)

	var writeErr error

	switch d.protocol {
	case ProtocolEthernetIP:
		goType, typeErr := abDataTypeToGoType(tag.DataType)
		if typeErr != nil {
			return NewDriverError(ErrCodeInvalidAddress, typeErr.Error(), typeErr)
		}
		writeErr = client.(*gologix.Client).Write(tag.Address, value, goType)
	case ProtocolModbusTCP:
		modbusAddress, isCoil, numRegisters, parseErr := parseSchneiderAddress(tag.Address, tag.DataType)
		if parseErr != nil {
			return NewDriverError(ErrCodeInvalidAddress, parseErr.Error(), parseErr)
		}
		if isCoil {
			boolVal, ok := value.(bool)
			if !ok {
				return NewDriverError(ErrCodeWriteFailed, fmt.Sprintf("expected bool value for coil, got %T", value), nil)
			}
			writeErr = client.(*gomodbus.Client).WriteSingleCoil(modbusAddress, boolVal)
		} else {
			bytes, convertErr := schneiderGoTypeToBytes(value, tag.DataType, numRegisters)
			if convertErr != nil {
				return NewDriverError(ErrCodeWriteFailed, fmt.Sprintf("failed to convert value for tag %s: %v", tag.Address, convertErr), convertErr)
			}
			writeErr = client.(*gomodbus.Client).WriteMultipleRegisters(modbusAddress, bytes)
		}
	}

	if writeErr != nil {
		return NewDriverError(ErrCodeWriteFailed, fmt.Sprintf("failed to write tag %s: %v", tag.Address, writeErr), writeErr)
	}

	logger.Debugf("Successfully wrote value %v to tag %s", value, tag.Address)
	return nil
}

// ReadTags reads multiple PLC tags in a single request when possible.
func (d *PhoenixContactDriver) ReadTags(ctx context.Context, tags []models.PLCTag) (map[string]interface{}, *DriverError) {
	if !d.IsConnected() {
		return nil, NewDriverError(ErrCodeNotConnected, "driver not connected", nil)
	}

	client, err := d.getClient(ctx)
	if err != nil {
		return nil, NewDriverError(ErrCodeReadFailed, fmt.Sprintf("failed to get client for read: %v", err), err)
	}
	defer d.releaseClient(client)

	results := make(map[string]interface{})

	// For simplicity, this mock just reads them individually.
	// In a real implementation, this would group contiguous tags and use block reads.
	for _, tag := range tags {
		value, readErr := d.ReadTag(ctx, tag)
		if readErr == nil {
			results[tag.ID] = value
		} else {
			logger.Warnf("Failed to read tag %s: %v", tag.Name, readErr)
		}
	}

	return results, nil
}

// WriteTags writes multiple PLC tags in a single request when possible.
func (d *PhoenixContactDriver) WriteTags(ctx context.Context, tags []models.PLCTag, values []interface{}) *DriverError {
	if !d.IsConnected() {
		return NewDriverError(ErrCodeNotConnected, "driver not connected", nil)
	}

	if len(tags) != len(values) {
		return NewDriverError(ErrCodeWriteFailed, "tags and values arrays must have the same length", nil)
	}

	client, err := d.getClient(ctx)
	if err != nil {
		return NewDriverError(ErrCodeWriteFailed, fmt.Sprintf("failed to get client for write: %v", err), err)
	}
	defer d.releaseClient(client)

	// For simplicity, this mock just writes them individually.
	// In a real implementation, this would group contiguous tags and use block writes.
	for i, tag := range tags {
		writeErr := d.WriteTag(ctx, tag, values[i])
		if writeErr != nil {
			return writeErr
		}
	}

	return nil
}

// ValidateAddress checks if the given address string is valid for Phoenix Contact PLCs.
// Supports PLCnext format (e.g., "Arp.Plc.Eclr/Program:MainInstance.tag") for EtherNet/IP
// and Modbus TCP addresses (e.g., "%MW100") for Modbus TCP.
func (d *PhoenixContactDriver) ValidateAddress(address string, dataType string) bool {
	if address == "" || dataType == "" {
		return false
	}

	switch d.protocol {
	case ProtocolEthernetIP:
		// PLCnext symbolic addressing: Arp.Plc.Eclr/Program:MainInstance.tag
		// Or simple tag names for global variables
		plcnextPattern := `^Arp\.Plc\.Eclr\/Program:[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*$`
		simpleTagPattern := `^[a-zA-Z_][a-zA-Z0-9_]*$`
		return regexp.MustCompile(plcnextPattern).MatchString(address) || regexp.MustCompile(simpleTagPattern).MatchString(address)
	case ProtocolModbusTCP:
		// Reuse Schneider's Modbus address validation as it's generic Modbus TCP
		// This is a simplified approach; a dedicated Phoenix Contact Modbus validation might be more specific.
		return (&SchneiderDriver{}).ValidateAddress(address, dataType)
	default:
		return false
	}
}

// GetName returns the name of the PLC driver.
func (d *PhoenixContactDriver) GetName() string {
	switch d.protocol {
	case ProtocolEthernetIP:
		return "Phoenix Contact PLCnext EtherNet/IP Driver"
	case ProtocolModbusTCP:
		return "Phoenix Contact Modbus TCP Driver"
	default:
		return "Phoenix Contact Driver (Unknown Protocol)"
	}
}

// GetSupportedDataTypes returns a list of data types supported by this driver.
func (d *PhoenixContactDriver) GetSupportedDataTypes() []string {
	// Supported data types depend on the protocol and PLC model.
	// For simplicity, return a common set.
	return []string{"BOOL", "INT", "DINT", "REAL", "STRING", "WORD", "DWORD"}
}

// Close cleans up any persistent resources held by the driver.
func (d *PhoenixContactDriver) Close() {
	d.mu.Lock()
	defer d.mu.Unlock()

	for d.activeConns > 0 {
		switch d.protocol {
		case ProtocolEthernetIP:
			client := d.connPool.Get().(*gologix.Client)
			if client != nil {
				client.Disconnect()
			}
		case ProtocolModbusTCP:
			client := d.connPool.Get().(*gomodbus.Client)
			if client != nil {
				client.Disconnect()
			}
		}
		d.activeConns--
	}
	logger.Info("Closed all Phoenix Contact driver connections.")
}

// getClient retrieves a client from the pool or creates a new one.
func (d *PhoenixContactDriver) getClient(ctx context.Context) (interface{}, error) {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.activeConns >= d.poolSize {
		return nil, fmt.Errorf("connection pool exhausted")
	}

	var client interface{}
	switch d.protocol {
	case ProtocolEthernetIP:
		client = d.connPool.Get().(*gologix.Client)
		if client == nil {
			return nil, fmt.Errorf("failed to get client from pool")
		}

		if !client.(*gologix.Client).IsConnected() {
			connectCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
			defer cancel()
			if err := client.(*gologix.Client).Connect(connectCtx); err != nil {
				d.connPool.Put(client)
				return nil, fmt.Errorf("failed to connect client: %w", err)
			}
		}
	case ProtocolModbusTCP:
		client = d.connPool.Get().(*gomodbus.Client)
		if client == nil {
			return nil, fmt.Errorf("failed to get client from pool")
		}

		if !client.(*gomodbus.Client).IsConnected() {
			connectCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
			defer cancel()
			if err := client.(*gomodbus.Client).Connect(connectCtx); err != nil {
				d.connPool.Put(client)
				return nil, fmt.Errorf("failed to connect client: %w", err)
			}
		}
	default:
		return nil, fmt.Errorf("unsupported protocol: %s", d.protocol)
	}

	d.activeConns++
	return client, nil
}

// releaseClient returns a client to the pool.
func (d *PhoenixContactDriver) releaseClient(client interface{}) {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.connPool.Put(client)
	d.activeConns--
}

// parsePLCnextAddress parses a PLCnext address into components.
// Format: Arp.Plc.Eclr/Program:MainInstance.tag
func parsePLCnextAddress(address string) (program string, instance string, tag string, err error) {
	// Check for full PLCnext format
	plcnextRegex := regexp.MustCompile(`^Arp\.Plc\.Eclr\/Program:([a-zA-Z_][a-zA-Z0-9_]*)\.([a-zA-Z_][a-zA-Z0-9_]*)$`)
	if matches := plcnextRegex.FindStringSubmatch(address); len(matches) == 3 {
		return matches[1], matches[2], "", nil
	}

	// Check for simple tag format (global variable)
	simpleRegex := regexp.MustCompile(`^([a-zA-Z_][a-zA-Z0-9_]*)$`)
	if matches := simpleRegex.FindStringSubmatch(address); len(matches) == 2 {
		return "", "", matches[1], nil
	}

	return "", "", "", fmt.Errorf("invalid PLCnext address format: %s", address)
}
