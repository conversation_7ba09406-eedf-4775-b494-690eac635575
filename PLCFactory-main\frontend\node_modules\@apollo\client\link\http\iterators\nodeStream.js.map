{"version": 3, "file": "nodeStream.js", "sourceRoot": "", "sources": ["../../../../src/link/http/iterators/nodeStream.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAGH,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAOxE,MAAM,CAAC,OAAO,UAAU,kBAAkB,CACxC,MAA0B;IAE1B,IAAI,OAAO,GAAwB,IAAI,CAAC;IACxC,IAAI,KAAK,GAAiB,IAAI,CAAC;IAC/B,IAAI,IAAI,GAAG,KAAK,CAAC;IACjB,IAAM,IAAI,GAAc,EAAE,CAAC;IAE3B,IAAM,OAAO,GAOP,EAAE,CAAC;IAET,SAAS,MAAM,CAAC,KAAU;QACxB,IAAI,KAAK;YAAE,OAAO;QAClB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,IAAM,UAAU,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;YACnC,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC/C,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IACD,SAAS,OAAO,CAAC,GAAU;QACzB,KAAK,GAAG,GAAG,CAAC;QACZ,IAAM,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QAC5B,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI;YACxB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;QACH,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;IACxB,CAAC;IACD,SAAS,KAAK;QACZ,IAAI,GAAG,IAAI,CAAC;QACZ,IAAM,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QAC5B,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI;YACxB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QACH,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;IACxB,CAAC;IAED,OAAO,GAAG;QACR,OAAO,GAAG,IAAI,CAAC;QACf,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACtC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACxC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACpC,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QACvC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACxC,CAAC,CAAC;IACF,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC1B,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5B,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACxB,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC3B,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAE1B,SAAS,OAAO;QACd,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM;YAC1C,IAAI,KAAK;gBAAE,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;YAChC,IAAI,IAAI,CAAC,MAAM;gBACb,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YAC5D,IAAI,IAAI;gBAAE,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;YAC3D,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAM,QAAQ,GAA0B;QACtC,IAAI;YACF,OAAO,OAAO,EAAE,CAAC;QACnB,CAAC;KACF,CAAC;IAEF,IAAI,yBAAyB,EAAE,CAAC;QAC9B,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;IACJ,CAAC;IAED,OAAO,QAAoC,CAAC;AAC9C,CAAC", "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/nodeStream.ts\n */\n\nimport type { Readable as NodeReadableStream } from \"stream\";\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\n\ninterface NodeStreamIterator<T> {\n  next(): Promise<IteratorResult<T, boolean | undefined>>;\n  [Symbol.asyncIterator]?(): AsyncIterator<T>;\n}\n\nexport default function nodeStreamIterator<T>(\n  stream: NodeReadableStream\n): AsyncIterableIterator<T> {\n  let cleanup: (() => void) | null = null;\n  let error: Error | null = null;\n  let done = false;\n  const data: unknown[] = [];\n\n  const waiting: [\n    (\n      value:\n        | IteratorResult<T, boolean | undefined>\n        | PromiseLike<IteratorResult<T, boolean | undefined>>\n    ) => void,\n    (reason?: any) => void,\n  ][] = [];\n\n  function onData(chunk: any) {\n    if (error) return;\n    if (waiting.length) {\n      const shiftedArr = waiting.shift();\n      if (Array.isArray(shiftedArr) && shiftedArr[0]) {\n        return shiftedArr[0]({ value: chunk, done: false });\n      }\n    }\n    data.push(chunk);\n  }\n  function onError(err: Error) {\n    error = err;\n    const all = waiting.slice();\n    all.forEach(function (pair) {\n      pair[1](err);\n    });\n    !cleanup || cleanup();\n  }\n  function onEnd() {\n    done = true;\n    const all = waiting.slice();\n    all.forEach(function (pair) {\n      pair[0]({ value: undefined, done: true });\n    });\n    !cleanup || cleanup();\n  }\n\n  cleanup = function () {\n    cleanup = null;\n    stream.removeListener(\"data\", onData);\n    stream.removeListener(\"error\", onError);\n    stream.removeListener(\"end\", onEnd);\n    stream.removeListener(\"finish\", onEnd);\n    stream.removeListener(\"close\", onEnd);\n  };\n  stream.on(\"data\", onData);\n  stream.on(\"error\", onError);\n  stream.on(\"end\", onEnd);\n  stream.on(\"finish\", onEnd);\n  stream.on(\"close\", onEnd);\n\n  function getNext(): Promise<IteratorResult<T, boolean | undefined>> {\n    return new Promise(function (resolve, reject) {\n      if (error) return reject(error);\n      if (data.length)\n        return resolve({ value: data.shift() as T, done: false });\n      if (done) return resolve({ value: undefined, done: true });\n      waiting.push([resolve, reject]);\n    });\n  }\n\n  const iterator: NodeStreamIterator<T> = {\n    next(): Promise<IteratorResult<T, boolean | undefined>> {\n      return getNext();\n    },\n  };\n\n  if (canUseAsyncIteratorSymbol) {\n    iterator[Symbol.asyncIterator] = function (): AsyncIterator<T> {\n      return this;\n    };\n  }\n\n  return iterator as AsyncIterableIterator<T>;\n}\n"]}