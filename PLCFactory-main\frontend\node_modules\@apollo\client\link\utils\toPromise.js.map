{"version": 3, "file": "toPromise.js", "sourceRoot": "", "sources": ["../../../src/link/utils/toPromise.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAG7D,MAAM,UAAU,SAAS,CAAI,UAAyB;IACpD,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,OAAO,IAAI,OAAO,CAAI,UAAC,OAAO,EAAE,MAAM;QACpC,UAAU,CAAC,SAAS,CAAC;YACnB,IAAI,EAAE,UAAC,IAAI;gBACT,IAAI,SAAS,EAAE,CAAC;oBACd,SAAS,CAAC,IAAI,CACZ,mEAAmE,CACpE,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,SAAS,GAAG,IAAI,CAAC;oBACjB,OAAO,CAAC,IAAI,CAAC,CAAC;gBAChB,CAAC;YACH,CAAC;YACD,KAAK,EAAE,MAAM;SACd,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\nimport type { Observable } from \"../../utilities/index.js\";\n\nexport function toPromise<R>(observable: Observable<R>): Promise<R> {\n  let completed = false;\n  return new Promise<R>((resolve, reject) => {\n    observable.subscribe({\n      next: (data) => {\n        if (completed) {\n          invariant.warn(\n            `Promise Wrapper does not support multiple results from Observable`\n          );\n        } else {\n          completed = true;\n          resolve(data);\n        }\n      },\n      error: reject,\n    });\n  });\n}\n"]}