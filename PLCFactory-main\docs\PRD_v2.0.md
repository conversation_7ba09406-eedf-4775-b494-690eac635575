# PLC Factory — Product Requirements Document (PRD) v 2.0  
Status : Draft  
Author : Product & UX Group, Factory AI  
Last Updated : 17 June 2025  

---

## 0 . Executive Summary  
PLC Factory is a cloud-native automation studio that lets anyone—from a maintenance tech to a senior controls engineer—design, troubleshoot and deploy PLC applications faster than ever.  A hybrid editor pairs an intuitive node-based builder with a real-time code / ladder view, so users can work visually or directly in the language their PLC speaks.  The aim: cut engineering time, lower training barriers and make industrial logic as collaborative and elegant as modern software development tools.

---

## 1 . Product Vision  

| Goal | Description |
|------|-------------|
| Democratise PLC development | Provide a workflow where non-experts drag, drop and connect functional blocks while experts can refine the generated code. |
| Vendor-agnostic exports | A single project can be output as Allen-Bradley Ladder (L5X), Siemens SCL, IEC-61131 ST, etc. |
| Real-time insight | Integrated live tag monitor, diagnostics and versioned deployments from the same UI. |
| Collaboration built-in | Multiplayer editing, comments and git-like history enable teams to work together across sites. |
| Professional polish | First launch should feel like the “Figma” of automation—responsive, refined, dark-mode native. |

---

## 2 . Target Personas  

| Persona | Primary Jobs-to-Be-Done | Key UX Needs |
|---------|------------------------|--------------|
| Maintenance Technician | • Locate fault < 5 min • Hot-patch a timer value • Verify I/O status | Mobile-friendly interface, visual ladder playback, minimal jargon |
| Controls Engineer | • Create new machine logic • Refactor imported L5X • Validate safety | Hybrid editor, deep vendor options, unit-test harness |
| System Integrator | • Template 10+ lines • Bulk export to multiple vendors | Batch CLI, parameterised templates, CI web-hooks |
| Engineering Manager | • Track progress & KPIs • Approve releases | Dashboards, audit trail, e-signatures |

---

## 3 . UI / UX Requirements  

### 3.1 Look & Feel  
1. Dark theme by default; soft gradients, subtle depth.  
2. Consistent iconography (Material Symbols rounded).  
3. 60 fps interactions on a 4 k monitor; canvas zoom < 8 ms latency.  
4. Zero clutter—primary actions surfaced, advanced options in drawers.  

### 3.2 Navigation & Layout  
```
┌ AppBar (logo • project selector • status) ┐
│ Drawer ← projects / settings              │
│ Canvas / Code Split                       │
│ Right Drawer → properties / diagnostics   │
└ Status bar  (zoom • coords • validation)  ┘
```
• All menu items functional on first run—no dead links.  
• Ctrl + 1/2/3 toggles Visual / Code / Split instantly.  

### 3.3 Interaction Principles  
- Drag-drop nodes, rubber-band multiple select.  
- Inline value editing with double-click.  
- Hover reveals I/O data types; color by Bool / Int / Real.  
- Undo/redo across visual + code views (Ctrl + Z/Y).  
- Ladder rungs rendered as SVG, scroll-zoom identical to RSLogix feel.  

### 3.4 Accessibility  
- Full keyboard parity (Tab nav, arrow pan, Delete).  
- Screen-reader labels for all controls; WCAG 2.1 AA.  
- Colour-blind-safe palette.  

---

## 4 . Functional Requirements  

### 4.1 Visual Logic Engine  
- Node library covering IEC 61131-3 plus vendor blocks.  
- Custom nodes packaged via JSON-schema; versioned.  
- Live validation: type, cycle-time estimate, unconnected ports.  

### 4.2 Code / Ladder View  
- One-click transpile to ST, SCL, L5X.  
- Ladder renderer: coils, contacts, rungs, comments.  
- Bidirectional sync ↔ edits in code update nodes where deterministic.  

### 4.3 Real-Time Collaboration  
- Y-CRDT multiplayer, presence cursors ≤ 3 s heartbeat.  
- Threaded comments anchored to node or line of code.  
- Branch/merge with visual diff of rungs and nodes.  

### 4.4 Live PLC Connectivity  
- Drivers: AB (EtherNet/IP), Siemens (S7), Mitsubishi (MC), Schneider (Modbus TCP), Omron (FINS), Phoenix (EIP/Modbus), GE (SRTP).  
- Tag monitor side-panel; drag tag onto HMI widget.  
- Hot-patch variables with safety guard matrix.  

### 4.5 HMI Designer (Phase 1 subset)  
- Snap-grid canvas, basic widgets (button, indicator, gauge).  
- Tag binding wizard.  
- Export to FactoryTalk ViewSE & generic Web HMI.  

### 4.6 Project & Asset Management  
- Create / clone / template projects.  
- Version history, semantic version tagging.  
- Download artifact bundle (*.zip) containing PLC, HMI, docs.  

---

## 5 . Expected Workflows  

| Scenario | Steps (happy path) |
|----------|--------------------|
| **New Program (Tech)** | Dashboard → “New Program” → choose template → drag nodes → Validate ✔ → Transpile → Download L5X → Deploy |
| **Hot Fix (Tech)** | Live view → click rung → adjust timer preset → Hot-patch ✓ (audit logged) |
| **Green-field (Engineer)** | Projects → “Create” → import Eplan XML → build logic visually → open Code view, refine ST → Run simulation → Merge to “main” |
| **Multi-site roll-out (Integrator)** | CLI `pf build --matrix siemens,ab` → artifacts per line → push to GitOps |

---

## 6 . Non-Functional Requirements  

| Area | Target |
|------|--------|
| Performance | Canvas ≥ 60 fps, transpile 1 k nodes < 2 s |
| Scalability | 100 concurrent editors / project |
| Availability | 99.9 % monthly uptime |
| Security | OIDC + MFA, AES-256 at rest, TLS 1.3 |
| Compliance | ISO 27001 baseline; option for ITAR / 21 CFR 11 |
| Extensibility | Plugin SDK (React/TS) sandboxed via iframe ‑ postMessage |

---

## 7 . Success Metrics  

| KPI | Target v2.0 |
|-----|-------------|
| Time-to-first-program | < 15 minutes for new technician |
| Visual-to-code parity | 95 % round-trip fidelity |
| User satisfaction (SUS) | ≥ 80 |
| Bug fix cycle | < 3 days P1 |

---

## 8 . Open Questions / Risks  
1. Ladder ↔ Node sync ambiguity for complex rungs—define deterministic rules?  
2. Vendor EULAs on code generation—legal review required.  
3. Real-time collaboration bandwidth in low-bandwidth plants.  
4. Safety PLCs (SIL) support roadmap.  

---

_End of Document_
