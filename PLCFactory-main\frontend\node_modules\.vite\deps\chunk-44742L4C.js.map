{"version": 3, "sources": ["../../@apollo/src/errors/index.ts"], "sourcesContent": ["import \"../utilities/globals/index.js\";\n\nimport type {\n  GraphQLError,\n  GraphQLErrorExtensions,\n  GraphQLFormattedError,\n} from \"graphql\";\n\nimport { isNonNullObject } from \"../utilities/index.js\";\nimport type { ServerParseError } from \"../link/http/index.js\";\nimport type { ServerError } from \"../link/utils/index.js\";\nimport type { FetchResult } from \"../link/core/index.js\";\n\n// This Symbol allows us to pass transport-specific errors from the link chain\n// into QueryManager/client internals without risking a naming collision within\n// extensions (which implementers can use as they see fit).\nexport const PROTOCOL_ERRORS_SYMBOL: unique symbol = Symbol();\n\ntype FetchResultWithSymbolExtensions<T> = FetchResult<T> & {\n  extensions: Record<string | symbol, any>;\n};\n\nexport interface ApolloErrorOptions {\n  graphQLErrors?: ReadonlyArray<GraphQLFormattedError>;\n  protocolErrors?: ReadonlyArray<GraphQLFormattedError>;\n  clientErrors?: ReadonlyArray<Error>;\n  networkError?: Error | ServerParseError | ServerError | null;\n  errorMessage?: string;\n  extraInfo?: any;\n}\n\nexport function graphQLResultHasProtocolErrors<T>(\n  result: FetchResult<T>\n): result is FetchResultWithSymbolExtensions<T> {\n  if (result.extensions) {\n    return Array.isArray(\n      (result as FetchResultWithSymbolExtensions<T>).extensions[\n        PROTOCOL_ERRORS_SYMBOL\n      ]\n    );\n  }\n  return false;\n}\n\nexport function isApolloError(err: Error): err is ApolloError {\n  return err.hasOwnProperty(\"graphQLErrors\");\n}\n\n// Sets the error message on this error according to the\n// the GraphQL and network errors that are present.\n// If the error message has already been set through the\n// constructor or otherwise, this function is a nop.\nconst generateErrorMessage = (err: ApolloError) => {\n  const errors = [\n    ...err.graphQLErrors,\n    ...err.clientErrors,\n    ...err.protocolErrors,\n  ];\n  if (err.networkError) errors.push(err.networkError);\n  return (\n    errors\n      // The rest of the code sometimes unsafely types non-Error objects as GraphQLErrors\n      .map(\n        (err) =>\n          (isNonNullObject(err) && err.message) || \"Error message not found.\"\n      )\n      .join(\"\\n\")\n  );\n};\n\n/**\n * @deprecated This type is deprecated and will be removed in the next major version of Apollo Client.\n * It mistakenly referenced `GraqhQLError` instead of `GraphQLFormattedError`.\n *\n * Use `ReadonlyArray<GraphQLFormattedError>` instead.\n */\n// eslint-disable-next-line @typescript-eslint/no-restricted-types\nexport type GraphQLErrors = ReadonlyArray<GraphQLError>;\n\nexport type NetworkError = Error | ServerParseError | ServerError | null;\n\nexport class ApolloError extends Error {\n  public name: string;\n  public message: string;\n  public graphQLErrors: ReadonlyArray<GraphQLFormattedError>;\n  public protocolErrors: ReadonlyArray<GraphQLFormattedError>;\n  public clientErrors: ReadonlyArray<Error>;\n  public networkError: Error | ServerParseError | ServerError | null;\n  /**\n   * Indicates the specific original cause of the error.\n   *\n   * This field contains the first available `networkError`, `graphQLError`, `protocolError`, `clientError`, or `null` if none are available.\n   */\n  public cause:\n    | ({\n        readonly message: string;\n        extensions?:\n          | GraphQLErrorExtensions[]\n          | GraphQLFormattedError[\"extensions\"];\n      } & Omit<Partial<Error> & Partial<GraphQLFormattedError>, \"extensions\">)\n    | null;\n\n  // An object that can be used to provide some additional information\n  // about an error, e.g. specifying the type of error this is. Used\n  // internally within Apollo Client.\n  public extraInfo: any;\n\n  // Constructs an instance of ApolloError given serialized GraphQL errors,\n  // client errors, protocol errors or network errors.\n  // Note that one of these has to be a valid\n  // value or the constructed error will be meaningless.\n  constructor({\n    graphQLErrors,\n    protocolErrors,\n    clientErrors,\n    networkError,\n    errorMessage,\n    extraInfo,\n  }: ApolloErrorOptions) {\n    super(errorMessage);\n    this.name = \"ApolloError\";\n    this.graphQLErrors = graphQLErrors || [];\n    this.protocolErrors = protocolErrors || [];\n    this.clientErrors = clientErrors || [];\n    this.networkError = networkError || null;\n    this.message = errorMessage || generateErrorMessage(this);\n    this.extraInfo = extraInfo;\n    this.cause =\n      [\n        networkError,\n        ...(graphQLErrors || []),\n        ...(protocolErrors || []),\n        ...(clientErrors || []),\n      ].find((e) => !!e) || null;\n\n    // We're not using `Object.setPrototypeOf` here as it isn't fully\n    // supported on Android (see issue #3236).\n    (this as any).__proto__ = ApolloError.prototype;\n  }\n}\n"], "mappings": ";;;;;;;AAgBO,IAAM,yBAAwC,OAAM;AAerD,SAAU,+BACd,QAAsB;AAEtB,MAAI,OAAO,YAAY;AACrB,WAAO,MAAM,QACV,OAA8C,WAC7C,sBAAsB,CACvB;EAEL;AACA,SAAO;AACT;AAEM,SAAU,cAAc,KAAU;AACtC,SAAO,IAAI,eAAe,eAAe;AAC3C;AAMA,IAAM,uBAAuB,SAAC,KAAgB;AAC5C,MAAM,SAAM,cAAA,cAAA,cAAA,CAAA,GACP,IAAI,eAAa,IAAA,GACjB,IAAI,cAAY,IAAA,GAChB,IAAI,gBAAc,IAAA;AAEvB,MAAI,IAAI;AAAc,WAAO,KAAK,IAAI,YAAY;AAClD,SACE,OAEG,IACC,SAACA,MAAG;AACF,WAAC,gBAAgBA,IAAG,KAAKA,KAAI,WAAY;EAAzC,CAAmE,EAEtE,KAAK,IAAI;AAEhB;AAaA,IAAA;;EAAA,SAAA,QAAA;AAAiC,cAAAC,cAAA,MAAA;AA8B/B,aAAAA,aAAY,IAOS;UANnB,gBAAa,GAAA,eACb,iBAAc,GAAA,gBACd,eAAY,GAAA,cACZ,eAAY,GAAA,cACZ,eAAY,GAAA,cACZ,YAAS,GAAA;AAET,UAAA,QAAA,OAAK,KAAA,MAAC,YAAY,KAAC;AACnB,YAAK,OAAO;AACZ,YAAK,gBAAgB,iBAAiB,CAAA;AACtC,YAAK,iBAAiB,kBAAkB,CAAA;AACxC,YAAK,eAAe,gBAAgB,CAAA;AACpC,YAAK,eAAe,gBAAgB;AACpC,YAAK,UAAU,gBAAgB,qBAAqB,KAAI;AACxD,YAAK,YAAY;AACjB,YAAK,QACH,cAAA,cAAA,cAAA;QACE;SACI,iBAAiB,CAAA,GAAG,IAAA,GACpB,kBAAkB,CAAA,GAAG,IAAA,GACrB,gBAAgB,CAAA,GAAG,IAAA,EACvB,KAAK,SAAC,GAAC;AAAK,eAAA,CAAC,CAAC;MAAF,CAAG,KAAK;AAIvB,YAAa,YAAYA,aAAY;;IACxC;AACF,WAAAA;EAAA,EA1DiC,KAAK;;", "names": ["err", "ApolloError"]}