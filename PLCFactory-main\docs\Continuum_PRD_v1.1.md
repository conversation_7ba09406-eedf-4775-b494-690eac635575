# Project "Continuum"  
Expanded Product Requirements Document (PRD) – v1.1  
Status     : Draft  
Author     : Factory AI – Software Engineering Group  
Date       : 14 June 2025  

---

## 0. Table of Contents
1. Introduction & Vision  
2. Personas & Detailed Workflows  
3. Core Epics & Feature Specifications  
4. Technical Architecture  
5. Integration & API Requirements  
6. Performance & Scalability Targets  
7. Security & Compliance  
8. Implementation Phasing & Priorities  
9. Testing & Quality Assurance Strategy  
10. Support, Training & Change-Management  
11. Open Questions / Risks  

---

## 1. Introduction & Vision
Continuum is a cloud-native, vendor-agnostic automation platform that unifies machine logic, HMI, drive tuning and AI-based optimisation into a single collaborative workspace.

---

## 2. Personas & Detailed User Workflows

### 2.1 Maintenance Technician (Primary)
**Goal: Diagnose machine fault in < 5 min**
1. Open Continuum dashboard on rugged tablet → live asset alert surfaces fault code
2. Click "Jump to logic" → visual logic canvas loads frozen replay at fault timestamp
3. Timeline scrubber visualises state changes; red overlay identifies anomalous node
4. Technician toggles "field view" to see wiring diagram + device manual
5. Applies approved fix, logs comment, presses "Request redeploy"

**Goal: Minor parameter tweak**
Use "Quick-edit" mode on running node, adjust single set-point → system validates safety constraints → hot-patch deploy.

### 2.2 Controls Engineer
**Goal: Green-field program**
1. Create Project → select hardware template → imports tag database from E-plan XML
2. Drag macros from library (e.g., "Pump Station") onto canvas, auto-asset-tagging
3. Unit-test inside soft-PLC simulator with digital twin tags
4. Generate Ladder for AB ControlLogix, push to Git-style branch, open PR
5. Peer review & merge → CI pipeline signs L5X and publishes release artifact

**Goal: Retro-fit existing code**
Upload L5K → Import Engine reverse-renders nodes → engineer refactors into encapsulated modules → commits.

### 2.3 System Integrator
Multi-site template creation; bulk export for Siemens, Rockwell, Phoenix Contact concurrently; uses CLI to automate build matrix.

### 2.4 Engineering Manager
Dashboards, KPI rollups, audit trail, approval gates.

---

## 3. Core Epics & Specifications

### 3.1 Epic: Visual Logic Engine
- **Canvas**: WebGL-accelerated infinite plane; ≥60 FPS on 4k; pan/zoom < 8 ms latency
- **Node types**: Cover IEC 61131-3 element set + vendor extensions; defined in JSON-Schema
- **Custom Nodes**: Saved as versioned packages; input/output contracts typed; support parameter dialog & embedded ST/structured-text script
- **Validation**: Static: schema + type; Dynamic: simulation step (scan time ≤10 ms/1k nodes)
- **Accessibility**: Keyboard wiring, screen-reader labels

### 3.2 Epic: Universal Code Transpiler
- **Supported targets (Phase 1)**: AB Logix (.L5X LD + AOI XML), Siemens TIA (SCL, FBD), Generic IEC ST
- **Architecture**: Each target is an isolated worker (Rust) using trait-based IR visitor; test corpus of 500 programs, diff equivalence via PLCopen XML
- **Import Engine**: Parsing using ANTLR grammars → IR; 95% round-trip fidelity KPI
- **CLI**: `continuum build --target ab-logix --out dist/` returns signed artifact

### 3.3 Epic: Real-Time Collaboration Hub
- **Multiplayer**: Y-CRDT operational transform, conflict-free
- **Presence**: WebSocket channel, presence heartbeat < 3 s
- **Commenting**: Anchored to node UUID; supports Markdown, image paste
- **Versioning**: Append-only DAG; diff renderer; branch/merge

### 3.4 Epic: Integrated Design Environment
- **HMI Designer**: React-based; component binding wizard to PLC tags; generates vendor-specific screen exports (FactoryTalk View SE, WinCC)
- **Drive Config**: Parameter matrix abstraction; OPC-UA / Ethernet-IP drivers; autotune wizard

### 3.5 Epic: AI-Powered "Ghost"
- **Data ingest**: MQTT/OPC-UA collector pods, stream to Kafka; schema auto-registration
- **Online model**: Edge Python micro-service runs temporal GNN predicting next-step tag values (latency < 50 ms)
- **Anomaly rules**: Unsupervised (Isolation-Forest) + rule engine; severity scoring
- **Suggestion engine**: Retrieval-augmented LLM fine-tuned on 10k PLC snippets; returns patch diff + English explanation
- **Safety guard**: Recommended fixes run through static analyser & "safety matrix" before presented

### 3.6 Epic: Project & Asset Management
- **Global Search** (Elastic) across tag names, commits, comments
- **Audit**: Immutable ledger (PostgreSQL + HashiCorp Vault); CFR 21 Part 11 e-Sig support
- **Dashboard**: Machine OEE, MTTR widgets

---

## 4. Technical Architecture

```
Client (PWA) ←→ Gateway (GraphQL) ←→ Microservices
   ├─ Node-Editor-Svc  (Elixir + Phoenix channels)
   ├─ Transpiler-Svc   (Rust workers)  – gRPC
   ├─ Ghost AI-Svc     (Python, TorchServe)
   ├─ HMI-Svc          (Go)
   ├─ AuthN/Z          (Keycloak, OIDC, RBAC)
   └─ Event Bus        (Kafka) → TimescaleDB (telemetry)
Storage:  
• Project graph → MongoDB (document)  
• Metadata → Postgres  
• Binary assets → S3 compatible (object)  
Deploy: Helm charts on EKS; zero-downtime blue/green.
```

---

## 5. Integration & API Requirements
- **REST/GraphQL**: `/v1/projects/{id}/build?target=ab_logix` returns presigned URL
- **Webhooks**: `build.completed`, `anomaly.detected`
- **OPC-UA Gateway**: Auto-discovers endpoints, maps to internal tag path
- **Plug-in SDK**: JS/TS; Sandbox iframe with postMessage; allowed capabilities list

---

## 6. Performance & Scalability
- **Canvas**: 50k nodes project opens < 4 s
- **Transpile**: 1k nodes → ST in < 2 s on 4-core worker
- **Collab**: Support 100 concurrent editors / project
- **Data ingest**: 25k tag/s per asset, horizontally scalable

---

## 7. Security & Compliance
- **Auth**: OIDC + MFA; SCIM provisioning
- **Data**: AES-256 at rest, TLS 1.3 in transit
- **Tenant isolation**: Kubernetes namespace per tenant; network policies
- **Compliance**: ISO 27001 baseline; optional ITAR/SOC 2-Type II
- **Code signing**: Build artifacts signed with PKI; checksum verified by deployment agent before flash
- **Safety**: IEC 61508 workflow hooks (approval gates, hazard log)

---

## 8. Implementation Phasing & Priorities

### Phase 0 – Foundations (Q3 2025)
Auth, project DB, basic canvas, AB Ladder export

### Phase 1 – MVP (Q4 2025)
Visual Logic Engine v1, Import for AB L5X, live collaboration, basic HMI designer

### Phase 2 – Multivendor (Q2 2026)
Siemens transpiler, generic ST, CLI pipeline, drive config

### Phase 3 – Ghost Alpha (Q3 2026)
Data ingest, anomaly detection dashboard

### Phase 4 – Ghost Assist (Q1 2027)
AI suggestions, auto-patch with approval

### Phase 5 – Enterprise (Q2 2027)
CFR 21 Part 11, ITAR, on-prem appliance

---

## 9. Testing & Quality Assurance
- **Unit**: ≥90% coverage of transpiler IR and node operations
- **Simulation harness**: Deterministic scan cycle verifies logical equivalence with vendor runtimes
- **Regression corpus**: Public sample PLC programs
- **End-to-end**: Cypress scripted multi-user sessions
- **AI validation**: Offline sandbox executes suggested patch; must reduce error metric vs baseline
- **Performance**: Locust load tests on WebSocket collab
- **Security**: Automated SCA, DAST, container image scanning

---

## 10. Support & Training Model

### Support Tiers
- **Community**: Best-effort (Forum, Discord)
- **Standard**: 8×5, 4h response (Ticket portal)
- **Premium**: 24×7, 1h (Phone, dedicated CSM)

### Training offerings
1. Web-based self-paced modules (intro, migration, AI use)
2. 3-day instructor-led "Continuum Certified Technician"
3. Partner integrator accreditation program

### Change-Management
Provide "sandbox tenant", migration wizards, feature flags

---

## 11. Open Questions / Risks
1. Legal exposure of auto-generated code for safety PLCs
2. Vendor cooperation for closed ECU file formats
3. Edge deployment latency for Ghost in air-gapped plants
4. Training data licensing for LLM fine-tune

---  
End of Document 