{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/link/remove-typename/index.ts"], "names": [], "mappings": "AACA,OAAO,EACL,2BAA2B,EAC3B,IAAI,GACL,MAAM,kCAAkC,CAAC", "sourcesContent": ["export type { RemoveTypenameFromVariablesOptions } from \"./removeTypenameFromVariables.js\";\nexport {\n  removeTypenameFromVariables,\n  KEEP,\n} from \"./removeTypenameFromVariables.js\";\n"]}