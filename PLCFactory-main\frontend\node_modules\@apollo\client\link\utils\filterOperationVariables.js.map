{"version": 3, "file": "filterOperationVariables.js", "sourceRoot": "", "sources": ["../../../src/link/utils/filterOperationVariables.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,MAAM,UAAU,wBAAwB,CACtC,SAA8B,EAC9B,KAAmB;IAEnB,IAAM,MAAM,gBAAQ,SAAS,CAAE,CAAC;IAChC,IAAM,WAAW,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IACpD,KAAK,CAAC,KAAK,EAAE;QACX,QAAQ,YAAC,IAAI,EAAE,IAAI,EAAE,MAAM;YACzB,gEAAgE;YAChE,gEAAgE;YAChE,yDAAyD;YACzD,yDAAyD;YACzD,IACE,MAAM;gBACL,MAAiC,CAAC,IAAI,KAAK,oBAAoB,EAChE,CAAC;gBACD,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;KACF,CAAC,CAAC;IACH,WAAW,CAAC,OAAO,CAAC,UAAC,IAAI;QACvB,OAAO,MAAO,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import type { VariableDefinitionNode, DocumentNode } from \"graphql\";\nimport { visit } from \"graphql\";\n\nexport function filterOperationVariables(\n  variables: Record<string, any>,\n  query: DocumentNode\n) {\n  const result = { ...variables };\n  const unusedNames = new Set(Object.keys(variables));\n  visit(query, {\n    Variable(node, _key, parent) {\n      // A variable type definition at the top level of a query is not\n      // enough to silence server-side errors about the variable being\n      // unused, so variable definitions do not count as usage.\n      // https://spec.graphql.org/draft/#sec-All-Variables-Used\n      if (\n        parent &&\n        (parent as VariableDefinitionNode).kind !== \"VariableDefinition\"\n      ) {\n        unusedNames.delete(node.name.value);\n      }\n    },\n  });\n  unusedNames.forEach((name) => {\n    delete result![name];\n  });\n  return result;\n}\n"]}