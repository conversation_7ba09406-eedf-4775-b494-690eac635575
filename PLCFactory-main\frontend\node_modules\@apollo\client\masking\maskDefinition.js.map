{"version": 3, "file": "maskDefinition.js", "sourceRoot": "", "sources": ["../../src/masking/maskDefinition.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,OAAO,EACL,mBAAmB,EACnB,eAAe,EACf,sBAAsB,GACvB,MAAM,uBAAuB,CAAC;AAG/B,OAAO,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAC;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAW1D,MAAM,UAAU,cAAc,CAC5B,IAAyB,EACzB,YAA8B,EAC9B,OAAuB;IAEvB,OAAO,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE;QACzC,IAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAEpE,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,CAAC,CAAC;QAC1B,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,gBAAgB,CACvB,IAAyB,EACzB,cAAiC;IAEjC,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7B,OAAO,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,IAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACrE,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IACxC,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,SAAS,gBAAgB,CACvB,IAAS,EACT,YAA8B,EAC9B,OAAuB,EACvB,SAAkB,EAClB,IAAyB;;IAEjB,IAAA,YAAY,GAAK,OAAO,aAAZ,CAAa;IACjC,IAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;IAE5D,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,KAA4B,UAA0B,EAA1B,KAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAA1B,cAA0B,EAA1B,IAA0B,EAAE,CAAC;YAA9C,IAAA,WAAa,EAAZ,KAAK,QAAA,EAAE,IAAI,QAAA;YACrB,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;gBACnB,SAAS;YACX,CAAC;YAED,IAAM,MAAM,GAAG,gBAAgB,CAC7B,IAAI,EACJ,YAAY,EACZ,OAAO,EACP,SAAS,EACT,OAAO,CAAC,CAAC,CAAC,UAAG,IAAI,IAAI,EAAE,cAAI,KAAK,MAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAC7C,CAAC;YACF,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC7B,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;YAED,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;QACvB,CAAC;QAED,OAAO,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9C,CAAC;IAED,KAAwB,UAAuB,EAAvB,KAAA,YAAY,CAAC,UAAU,EAAvB,cAAuB,EAAvB,IAAuB,EAAE,CAAC;QAA7C,IAAM,SAAS,SAAA;QAClB,IAAI,KAAK,SAAK,CAAC;QAEf,4DAA4D;QAC5D,yDAAyD;QACzD,IAAI,SAAS,EAAE,CAAC;YACd,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;YAClC,IAAM,OAAO,GAAG,sBAAsB,CAAC,SAAS,CAAC,CAAC;YAClD,IAAM,iBAAiB,GAAG,SAAS,CAAC,YAAY,CAAC;YAEjD,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;YAEvC,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC;gBACrB,SAAS;YACX,CAAC;YAED,IAAI,iBAAiB,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACxC,IAAM,MAAM,GAAG,gBAAgB,CAC7B,IAAI,CAAC,OAAO,CAAC,EACb,iBAAiB,EACjB,OAAO,EACP,SAAS,EACT,OAAO,CAAC,CAAC,CAAC,UAAG,IAAI,IAAI,EAAE,cAAI,OAAO,CAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAC9C,CAAC;gBAEF,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,KAAK,GAAG,MAAM,CAAC;gBACjB,CAAC;YACH,CAAC;YAED,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;YACxB,CAAC;YACD,IAAI,OAAO,EAAE,CAAC;gBACZ,IACE,SAAS;oBACT,OAAO,KAAK,YAAY;oBACxB,qDAAqD;oBACrD,yDAAyD;oBACzD,sDAAsD;oBACtD,wBAAwB;oBACxB,CAAC,CAAA,MAAA,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,OAAO,CAAC,0CAAE,KAAK,CAAA,EACtD,CAAC;oBACD,MAAM,CAAC,cAAc,CACnB,IAAI,EACJ,OAAO,EACP,4BAA4B,CAC1B,OAAO,EACP,KAAK,EACL,IAAI,IAAI,EAAE,EACV,OAAO,CAAC,aAAa,EACrB,OAAO,CAAC,aAAa,CACtB,CACF,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;oBACrB,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;gBACxB,CAAC;YACH,CAAC;QACH,CAAC;QAED,IACE,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,eAAe;YACvC,CAAC,CAAC,SAAS,CAAC,aAAa;gBACvB,OAAO,CAAC,KAAK,CAAC,eAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,EAC7D,CAAC;YACD,KAAK,GAAG,gBAAgB,CACtB,IAAI,EACJ,SAAS,CAAC,YAAY,EACtB,OAAO,EACP,SAAS,EACT,IAAI,CACL,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5C,IAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;YAC1C,IAAM,QAAQ,GACZ,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC;gBACjC,CAAC,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC;oBAChC,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,YAAY,CAAE,CAAC,CAAC;YACjD,SAAS,CACP,QAAQ,EACR,yCAAyC,EACzC,YAAY,CACb,CAAC;YAEF,IAAM,IAAI,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;YAE5C,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;gBACpB,KAAK,GAAG,gBAAgB,CACtB,IAAI,EACJ,QAAQ,CAAC,YAAY,EACrB,OAAO,EACP,IAAI,KAAK,SAAS,EAClB,IAAI,CACL,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAED,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC,CAAC,YAAY,IAAI,IAAI,CAAC,EAAE,CAAC;QACpD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACpC,CAAC;IAED,oEAAoE;IACpE,iEAAiE;IACjE,mDAAmD;IACnD,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;QAC1D,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED,OAAO,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC9C,CAAC;AAED,SAAS,4BAA4B,CACnC,SAAiB,EACjB,KAAU,EACV,IAAY,EACZ,aAAiC,EACjC,aAAqB;IAErB,IAAI,QAAQ,GAAG;QACb,IAAI,mBAAmB,CAAC,QAAQ,EAAE,EAAE,CAAC;YACnC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,CAAC,IAAI,CACZ,yJAAyJ,EACzJ,aAAa,CAAC,CAAC;YACb,UAAG,aAAa,eAAK,aAAa,MAAG;YACvC,CAAC,CAAC,oBAAa,aAAa,CAAE,EAC9B,UAAG,IAAI,cAAI,SAAS,CAAE,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAC1C,CAAC;QAEF,QAAQ,GAAG,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC;QAEvB,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,OAAO;QACL,GAAG;YACD,OAAO,QAAQ,EAAE,CAAC;QACpB,CAAC;QACD,GAAG,YAAC,QAAQ;YACV,QAAQ,GAAG,cAAM,OAAA,QAAQ,EAAR,CAAQ,CAAC;QAC5B,CAAC;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACnB,CAAC;AACJ,CAAC", "sourcesContent": ["import { Kind } from \"graphql\";\nimport type { FragmentDefinitionNode, SelectionSetNode } from \"graphql\";\nimport {\n  getFragmentMaskMode,\n  maybeDeepFreeze,\n  resultKeyNameFromField,\n} from \"../utilities/index.js\";\nimport type { FragmentMap } from \"../utilities/index.js\";\nimport type { ApolloCache } from \"../cache/index.js\";\nimport { disableWarningsSlot } from \"./utils.js\";\nimport { invariant } from \"../utilities/globals/index.js\";\n\ninterface MaskingContext {\n  operationType: \"query\" | \"mutation\" | \"subscription\" | \"fragment\";\n  operationName: string | undefined;\n  fragmentMap: FragmentMap;\n  cache: ApolloCache<unknown>;\n  mutableTargets: WeakMap<any, any>;\n  knownChanged: WeakSet<any>;\n}\n\nexport function maskDefinition(\n  data: Record<string, any>,\n  selectionSet: SelectionSetNode,\n  context: MaskingContext\n) {\n  return disableWarningsSlot.withValue(true, () => {\n    const masked = maskSelectionSet(data, selectionSet, context, false);\n\n    if (Object.isFrozen(data)) {\n      maybeDeepFreeze(masked);\n    }\n    return masked;\n  });\n}\n\nfunction getMutableTarget(\n  data: Record<string, any>,\n  mutableTargets: WeakMap<any, any>\n): typeof data {\n  if (mutableTargets.has(data)) {\n    return mutableTargets.get(data);\n  }\n\n  const mutableTarget = Array.isArray(data) ? [] : Object.create(null);\n  mutableTargets.set(data, mutableTarget);\n  return mutableTarget;\n}\n\nfunction maskSelectionSet(\n  data: any,\n  selectionSet: SelectionSetNode,\n  context: MaskingContext,\n  migration: boolean,\n  path?: string | undefined\n): typeof data {\n  const { knownChanged } = context;\n  const memo = getMutableTarget(data, context.mutableTargets);\n\n  if (Array.isArray(data)) {\n    for (const [index, item] of Array.from(data.entries())) {\n      if (item === null) {\n        memo[index] = null;\n        continue;\n      }\n\n      const masked = maskSelectionSet(\n        item,\n        selectionSet,\n        context,\n        migration,\n        __DEV__ ? `${path || \"\"}[${index}]` : void 0\n      );\n      if (knownChanged.has(masked)) {\n        knownChanged.add(memo);\n      }\n\n      memo[index] = masked;\n    }\n\n    return knownChanged.has(memo) ? memo : data;\n  }\n\n  for (const selection of selectionSet.selections) {\n    let value: any;\n\n    // we later want to add acessor warnings to the final result\n    // so we need a new object to add the accessor warning to\n    if (migration) {\n      knownChanged.add(memo);\n    }\n\n    if (selection.kind === Kind.FIELD) {\n      const keyName = resultKeyNameFromField(selection);\n      const childSelectionSet = selection.selectionSet;\n\n      value = memo[keyName] || data[keyName];\n\n      if (value === void 0) {\n        continue;\n      }\n\n      if (childSelectionSet && value !== null) {\n        const masked = maskSelectionSet(\n          data[keyName],\n          childSelectionSet,\n          context,\n          migration,\n          __DEV__ ? `${path || \"\"}.${keyName}` : void 0\n        );\n\n        if (knownChanged.has(masked)) {\n          value = masked;\n        }\n      }\n\n      if (!__DEV__) {\n        memo[keyName] = value;\n      }\n      if (__DEV__) {\n        if (\n          migration &&\n          keyName !== \"__typename\" &&\n          // either the field is not present in the memo object\n          // or it has a `get` descriptor, not a `value` descriptor\n          // => it is a warning accessor and we can overwrite it\n          // with another accessor\n          !Object.getOwnPropertyDescriptor(memo, keyName)?.value\n        ) {\n          Object.defineProperty(\n            memo,\n            keyName,\n            getAccessorWarningDescriptor(\n              keyName,\n              value,\n              path || \"\",\n              context.operationName,\n              context.operationType\n            )\n          );\n        } else {\n          delete memo[keyName];\n          memo[keyName] = value;\n        }\n      }\n    }\n\n    if (\n      selection.kind === Kind.INLINE_FRAGMENT &&\n      (!selection.typeCondition ||\n        context.cache.fragmentMatches!(selection, data.__typename))\n    ) {\n      value = maskSelectionSet(\n        data,\n        selection.selectionSet,\n        context,\n        migration,\n        path\n      );\n    }\n\n    if (selection.kind === Kind.FRAGMENT_SPREAD) {\n      const fragmentName = selection.name.value;\n      const fragment: FragmentDefinitionNode | null =\n        context.fragmentMap[fragmentName] ||\n        (context.fragmentMap[fragmentName] =\n          context.cache.lookupFragment(fragmentName)!);\n      invariant(\n        fragment,\n        \"Could not find fragment with name '%s'.\",\n        fragmentName\n      );\n\n      const mode = getFragmentMaskMode(selection);\n\n      if (mode !== \"mask\") {\n        value = maskSelectionSet(\n          data,\n          fragment.selectionSet,\n          context,\n          mode === \"migrate\",\n          path\n        );\n      }\n    }\n\n    if (knownChanged.has(value)) {\n      knownChanged.add(memo);\n    }\n  }\n\n  if (\"__typename\" in data && !(\"__typename\" in memo)) {\n    memo.__typename = data.__typename;\n  }\n\n  // This check prevents cases where masked fields may accidentally be\n  // returned as part of this object when the fragment also selects\n  // additional fields from the same child selection.\n  if (Object.keys(memo).length !== Object.keys(data).length) {\n    knownChanged.add(memo);\n  }\n\n  return knownChanged.has(memo) ? memo : data;\n}\n\nfunction getAccessorWarningDescriptor(\n  fieldName: string,\n  value: any,\n  path: string,\n  operationName: string | undefined,\n  operationType: string\n): PropertyDescriptor {\n  let getValue = () => {\n    if (disableWarningsSlot.getValue()) {\n      return value;\n    }\n\n    invariant.warn(\n      \"Accessing unmasked field on %s at path '%s'. This field will not be available when masking is enabled. Please read the field from the fragment instead.\",\n      operationName ?\n        `${operationType} '${operationName}'`\n      : `anonymous ${operationType}`,\n      `${path}.${fieldName}`.replace(/^\\./, \"\")\n    );\n\n    getValue = () => value;\n\n    return value;\n  };\n\n  return {\n    get() {\n      return getValue();\n    },\n    set(newValue) {\n      getValue = () => newValue;\n    },\n    enumerable: true,\n    configurable: true,\n  };\n}\n"]}