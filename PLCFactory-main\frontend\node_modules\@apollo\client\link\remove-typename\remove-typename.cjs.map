{"version": 3, "file": "remove-typename.cjs", "sources": ["removeTypenameFromVariables.js"], "sourcesContent": ["import { wrap } from \"optimism\";\nimport { Kind, visit } from \"graphql\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { stripTypename, isPlainObject, cacheSizes, } from \"../../utilities/index.js\";\nimport { WeakCache } from \"@wry/caches\";\nexport var KEEP = \"__KEEP\";\nexport function removeTypenameFromVariables(options) {\n    if (options === void 0) { options = Object.create(null); }\n    return Object.assign(new ApolloLink(function (operation, forward) {\n        var except = options.except;\n        var query = operation.query, variables = operation.variables;\n        if (variables) {\n            operation.variables =\n                except ?\n                    maybeStripTypenameUsingConfig(query, variables, except)\n                    : stripTypename(variables);\n        }\n        return forward(operation);\n    }), globalThis.__DEV__ !== false ?\n        {\n            getMemoryInternals: function () {\n                var _a;\n                return {\n                    removeTypenameFromVariables: {\n                        getVariableDefinitions: (_a = getVariableDefinitions === null || getVariableDefinitions === void 0 ? void 0 : getVariableDefinitions.size) !== null && _a !== void 0 ? _a : 0,\n                    },\n                };\n            },\n        }\n        : {});\n}\nfunction maybeStripTypenameUsingConfig(query, variables, config) {\n    var variableDefinitions = getVariableDefinitions(query);\n    return Object.fromEntries(Object.entries(variables).map(function (keyVal) {\n        var key = keyVal[0], value = keyVal[1];\n        var typename = variableDefinitions[key];\n        var typenameConfig = config[typename];\n        keyVal[1] =\n            typenameConfig ?\n                maybeStripTypename(value, typenameConfig)\n                : stripTypename(value);\n        return keyVal;\n    }));\n}\nfunction maybeStripTypename(value, config) {\n    if (config === KEEP) {\n        return value;\n    }\n    if (Array.isArray(value)) {\n        return value.map(function (item) { return maybeStripTypename(item, config); });\n    }\n    if (isPlainObject(value)) {\n        var modified_1 = {};\n        Object.keys(value).forEach(function (key) {\n            var child = value[key];\n            if (key === \"__typename\") {\n                return;\n            }\n            var fieldConfig = config[key];\n            modified_1[key] =\n                fieldConfig ?\n                    maybeStripTypename(child, fieldConfig)\n                    : stripTypename(child);\n        });\n        return modified_1;\n    }\n    return value;\n}\nvar getVariableDefinitions = wrap(function (document) {\n    var definitions = {};\n    visit(document, {\n        VariableDefinition: function (node) {\n            definitions[node.variable.name.value] = unwrapType(node.type);\n        },\n    });\n    return definitions;\n}, {\n    max: cacheSizes[\"removeTypenameFromVariables.getVariableDefinitions\"] ||\n        2000 /* defaultCacheSizes[\"removeTypenameFromVariables.getVariableDefinitions\"] */,\n    cache: WeakCache,\n});\nfunction unwrapType(node) {\n    switch (node.kind) {\n        case Kind.NON_NULL_TYPE:\n            return unwrapType(node.type);\n        case Kind.LIST_TYPE:\n            return unwrapType(node.type);\n        case Kind.NAMED_TYPE:\n            return node.name.value;\n    }\n}\n//# sourceMappingURL=removeTypenameFromVariables.js.map"], "names": ["ApolloLink", "stripTypename", "isPlainObject", "wrap", "visit", "cacheSizes", "<PERSON>ak<PERSON><PERSON>", "Kind"], "mappings": ";;;;;;;;;;AAKU,IAAC,IAAI,GAAG,SAAS;AACpB,SAAS,2BAA2B,CAAC,OAAO,EAAE;AACrD,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAC9D,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,IAAIA,eAAU,CAAC,UAAU,SAAS,EAAE,OAAO,EAAE;AACtE,QAAQ,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AACpC,QAAQ,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,EAAE,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;AACrE,QAAQ,IAAI,SAAS,EAAE;AACvB,YAAY,SAAS,CAAC,SAAS;AAC/B,gBAAgB,MAAM;AACtB,oBAAoB,6BAA6B,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;AAC3E,sBAAsBC,uBAAa,CAAC,SAAS,CAAC,CAAC;AAC/C,SAAS;AACT,QAAQ,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;AAClC,KAAK,CAAC,EAAE,UAAU,CAAC,OAAO,KAAK,KAAK;AACpC,QAAQ;AACR,YAAY,kBAAkB,EAAE,YAAY;AAC5C,gBAAgB,IAAI,EAAE,CAAC;AACvB,gBAAgB,OAAO;AACvB,oBAAoB,2BAA2B,EAAE;AACjD,wBAAwB,sBAAsB,EAAE,CAAC,EAAE,GAAG,sBAAsB,KAAK,IAAI,IAAI,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC;AACrM,qBAAqB;AACrB,iBAAiB,CAAC;AAClB,aAAa;AACb,SAAS;AACT,UAAU,EAAE,CAAC,CAAC;AACd,CAAC;AACD,SAAS,6BAA6B,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE;AACjE,IAAI,IAAI,mBAAmB,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;AAC5D,IAAI,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAU,MAAM,EAAE;AAC9E,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/C,QAAQ,IAAI,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;AAChD,QAAQ,IAAI,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC9C,QAAQ,MAAM,CAAC,CAAC,CAAC;AACjB,YAAY,cAAc;AAC1B,gBAAgB,kBAAkB,CAAC,KAAK,EAAE,cAAc,CAAC;AACzD,kBAAkBA,uBAAa,CAAC,KAAK,CAAC,CAAC;AACvC,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK,CAAC,CAAC,CAAC;AACR,CAAC;AACD,SAAS,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE;AAC3C,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE;AACzB,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC9B,QAAQ,OAAO,KAAK,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AACvF,KAAK;AACL,IAAI,IAAIC,uBAAa,CAAC,KAAK,CAAC,EAAE;AAC9B,QAAQ,IAAI,UAAU,GAAG,EAAE,CAAC;AAC5B,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AAClD,YAAY,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AACnC,YAAY,IAAI,GAAG,KAAK,YAAY,EAAE;AACtC,gBAAgB,OAAO;AACvB,aAAa;AACb,YAAY,IAAI,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAC1C,YAAY,UAAU,CAAC,GAAG,CAAC;AAC3B,gBAAgB,WAAW;AAC3B,oBAAoB,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC;AAC1D,sBAAsBD,uBAAa,CAAC,KAAK,CAAC,CAAC;AAC3C,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,UAAU,CAAC;AAC1B,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD,IAAI,sBAAsB,GAAGE,aAAI,CAAC,UAAU,QAAQ,EAAE;AACtD,IAAI,IAAI,WAAW,GAAG,EAAE,CAAC;AACzB,IAAIC,aAAK,CAAC,QAAQ,EAAE;AACpB,QAAQ,kBAAkB,EAAE,UAAU,IAAI,EAAE;AAC5C,YAAY,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1E,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC,EAAE;AACH,IAAI,GAAG,EAAEC,oBAAU,CAAC,oDAAoD,CAAC;AACzE,QAAQ,IAAI;AACZ,IAAI,KAAK,EAAEC,gBAAS;AACpB,CAAC,CAAC,CAAC;AACH,SAAS,UAAU,CAAC,IAAI,EAAE;AAC1B,IAAI,QAAQ,IAAI,CAAC,IAAI;AACrB,QAAQ,KAAKC,YAAI,CAAC,aAAa;AAC/B,YAAY,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzC,QAAQ,KAAKA,YAAI,CAAC,SAAS;AAC3B,YAAY,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzC,QAAQ,KAAKA,YAAI,CAAC,UAAU;AAC5B,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AACnC,KAAK;AACL;;;;;"}