package drivers

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"hmi/internal/models"
	"hmi/pkg/logger"

	// Placeholder for a real GE Fanuc SRTP library, e.g., "github.com/some/gosrtp"
	// For this example, we'll mock its behavior.
	"gosrtp" // Assuming a mock or actual gosrtp library is available
)

// GEFanucDriver implements the PLCDriver interface for GE Fanuc PLCs.
// It supports SRTP protocol communication.
type GEFanucDriver struct {
	endpoint    string // IP address or hostname of the PLC
	port        int    // SRTP port, typically 18245
	connected   bool
	mu          sync.Mutex // Mutex to protect connected status and connection pool
	connPool    sync.Pool  // Pool of gosrtp.Client connections
	poolSize    int
	activeConns int
	cancelCtx   context.Context
	cancelFunc  context.CancelFunc
}

// NewGEFanucDriver creates a new GEFanucDriver instance.
// endpoint: IP address or hostname of the PLC.
// port: The SRTP port (default 18245).
// poolSize: The maximum number of connections to maintain in the pool.
func NewGEFanucDriver(endpoint string, port, poolSize int) *GEFanucDriver {
	ctx, cancel := context.WithCancel(context.Background())
	driver := &GEFanucDriver{
		endpoint:   endpoint,
		port:       port,
		poolSize:   poolSize,
		cancelCtx:  ctx,
		cancelFunc: cancel,
		connPool: sync.Pool{
			New: func() interface{} {
				// This function creates a new gosrtp client when needed
				return gosrtp.NewClient(endpoint, port)
			},
		},
	}
	logger.Infof("Initialized GE Fanuc driver for %s:%d, pool size %d", endpoint, port, poolSize)
	return driver
}

// Connect establishes a connection to the PLC.
// It attempts to get a connection from the pool and verifies it.
func (d *GEFanucDriver) Connect(ctx context.Context) *DriverError {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.connected {
		return nil // Already connected
	}

	logger.Infof("Attempting to connect to GE Fanuc PLC at %s:%d...", d.endpoint, d.port)
	// Try to get a connection and verify it
	client, err := d.getClient(ctx)
	if err != nil {
		return NewDriverError(ErrCodeConnectionFailed, fmt.Sprintf("failed to get initial connection: %v", err), err)
	}
	d.releaseClient(client) // Release it back to the pool after verification

	d.connected = true
	logger.Info("Successfully connected to GE Fanuc PLC.")
	return nil
}

// Disconnect closes the connection to the PLC.
func (d *GEFanucDriver) Disconnect(ctx context.Context) *DriverError {
	d.mu.Lock()
	defer d.mu.Unlock()

	if !d.connected {
		return nil // Already disconnected
	}

	logger.Info("Disconnecting from GE Fanuc PLC...")
	d.cancelFunc() // Signal all background operations to stop
	d.Close()      // Close all pooled connections

	d.connected = false
	logger.Info("Disconnected from GE Fanuc PLC.")
	return nil
}

// IsConnected returns true if the driver is currently connected to the PLC.
func (d *GEFanucDriver) IsConnected() bool {
	d.mu.Lock()
	defer d.mu.Unlock()
	return d.connected
}

// ReadTag reads the current value of a single PLC tag.
func (d *GEFanucDriver) ReadTag(ctx context.Context, tag models.PLCTag) (interface{}, *DriverError) {
	if !d.IsConnected() {
		return nil, NewDriverError(ErrCodeNotConnected, "driver not connected", nil)
	}

	client, err := d.getClient(ctx)
	if err != nil {
		return nil, NewDriverError(ErrCodeReadFailed, fmt.Sprintf("failed to get client for read: %v", err), err)
	}
	defer d.releaseClient(client)

	logger.Debugf("Reading tag: %s (%s) from %s", tag.Name, tag.Address, d.endpoint)

	memType, address, bitOffset, size, parseErr := parseGEFanucAddress(tag.Address, tag.DataType)
	if parseErr != nil {
		return nil, NewDriverError(ErrCodeInvalidAddress, parseErr.Error(), parseErr)
	}

	var value interface{}
	var readErr error

	if bitOffset != -1 { // It's a bit access
		var bitVal bool
		bitVal, readErr = client.ReadBit(memType, address, bitOffset)
		value = bitVal
	} else { // It's a word/register access
		var wordVal []byte
		wordVal, readErr = client.ReadWords(memType, address, size) // size is in bytes
		if readErr == nil {
			value, readErr = geFanucBytesToGoType(wordVal, tag.DataType)
		}
	}

	if readErr != nil {
		return nil, NewDriverError(ErrCodeReadFailed, fmt.Sprintf("failed to read tag %s: %v", tag.Address, readErr), readErr)
	}

	logger.Debugf("Read tag %s: %v", tag.Address, value)
	return value, nil
}

// WriteTag writes a value to a single PLC tag.
func (d *GEFanucDriver) WriteTag(ctx context.Context, tag models.PLCTag, value interface{}) *DriverError {
	if !d.IsConnected() {
		return NewDriverError(ErrCodeNotConnected, "driver not connected", nil)
	}

	client, err := d.getClient(ctx)
	if err != nil {
		return NewDriverError(ErrCodeWriteFailed, fmt.Sprintf("failed to get client for write: %v", err), err)
	}
	defer d.releaseClient(client)

	logger.Debugf("Writing value %v to tag: %s (%s) on %s", value, tag.Name, tag.Address, d.endpoint)

	memType, address, bitOffset, size, parseErr := parseGEFanucAddress(tag.Address, tag.DataType)
	if parseErr != nil {
		return NewDriverError(ErrCodeInvalidAddress, parseErr.Error(), parseErr)
	}

	var writeErr error

	if bitOffset != -1 { // It's a bit access
		boolVal, ok := value.(bool)
		if !ok {
			return NewDriverError(ErrCodeWriteFailed, fmt.Sprintf("expected bool value for bit access, got %T", value), nil)
		}
		writeErr = client.WriteBit(memType, address, bitOffset, boolVal)
	} else { // It's a word/register access
		bytes, convertErr := geFanucGoTypeToBytes(value, tag.DataType, size)
		if convertErr != nil {
			return NewDriverError(ErrCodeWriteFailed, fmt.Sprintf("failed to convert value for tag %s: %v", tag.Address, convertErr), convertErr)
		}
		writeErr = client.WriteWords(memType, address, bytes)
	}

	if writeErr != nil {
		return NewDriverError(ErrCodeWriteFailed, fmt.Sprintf("failed to write tag %s: %v", tag.Address, writeErr), writeErr)
	}

	logger.Debugf("Successfully wrote value %v to tag %s", value, tag.Address)
	return nil
}

// ReadTags reads multiple PLC tags in a single request when possible.
// This optimizes communication by using block reads when tags are contiguous.
func (d *GEFanucDriver) ReadTags(ctx context.Context, tags []models.PLCTag) (map[string]interface{}, *DriverError) {
	if !d.IsConnected() {
		return nil, NewDriverError(ErrCodeNotConnected, "driver not connected", nil)
	}

	client, err := d.getClient(ctx)
	if err != nil {
		return nil, NewDriverError(ErrCodeReadFailed, fmt.Sprintf("failed to get client for read: %v", err), err)
	}
	defer d.releaseClient(client)

	results := make(map[string]interface{})

	// Group tags by memory type and contiguity for batch reading
	groupedTags := groupTagsByMemoryType(tags)

	// Process each group of tags
	for _, group := range groupedTags {
		if len(group) == 1 {
			// Single tag, use standard read
			value, readErr := d.ReadTag(ctx, group[0])
			if readErr == nil {
				results[group[0].ID] = value
			} else {
				logger.Warnf("Failed to read tag %s: %v", group[0].Name, readErr)
			}
		} else {
			// Multiple contiguous tags, use block read
			err := d.readTagGroup(ctx, client, group, results)
			if err != nil {
				logger.Warnf("Failed to read tag group: %v", err)
				// Fall back to individual reads
				for _, tag := range group {
					value, readErr := d.ReadTag(ctx, tag)
					if readErr == nil {
						results[tag.ID] = value
					} else {
						logger.Warnf("Failed to read tag %s: %v", tag.Name, readErr)
					}
				}
			}
		}
	}

	return results, nil
}

// readTagGroup reads a group of contiguous tags in a single request.
func (d *GEFanucDriver) readTagGroup(ctx context.Context, client *gosrtp.Client, tags []models.PLCTag, results map[string]interface{}) error {
	if len(tags) == 0 {
		return nil
	}

	// Parse the first tag to get memory type and starting address
	memType, startAddr, _, _, parseErr := parseGEFanucAddress(tags[0].Address, tags[0].DataType)
	if parseErr != nil {
		return parseErr
	}

	// Find the last address in the group
	maxAddr := startAddr
	for _, tag := range tags {
		_, addr, _, size, parseErr := parseGEFanucAddress(tag.Address, tag.DataType)
		if parseErr != nil {
			return parseErr
		}
		if addr+size > maxAddr {
			maxAddr = addr + size
		}
	}

	// Calculate the number of words to read
	numWords := (maxAddr - startAddr + 1) / 2
	
	// Read the block of data
	data, err := client.ReadWords(memType, startAddr, numWords*2) // size in bytes
	if err != nil {
		return err
	}

	// Extract individual tag values from the block
	for _, tag := range tags {
		memType, addr, bitOffset, size, parseErr := parseGEFanucAddress(tag.Address, tag.DataType)
		if parseErr != nil {
			continue
		}

		if bitOffset != -1 {
			// Bit access - extract from the word
			wordOffset := (addr - startAddr) / 2
			if wordOffset < 0 || wordOffset >= len(data)/2 {
				continue
			}
			wordValue := uint16(data[wordOffset*2]) | (uint16(data[wordOffset*2+1]) << 8)
			bitValue := (wordValue & (1 << uint(bitOffset))) != 0
			results[tag.ID] = bitValue
		} else {
			// Word access - extract the bytes
			byteOffset := addr - startAddr
			if byteOffset < 0 || byteOffset+size > len(data) {
				continue
			}
			value, err := geFanucBytesToGoType(data[byteOffset:byteOffset+size], tag.DataType)
			if err == nil {
				results[tag.ID] = value
			}
		}
	}

	return nil
}

// WriteTags writes multiple PLC tags in a single request when possible.
// This optimizes communication by using block writes when tags are contiguous.
func (d *GEFanucDriver) WriteTags(ctx context.Context, tags []models.PLCTag, values []interface{}) *DriverError {
	if !d.IsConnected() {
		return NewDriverError(ErrCodeNotConnected, "driver not connected", nil)
	}

	if len(tags) != len(values) {
		return NewDriverError(ErrCodeWriteFailed, "tags and values arrays must have the same length", nil)
	}

	client, err := d.getClient(ctx)
	if err != nil {
		return NewDriverError(ErrCodeWriteFailed, fmt.Sprintf("failed to get client for write: %v", err), err)
	}
	defer d.releaseClient(client)

	// Group tags by memory type and contiguity for batch writing
	tagValuePairs := make([]struct {
		Tag   models.PLCTag
		Value interface{}
	}, len(tags))
	for i := range tags {
		tagValuePairs[i].Tag = tags[i]
		tagValuePairs[i].Value = values[i]
	}

	// Group by memory type and contiguity
	groups := groupTagValuePairsByMemoryType(tagValuePairs)

	// Process each group
	for _, group := range groups {
		if len(group) == 1 {
			// Single tag, use standard write
			writeErr := d.WriteTag(ctx, group[0].Tag, group[0].Value)
			if writeErr != nil {
				return writeErr
			}
		} else {
			// Multiple contiguous tags, use block write
			err := d.writeTagGroup(ctx, client, group)
			if err != nil {
				logger.Warnf("Failed to write tag group: %v", err)
				// Fall back to individual writes
				for _, pair := range group {
					writeErr := d.WriteTag(ctx, pair.Tag, pair.Value)
					if writeErr != nil {
						return writeErr
					}
				}
			}
		}
	}

	return nil
}

// writeTagGroup writes a group of contiguous tags in a single request.
func (d *GEFanucDriver) writeTagGroup(ctx context.Context, client *gosrtp.Client, pairs []struct {
	Tag   models.PLCTag
	Value interface{}
}) error {
	if len(pairs) == 0 {
		return nil
	}

	// Parse the first tag to get memory type and starting address
	memType, startAddr, _, _, parseErr := parseGEFanucAddress(pairs[0].Tag.Address, pairs[0].Tag.DataType)
	if parseErr != nil {
		return parseErr
	}

	// Find the last address in the group
	maxAddr := startAddr
	for _, pair := range pairs {
		_, addr, _, size, parseErr := parseGEFanucAddress(pair.Tag.Address, pair.Tag.DataType)
		if parseErr != nil {
			return parseErr
		}
		if addr+size > maxAddr {
			maxAddr = addr + size
		}
	}

	// Read the current block of data first (for bit operations)
	numWords := (maxAddr - startAddr + 1) / 2
	data, err := client.ReadWords(memType, startAddr, numWords*2) // size in bytes
	if err != nil {
		return err
	}

	// Update the data with new values
	for _, pair := range pairs {
		_, addr, bitOffset, size, parseErr := parseGEFanucAddress(pair.Tag.Address, pair.Tag.DataType)
		if parseErr != nil {
			continue
		}

		if bitOffset != -1 {
			// Bit access - update the bit in the word
			wordOffset := (addr - startAddr) / 2
			if wordOffset < 0 || wordOffset >= len(data)/2 {
				continue
			}
			wordValue := uint16(data[wordOffset*2]) | (uint16(data[wordOffset*2+1]) << 8)
			boolVal, ok := pair.Value.(bool)
			if !ok {
				continue
			}
			if boolVal {
				wordValue |= (1 << uint(bitOffset))
			} else {
				wordValue &= ^(1 << uint(bitOffset))
			}
			data[wordOffset*2] = byte(wordValue)
			data[wordOffset*2+1] = byte(wordValue >> 8)
		} else {
			// Word access - update the bytes
			byteOffset := addr - startAddr
			if byteOffset < 0 || byteOffset+size > len(data) {
				continue
			}
			newBytes, err := geFanucGoTypeToBytes(pair.Value, pair.Tag.DataType, size)
			if err != nil {
				continue
			}
			copy(data[byteOffset:byteOffset+size], newBytes)
		}
	}

	// Write the updated block back to the PLC
	return client.WriteWords(memType, startAddr, data)
}

// ValidateAddress checks if the given address string is valid for GE Fanuc PLCs.
// Supports variable addressing (%R, %AI, %AQ, %I, %Q, %T, %M, etc.) and PACSystems addressing.
func (d *GEFanucDriver) ValidateAddress(address string, dataType string) bool {
	if address == "" || dataType == "" {
		return false
	}

	// Regex for variable addressing: %<memory_type><address>[.<bit>]
	// Memory types: R (Register), AI (Analog Input), AQ (Analog Output), I (Input), Q (Output), T (Timer), M (Internal Relay)
	variableRegex := regexp.MustCompile(`^%(R|AI|AQ|I|Q|T|M)(\d+)(\.(\d+))?$`)

	// Regex for PACSystems symbolic addressing (simplified): <program_name>.<tag_name>
	// This is a very basic representation. Real PACSystems addressing is more complex.
	pacSystemsRegex := regexp.MustCompile(`^[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*$`)

	if variableRegex.MatchString(address) {
		matches := variableRegex.FindStringSubmatch(address)
		memType := matches[1]
		addressNum, _ := strconv.Atoi(matches[2])
		bitOffset := -1
		if matches[4] != "" {
			bitOffset, _ = strconv.Atoi(matches[4])
		}

		// Validate address number (simplified range)
		if addressNum < 0 {
			return false
		}

		// Validate bit offset for bit-addressable types
		if bitOffset != -1 && (bitOffset < 0 || bitOffset > 15) {
			return false
		}

		// Validate data type compatibility
		switch strings.ToUpper(dataType) {
		case "BOOL":
			return bitOffset != -1 || memType == "T" || memType == "M" // Timers and M-bits are often BOOL
		case "INT", "DINT", "REAL", "STRING":
			return bitOffset == -1 // Word/register types should not have bit offset
		default:
			return false
		}
	} else if pacSystemsRegex.MatchString(address) {
		// For PACSystems, we assume the symbolic address is valid if it matches the pattern
		// and the data type is generally supported.
		return true
	}

	return false
}

// GetName returns the name of the PLC driver.
func (d *GEFanucDriver) GetName() string {
	return "GE Fanuc SRTP Driver"
}

// GetSupportedDataTypes returns a list of data types supported by this driver.
func (d *GEFanucDriver) GetSupportedDataTypes() []string {
	return []string{"BOOL", "INT", "DINT", "REAL", "STRING", "WORD", "DWORD"}
}

// Close cleans up any persistent resources held by the driver.
// It closes all connections currently in the pool.
func (d *GEFanucDriver) Close() {
	d.mu.Lock()
	defer d.mu.Unlock()

	for d.activeConns > 0 {
		client := d.connPool.Get().(*gosrtp.Client)
		if client != nil {
			client.Disconnect()
			d.activeConns--
		}
	}
	logger.Info("Closed all GE Fanuc driver connections.")
}

// getClient retrieves a gosrtp client from the pool or creates a new one.
// It also handles connection establishment for the client.
func (d *GEFanucDriver) getClient(ctx context.Context) (*gosrtp.Client, error) {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.activeConns >= d.poolSize {
		return nil, fmt.Errorf("connection pool exhausted")
	}

	client := d.connPool.Get().(*gosrtp.Client)
	if client == nil {
		return nil, fmt.Errorf("failed to get client from pool")
	}

	if !client.IsConnected() {
		connectCtx, cancel := context.WithTimeout(ctx, 5*time.Second) // Short timeout for connection
		defer cancel()
		if err := client.Connect(connectCtx); err != nil {
			d.connPool.Put(client) // Return client to pool even if connection failed
			return nil, fmt.Errorf("failed to connect client: %w", err)
		}
	}
	d.activeConns++
	return client, nil
}

// releaseClient returns a gosrtp client to the pool.
func (d *GEFanucDriver) releaseClient(client *gosrtp.Client) {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.connPool.Put(client)
	d.activeConns--
}

// parseGEFanucAddress parses a GE Fanuc address string into its components.
// Returns memType, address, bitOffset, size (in bytes), and error.
func parseGEFanucAddress(address, dataType string) (string, int, int, int, error) {
	bitOffset := -1 // Default to no bit offset

	// Regex for variable addressing: %<memory_type><address>[.<bit>]
	variableRegex := regexp.MustCompile(`^%(R|AI|AQ|I|Q|T|M)(\d+)(\.(\d+))?$`)
	// Regex for PACSystems symbolic addressing (simplified): <program_name>.<tag_name>
	pacSystemsRegex := regexp.MustCompile(`^([a-zA-Z_][a-zA-Z0-9_]*)\.([a-zA-Z_][a-zA-Z0-9_]*)$`)

	if variableRegex.MatchString(address) {
		matches := variableRegex.FindStringSubmatch(address)
		memType := matches[1]
		addressNum, _ := strconv.Atoi(matches[2])

		// Handle bit offset if present
		if matches[4] != "" {
			bitOffset, _ = strconv.Atoi(matches[4])
		}

		// Determine size based on data type
		var size int
		switch strings.ToUpper(dataType) {
		case "BOOL":
			size = 1 // 1 byte for bit operations
		case "INT", "WORD":
			size = 2 // 2 bytes for int/word
		case "DINT", "DWORD", "REAL":
			size = 4 // 4 bytes for dint/dword/real
		case "STRING":
			size = 82 // Arbitrary size for strings (80 chars + length)
		default:
			return "", 0, 0, 0, fmt.Errorf("unsupported data type: %s", dataType)
		}

		return memType, addressNum, bitOffset, size, nil
	} else if pacSystemsRegex.MatchString(address) {
		// For PACSystems symbolic addressing, we'll use a placeholder memory type
		matches := pacSystemsRegex.FindStringSubmatch(address)
		programName := matches[1]
		tagName := matches[2]

		// In a real implementation, we would need to resolve the symbolic address
		// to a physical address. For now, we'll just use a placeholder.
		memType := "SYM" // Symbolic
		addressNum := 0  // Placeholder

		// Determine size based on data type
		var size int
		switch strings.ToUpper(dataType) {
		case "BOOL":
			size = 1
		case "INT", "WORD":
			size = 2
		case "DINT", "DWORD", "REAL":
			size = 4
		case "STRING":
			size = 82
		default:
			return "", 0, 0, 0, fmt.Errorf("unsupported data type: %s", dataType)
		}

		// In a real implementation, we would need to handle the symbolic address properly
		// For now, we'll just return the placeholder values
		logger.Debugf("Symbolic address: program=%s, tag=%s", programName, tagName)
		return memType, addressNum, bitOffset, size, nil
	}

	return "", 0, 0, 0, fmt.Errorf("invalid GE Fanuc address format: %s", address)
}

// geFanucBytesToGoType converts raw bytes from a GE Fanuc PLC to the appropriate Go type.
func geFanucBytesToGoType(data []byte, dataType string) (interface{}, error) {
	switch strings.ToUpper(dataType) {
	case "BOOL":
		if len(data) < 1 {
			return nil, fmt.Errorf("not enough bytes for BOOL")
		}
		return data[0] != 0, nil
	case "INT", "WORD":
		if len(data) < 2 {
			return nil, fmt.Errorf("not enough bytes for INT/WORD")
		}
		return int16(data[0]) | (int16(data[1]) << 8), nil // Little-endian
	case "DINT", "DWORD":
		if len(data) < 4 {
			return nil, fmt.Errorf("not enough bytes for DINT/DWORD")
		}
		return int32(data[0]) | (int32(data[1]) << 8) | (int32(data[2]) << 16) | (int32(data[3]) << 24), nil // Little-endian
	case "REAL":
		if len(data) < 4 {
			return nil, fmt.Errorf("not enough bytes for REAL")
		}
		bits := uint32(data[0]) | (uint32(data[1]) << 8) | (uint32(data[2]) << 16) | (uint32(data[3]) << 24)
		return float32FromBits(bits), nil
	case "STRING":
		// GE Fanuc strings are typically length-prefixed
		if len(data) < 2 {
			return nil, fmt.Errorf("not enough bytes for STRING")
		}
		length := int(data[0]) | (int(data[1]) << 8)
		if length > len(data)-2 {
			length = len(data) - 2
		}
		return string(data[2 : 2+length]), nil
	default:
		return nil, fmt.Errorf("unsupported data type for conversion: %s", dataType)
	}
}

// geFanucGoTypeToBytes converts a Go type to raw bytes for writing to a GE Fanuc PLC.
func geFanucGoTypeToBytes(value interface{}, dataType string, size int) ([]byte, error) {
	switch strings.ToUpper(dataType) {
	case "BOOL":
		val, ok := value.(bool)
		if !ok {
			return nil, fmt.Errorf("expected bool for BOOL, got %T", value)
		}
		if val {
			return []byte{1}, nil
		}
		return []byte{0}, nil
	case "INT", "WORD":
		val, ok := value.(int16)
		if !ok {
			if i, ok := value.(int); ok {
				val = int16(i)
			} else {
				return nil, fmt.Errorf("expected int16 for INT/WORD, got %T", value)
			}
		}
		return []byte{byte(val), byte(val >> 8)}, nil // Little-endian
	case "DINT", "DWORD":
		val, ok := value.(int32)
		if !ok {
			if i, ok := value.(int); ok {
				val = int32(i)
			} else {
				return nil, fmt.Errorf("expected int32 for DINT/DWORD, got %T", value)
			}
		}
		return []byte{
			byte(val),
			byte(val >> 8),
			byte(val >> 16),
			byte(val >> 24),
		}, nil // Little-endian
	case "REAL":
		val, ok := value.(float32)
		if !ok {
			if f, ok := value.(float64); ok {
				val = float32(f)
			} else {
				return nil, fmt.Errorf("expected float32 for REAL, got %T", value)
			}
		}
		bits := float32Bits(val)
		return []byte{
			byte(bits),
			byte(bits >> 8),
			byte(bits >> 16),
			byte(bits >> 24),
		}, nil // Little-endian
	case "STRING":
		val, ok := value.(string)
		if !ok {
			return nil, fmt.Errorf("expected string for STRING, got %T", value)
		}
		// Truncate string if too long for the allocated size
		if len(val) > size-2 {
			val = val[:size-2]
		}
		// Create length-prefixed string
		result := make([]byte, len(val)+2)
		result[0] = byte(len(val))
		result[1] = byte(len(val) >> 8)
		copy(result[2:], val)
		return result, nil
	default:
		return nil, fmt.Errorf("unsupported data type for conversion: %s", dataType)
	}
}

// groupTagsByMemoryType groups tags by memory type and contiguity for batch operations.
func groupTagsByMemoryType(tags []models.PLCTag) [][]models.PLCTag {
	if len(tags) <= 1 {
		return [][]models.PLCTag{tags}
	}

	// Group tags by memory type first
	memTypeGroups := make(map[string][]models.PLCTag)
	for _, tag := range tags {
		memType, _, _, _, err := parseGEFanucAddress(tag.Address, tag.DataType)
		if err != nil {
			// Skip tags with invalid addresses
			continue
		}
		memTypeGroups[memType] = append(memTypeGroups[memType], tag)
	}

	// For each memory type, group contiguous tags
	var result [][]models.PLCTag
	for _, memTypeTags := range memTypeGroups {
		if len(memTypeTags) <= 1 {
			result = append(result, memTypeTags)
			continue
		}

		// Sort tags by address
		sortedTags := make([]models.PLCTag, len(memTypeTags))
		copy(sortedTags, memTypeTags)
		// Sort logic would go here - omitted for brevity

		// Group contiguous tags
		var currentGroup []models.PLCTag
		currentGroup = append(currentGroup, sortedTags[0])

		for i := 1; i < len(sortedTags); i++ {
			// Check if contiguous - simplified logic
			currentGroup = append(currentGroup, sortedTags[i])
			
			// In a real implementation, we would check if addresses are contiguous
			// and split into multiple groups if not
		}

		result = append(result, currentGroup)
	}

	return result
}

// groupTagValuePairsByMemoryType groups tag-value pairs by memory type and contiguity.
func groupTagValuePairsByMemoryType(pairs []struct {
	Tag   models.PLCTag
	Value interface{}
}) [][]struct {
	Tag   models.PLCTag
	Value interface{}
} {
	if len(pairs) <= 1 {
		return [][]struct {
			Tag   models.PLCTag
			Value interface{}
		}{pairs}
	}

	// Group by memory type first
	memTypeGroups := make(map[string][]struct {
		Tag   models.PLCTag
		Value interface{}
	})
	for _, pair := range pairs {
		memType, _, _, _, err := parseGEFanucAddress(pair.Tag.Address, pair.Tag.DataType)
		if err != nil {
			// Skip tags with invalid addresses
			continue
		}
		memTypeGroups[memType] = append(memTypeGroups[memType], pair)
	}

	// For each memory type, group contiguous tags
	var result [][]struct {
		Tag   models.PLCTag
		Value interface{}
	}
	for _, memTypePairs := range memTypeGroups {
		if len(memTypePairs) <= 1 {
			result = append(result, memTypePairs)
			continue
		}

		// Sort pairs by address
		sortedPairs := make([]struct {
			Tag   models.PLCTag
			Value interface{}
		}, len(memTypePairs))
		copy(sortedPairs, memTypePairs)
		// Sort logic would go here - omitted for brevity

		// Group contiguous pairs
		var currentGroup []struct {
			Tag   models.PLCTag
			Value interface{}
		}
		currentGroup = append(currentGroup, sortedPairs[0])

		for i := 1; i < len(sortedPairs); i++ {
			// Check if contiguous - simplified logic
			currentGroup = append(currentGroup, sortedPairs[i])
			
			// In a real implementation, we would check if addresses are contiguous
			// and split into multiple groups if not
		}

		result = append(result, currentGroup)
	}

	return result
}

// Helper functions for float conversion
func float32FromBits(bits uint32) float32 {
	// Simple mock implementation
	return float32(bits) / 1000.0
}

func float32Bits(f float32) uint32 {
	// Simple mock implementation
	return uint32(f * 1000.0)
}

// Mock gosrtp library for compilation without actual dependency
// In a real project, you would import the actual gosrtp library.
// This mock allows the code to compile and demonstrate the driver's structure.
type gosrtpClient struct {
	connected bool
	endpoint  string
	port      int
}

type gosrtp struct{}

// Client wraps the gosrtp client
type Client struct {
	client *gosrtpClient
}

// NewClient creates a new gosrtp client
var NewClient = func(endpoint string, port int) *Client {
	return &Client{
		client: &gosrtpClient{
			endpoint: endpoint,
			port:     port,
		},
	}
}

// Connect establishes a connection to the PLC
func (c *Client) Connect(ctx context.Context) error {
	select {
	case <-ctx.Done():
		return ctx.Err()
	case <-time.After(20 * time.Millisecond):
		c.client.connected = true
		logger.Debugf("Mock gosrtp client connected to %s:%d", c.client.endpoint, c.client.port)
		return nil
	}
}

// Disconnect closes the connection to the PLC
func (c *Client) Disconnect() error {
	c.client.connected = false
	logger.Debugf("Mock gosrtp client disconnected from %s", c.client.endpoint)
	return nil
}

// IsConnected returns the connection status
func (c *Client) IsConnected() bool {
	return c.client.connected
}

// ReadBit reads a bit value from a memory area
func (c *Client) ReadBit(memType string, address int, bitOffset int) (bool, error) {
	if !c.client.connected {
		return false, fmt.Errorf("not connected")
	}
	logger.Debugf("Mock gosrtp client reading bit from %s%d.%d", memType, address, bitOffset)
	// Generate a deterministic but "random" value based on address and bit
	return (address+bitOffset)%2 == 0, nil
}

// WriteBit writes a bit value to a memory area
func (c *Client) WriteBit(memType string, address int, bitOffset int, value bool) error {
	if !c.client.connected {
		return fmt.Errorf("not connected")
	}
	logger.Debugf("Mock gosrtp client writing bit %v to %s%d.%d", value, memType, address, bitOffset)
	return nil
}

// ReadWords reads word values from a memory area
func (c *Client) ReadWords(memType string, address int, size int) ([]byte, error) {
	if !c.client.connected {
		return nil, fmt.Errorf("not connected")
	}
	logger.Debugf("Mock gosrtp client reading %d bytes from %s%d", size, memType, address)
	// Generate mock data
	data := make([]byte, size)
	for i := 0; i < size; i++ {
		data[i] = byte((address + i) % 256)
	}
	return data, nil
}

// WriteWords writes word values to a memory area
func (c *Client) WriteWords(memType string, address int, data []byte) error {
	if !c.client.connected {
		return fmt.Errorf("not connected")
	}
	logger.Debugf("Mock gosrtp client writing %d bytes to %s%d", len(data), memType, address)
	return nil
}
