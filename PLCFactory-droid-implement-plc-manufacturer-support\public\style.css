/* PLC Studio - Professional Industrial Automation Styling */
:root {
    /* Color Palette - Industrial Professional */
    --primary: #007acc;
    --primary-hover: #005a9e;
    --secondary: #f8f9fa;
    --accent: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --dark: #1e1e1e;
    --darker: #141414;
    --light: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    
    /* Spacing */
    --spacing-xs: 8px;
    --spacing-sm: 14px;
    --spacing-md: 22px;
    --spacing-lg: 30px;
    --spacing-xl: 40px;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-size-xs: 14px;
    --font-size-sm: 15px;
    --font-size-md: 17px;
    --font-size-lg: 19px;
    --font-size-xl: 22px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-md);
    color: var(--gray-800);
    background: var(--gray-100);
    overflow: hidden;
}

/* Layout */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 75px;
    background: var(--light);
    border-bottom: 1px solid var(--gray-200);
    padding: 0 var(--spacing-md);
    box-shadow: var(--shadow-sm);
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    font-size: var(--font-size-lg);
    color: var(--primary);
}

.logo i {
    font-size: 28px;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.status-indicator {
    position: relative;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--gray-400);
}

.status-indicator.connected {
    background: var(--accent);
}

.status-indicator.connected .pulse {
    position: absolute;
    top: -2px;
    left: -2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--accent);
    opacity: 0.3;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(0.95); opacity: 0.3; }
    70% { transform: scale(1); opacity: 0; }
    100% { transform: scale(0.95); opacity: 0; }
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.manufacturer-selector select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--light);
    font-family: var(--font-family);
    font-size: var(--font-size-md);
    cursor: pointer;
    min-width: 180px;
}

.manufacturer-selector select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.1);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-size: var(--font-size-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary);
    color: var(--light);
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: var(--radius-sm);
    background: transparent;
    color: var(--gray-600);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: var(--font-size-md);
}

.btn-icon:hover {
    background: var(--gray-200);
    color: var(--gray-800);
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    min-height: 0;
}

/* Sidebar */
.sidebar {
    width: 320px;
    min-width: 200px;
    max-width: 500px;
    background: var(--light);
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    position: relative;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
}

.sidebar-header h3 {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--gray-800);
}

.project-tree {
    flex: 1;
    padding: var(--spacing-md);
    overflow-y: auto;
}

.tree-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-bottom: var(--spacing-xs);
}

.tree-item:hover {
    background: var(--gray-100);
}

.tree-item.indent {
    margin-left: var(--spacing-lg);
}

.tree-item i {
    width: 18px;
    color: var(--gray-500);
    font-size: var(--font-size-md);
}

/* Editor Panel */
.editor-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--light);
}

.editor-tabs {
    display: flex;
    align-items: center;
    background: var(--gray-100);
    border-bottom: 1px solid var(--gray-200);
    padding: 0 var(--spacing-md);
    gap: var(--spacing-xs);
}

.tab {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: transparent;
    border: none;
    border-radius: var(--radius-sm) var(--radius-sm) 0 0;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: var(--font-size-md);
    color: var(--gray-600);
}

.tab.active {
    background: var(--light);
    color: var(--gray-800);
    border-bottom: 2px solid var(--primary);
}

.tab-close {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    font-size: 14px;
}

.tab-close:hover {
    background: var(--gray-300);
    color: var(--gray-800);
}

.new-tab {
    margin-left: auto;
}

.editor-container {
    flex: 1;
    position: relative;
}

#monacoEditor {
    width: 100%;
    height: 100%;
}

.editor-footer {
    height: 38px;
    background: var(--gray-100);
    border-top: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-md);
}

.editor-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.separator {
    color: var(--gray-400);
}

.validation-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.text-success {
    color: var(--accent);
}

/* Data Panel */
.data-panel {
    width: 380px;
    min-width: 250px;
    max-width: 600px;
    background: var(--light);
    border-left: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    position: relative;
}

.panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
}

.panel-header h3 {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--gray-800);
}

.panel-controls {
    display: flex;
    gap: var(--spacing-xs);
}

.data-monitor {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
}

.monitor-section {
    margin-bottom: var(--spacing-lg);
}

.monitor-section h4 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
}

.status-card {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    text-align: center;
    transition: all 0.2s ease;
}

.status-card:hover {
    border-color: var(--primary);
    box-shadow: var(--shadow-sm);
}

.status-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
}

.status-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tag-monitor {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.tag-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-sm);
    align-items: center;
    transition: all 0.2s ease;
}

.tag-item:hover {
    border-color: var(--primary);
    background: var(--light);
}

.tag-name {
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: var(--font-size-md);
    color: var(--gray-800);
    overflow: hidden;
    text-overflow: ellipsis;
}

.tag-value {
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: var(--font-size-md);
    font-weight: 600;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    background: var(--gray-200);
    color: var(--gray-800);
}

.tag-value.bool-true {
    background: var(--accent);
    color: var(--light);
}

.tag-value.bool-false {
    background: var(--gray-400);
    color: var(--light);
}

.tag-value.analog {
    background: var(--primary);
    color: var(--light);
}

.tag-type {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    text-transform: uppercase;
    font-weight: 500;
}

/* Bottom Panel */
.bottom-panel {
    height: 260px;
    min-height: 150px;
    max-height: 500px;
    background: var(--light);
    border-top: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    position: relative;
}

.panel-tabs {
    display: flex;
    background: var(--gray-100);
    border-bottom: 1px solid var(--gray-200);
}

.panel-tab {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: var(--font-size-md);
    color: var(--gray-600);
    transition: all 0.2s ease;
}

.panel-tab.active {
    background: var(--light);
    color: var(--gray-800);
    border-bottom: 2px solid var(--primary);
}

.panel-tab:hover:not(.active) {
    background: var(--gray-200);
}

.panel-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
}

.diagnostics-panel {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.diagnostic-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
}

.diagnostic-item.info {
    background: rgba(0, 122, 204, 0.1);
    color: var(--primary);
}

.diagnostic-item.success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--accent);
}

.diagnostic-item.warning {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
}

.diagnostic-item.error {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger);
}

.diagnostic-item i {
    width: 18px;
    font-size: var(--font-size-md);
}

.timestamp {
    margin-left: auto;
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    font-family: 'Monaco', 'Consolas', monospace;
}

/* Gray variations */
.gray-50 {
    background: #f9fafb;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        width: 240px;
    }
    
    .data-panel {
        width: 280px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        display: none;
    }
    
    .data-panel {
        display: none;
    }
    
    .header {
        padding: 0 var(--spacing-sm);
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* Animation for smooth transitions */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Focus states for accessibility */
button:focus,
select:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Loading animation */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spinning {
    animation: spin 1s linear infinite;
}

/* Resize Handles */
.resize-handle {
    position: absolute;
    background: transparent;
    transition: background-color 0.2s ease;
    z-index: 10;
}

.resize-handle:hover {
    background: var(--primary);
    opacity: 0.3;
}

.resize-handle.resizing {
    background: var(--primary);
    opacity: 0.5;
}

/* Horizontal resize handle (for sidebar and data panel) */
.resize-handle-horizontal {
    width: 4px;
    height: 100%;
    top: 0;
    cursor: col-resize;
}

/* Vertical resize handle (for bottom panel) */
.resize-handle-vertical {
    width: 100%;
    height: 4px;
    left: 0;
    cursor: row-resize;
}

/* Specific handle positions */
.sidebar .resize-handle-horizontal {
    right: -2px;
}

.data-panel .resize-handle-horizontal {
    left: -2px;
}

.bottom-panel .resize-handle-vertical {
    top: -2px;
}

/* Prevent text selection during resize */
.resizing-active {
    user-select: none;
    cursor: col-resize;
}

.resizing-active-vertical {
    user-select: none;
    cursor: row-resize;
}

.resizing-active * {
    pointer-events: none;
}

.resizing-active-vertical * {
    pointer-events: none;
} 