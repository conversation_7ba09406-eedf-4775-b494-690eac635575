# Task ID: 5
# Title: Build Real-Time Collaboration Hub
# Status: pending
# Dependencies: 3
# Priority: medium
# Description: Create the real-time collaboration system using Y-CRDT for conflict-free editing, WebSocket channels for presence, and versioning with branch/merge capabilities.
# Details:
1. Implement Elixir + Phoenix channels backend for WebSocket communication
2. Integrate Y-CRDT for operational transform and conflict-free editing
3. Develop presence system with heartbeat <3s requirement
4. Create commenting system with Markdown support and image paste
5. Implement versioning as append-only DAG with diff renderer
6. Add branch/merge functionality for parallel development
7. Optimize for 100 concurrent editors per project

Elixir backend implementation:
```elixir
defmodule Continuum.CollabHub do
  use Phoenix.Channel
  alias Continuum.CRDT.Document
  alias Continuum.Presence
  
  def join("project:" <> project_id, _params, socket) do
    if authorized?(socket, project_id) do
      send(self(), :after_join)
      document = Document.get_or_create(project_id)
      {:ok, %{document: document}, assign(socket, :project_id, project_id)}
    else
      {:error, %{reason: "unauthorized"}}
    end
  end
  
  def handle_info(:after_join, socket) do
    push(socket, "presence_state", Presence.list(socket))
    {:ok, _} = Presence.track(socket, socket.assigns.user_id, %{
      online_at: inspect(System.system_time(:second)),
      cursor: %{x: 0, y: 0}
    })
    {:noreply, socket}
  end
  
  def handle_in("update", %{"ops" => ops}, socket) do
    project_id = socket.assigns.project_id
    Document.apply_operations(project_id, ops)
    broadcast_from!(socket, "update", %{ops: ops, user_id: socket.assigns.user_id})
    {:reply, :ok, socket}
  end
  
  # Additional handlers for comments, versioning, etc.
end
```

# Test Strategy:
1. Unit tests for Y-CRDT operations and conflict resolution
2. Integration tests for WebSocket communication and presence
3. Load testing with 100 concurrent editors per project
4. Latency testing for operation propagation
5. Failover and recovery testing for collaboration sessions
6. Branch/merge scenario testing with concurrent edits
7. Performance testing of diff rendering for large changes

# Subtasks:
## 1. Implement WebSocket Server [pending]
### Dependencies: None
### Description: Set up a WebSocket server to handle real-time communication between clients
### Details:
Use a WebSocket library compatible with the backend framework. Implement connection handling, message routing, and error handling.

## 2. Integrate Y-CRDT [pending]
### Dependencies: 5.1
### Description: Implement Y-CRDT for conflict-free real-time collaboration
### Details:
Set up Y-CRDT on both client and server. Implement document synchronization and conflict resolution mechanisms.

## 3. Develop Presence System [pending]
### Dependencies: 5.1, 5.2
### Description: Create a system to track and display user presence in real-time
### Details:
Implement user status tracking, real-time updates, and efficient data structures for managing presence information.

## 4. Implement Commenting Features [pending]
### Dependencies: 5.2, 5.3
### Description: Add functionality for users to create, edit, and delete comments in real-time
### Details:
Develop comment data structures, real-time synchronization, and user interface components for commenting.

## 5. Create Versioning System [pending]
### Dependencies: 5.2, 5.4
### Description: Implement a versioning system to track changes and allow reverting to previous states
### Details:
Design and implement efficient data structures for version history, develop mechanisms for creating and restoring snapshots.

## 6. Optimize Performance [pending]
### Dependencies: 5.1, 5.2, 5.3, 5.4, 5.5
### Description: Implement performance optimizations for handling many concurrent users
### Details:
Profile the application, identify bottlenecks, implement caching strategies, optimize database queries, and fine-tune WebSocket communication.

