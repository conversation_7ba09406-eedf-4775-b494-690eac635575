<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PLC Studio - Professional Industrial Automation Platform</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <i class="fas fa-industry"></i>
                    <span>PLC Studio</span>
                </div>
                <div class="connection-status">
                    <div class="status-indicator connected" id="connectionStatus">
                        <div class="pulse"></div>
                    </div>
                    <span id="connectionText">Connected</span>
                </div>
            </div>
            <div class="header-center">
                <div class="manufacturer-selector">
                    <select id="manufacturerSelect">
                        <option value="allen_bradley">Allen-Bradley</option>
                        <option value="siemens">Siemens</option>
                        <option value="mitsubishi">Mitsubishi</option>
                        <option value="schneider">Schneider Electric</option>
                        <option value="omron">Omron</option>
                    </select>
                </div>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" id="connectBtn">
                    <i class="fas fa-plug"></i>
                    Connect PLC
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3>Project Explorer</h3>
                    <button class="btn-icon" title="New Project">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <div class="project-tree">
                    <div class="tree-item">
                        <i class="fas fa-folder-open"></i>
                        <span>Main Program</span>
                    </div>
                    <div class="tree-item indent">
                        <i class="fas fa-file-code"></i>
                        <span>Main.st</span>
                    </div>
                    <div class="tree-item indent">
                        <i class="fas fa-file-code"></i>
                        <span>Safety.st</span>
                    </div>
                    <div class="tree-item">
                        <i class="fas fa-folder"></i>
                        <span>Function Blocks</span>
                    </div>
                    <div class="tree-item">
                        <i class="fas fa-database"></i>
                        <span>Data Blocks</span>
                    </div>
                </div>
                <div class="resize-handle resize-handle-horizontal" id="sidebarResize"></div>
            </aside>

            <!-- Editor Panel -->
            <div class="editor-panel">
                <div class="editor-tabs">
                    <div class="tab active" data-tab="main">
                        <span>Main.st</span>
                        <button class="tab-close">×</button>
                    </div>
                    <div class="tab" data-tab="safety">
                        <span>Safety.st</span>
                        <button class="tab-close">×</button>
                    </div>
                    <button class="btn-icon new-tab" title="New File">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <div class="editor-container">
                    <div id="monacoEditor"></div>
                </div>
                <div class="editor-footer">
                    <div class="editor-status">
                        <span>Ln 1, Col 1</span>
                        <span class="separator">|</span>
                        <span>Structured Text</span>
                        <span class="separator">|</span>
                        <span class="validation-status">
                            <i class="fas fa-check-circle text-success"></i>
                            No Errors
                        </span>
                    </div>
                </div>
            </div>

            <!-- Live Data Panel -->
            <div class="data-panel">
                <div class="resize-handle resize-handle-horizontal" id="dataPanelResize"></div>
                <div class="panel-header">
                    <h3>Live Data Monitor</h3>
                    <div class="panel-controls">
                        <button class="btn-icon" title="Refresh" id="refreshData">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="btn-icon" title="Settings">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
                <div class="data-monitor">
                    <div class="monitor-section">
                        <h4>System Status</h4>
                        <div class="status-grid">
                            <div class="status-card">
                                <div class="status-value" id="scanTime">2.5ms</div>
                                <div class="status-label">Scan Time</div>
                            </div>
                            <div class="status-card">
                                <div class="status-value" id="cpuLoad">15%</div>
                                <div class="status-label">CPU Load</div>
                            </div>
                            <div class="status-card">
                                <div class="status-value" id="memoryUsage">45%</div>
                                <div class="status-label">Memory</div>
                            </div>
                        </div>
                    </div>
                    <div class="monitor-section">
                        <h4>Tag Monitor</h4>
                        <div class="tag-monitor">
                            <div class="tag-item">
                                <div class="tag-name">Motor_1_Run</div>
                                <div class="tag-value bool-true">TRUE</div>
                                <div class="tag-type">BOOL</div>
                            </div>
                            <div class="tag-item">
                                <div class="tag-name">Conveyor_Speed</div>
                                <div class="tag-value analog">1250</div>
                                <div class="tag-type">INT</div>
                            </div>
                            <div class="tag-item">
                                <div class="tag-name">Temperature_1</div>
                                <div class="tag-value analog">65.4</div>
                                <div class="tag-type">REAL</div>
                            </div>
                            <div class="tag-item">
                                <div class="tag-name">Safety_OK</div>
                                <div class="tag-value bool-true">TRUE</div>
                                <div class="tag-type">BOOL</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Panel -->
        <div class="bottom-panel">
            <div class="resize-handle resize-handle-vertical" id="bottomPanelResize"></div>
            <div class="panel-tabs">
                <div class="panel-tab active" data-panel="diagnostics">
                    <i class="fas fa-bug"></i>
                    Diagnostics
                </div>
                <div class="panel-tab" data-panel="output">
                    <i class="fas fa-terminal"></i>
                    Output
                </div>
                <div class="panel-tab" data-panel="watch">
                    <i class="fas fa-eye"></i>
                    Watch
                </div>
            </div>
            <div class="panel-content">
                <div class="diagnostics-panel">
                    <div class="diagnostic-item info">
                        <i class="fas fa-info-circle"></i>
                        <span>PLC connection established successfully</span>
                        <span class="timestamp">14:23:45</span>
                    </div>
                    <div class="diagnostic-item success">
                        <i class="fas fa-check-circle"></i>
                        <span>Program compiled without errors</span>
                        <span class="timestamp">14:23:40</span>
                    </div>
                    <div class="diagnostic-item warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>Tag validation warning: Unused variable 'temp_var'</span>
                        <span class="timestamp">14:23:35</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monaco Editor -->
    <script src="https://unpkg.com/monaco-editor@0.44.0/min/vs/loader.js"></script>
    <script src="script.js"></script>
</body>
</html> 