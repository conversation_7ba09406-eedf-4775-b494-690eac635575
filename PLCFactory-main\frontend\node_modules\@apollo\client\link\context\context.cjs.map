{"version": 3, "file": "context.cjs", "sources": ["index.js"], "sourcesContent": ["import { __rest } from \"tslib\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nexport function setContext(setter) {\n    return new ApolloLink(function (operation, forward) {\n        var request = __rest(operation, []);\n        return new Observable(function (observer) {\n            var handle;\n            var closed = false;\n            Promise.resolve(request)\n                .then(function (req) { return setter(req, operation.getContext()); })\n                .then(operation.setContext)\n                .then(function () {\n                // if the observer is already closed, no need to subscribe.\n                if (closed)\n                    return;\n                handle = forward(operation).subscribe({\n                    next: observer.next.bind(observer),\n                    error: observer.error.bind(observer),\n                    complete: observer.complete.bind(observer),\n                });\n            })\n                .catch(observer.error.bind(observer));\n            return function () {\n                closed = true;\n                if (handle)\n                    handle.unsubscribe();\n            };\n        });\n    });\n}\n//# sourceMappingURL=index.js.map"], "names": ["ApolloLink", "__rest", "Observable"], "mappings": ";;;;;;;;AAGO,SAAS,UAAU,CAAC,MAAM,EAAE;AACnC,IAAI,OAAO,IAAIA,eAAU,CAAC,UAAU,SAAS,EAAE,OAAO,EAAE;AACxD,QAAQ,IAAI,OAAO,GAAGC,YAAM,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;AAC5C,QAAQ,OAAO,IAAIC,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAClD,YAAY,IAAI,MAAM,CAAC;AACvB,YAAY,IAAI,MAAM,GAAG,KAAK,CAAC;AAC/B,YAAY,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;AACpC,iBAAiB,IAAI,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,MAAM,CAAC,GAAG,EAAE,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC;AACrF,iBAAiB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AAC3C,iBAAiB,IAAI,CAAC,YAAY;AAElC,gBAAgB,IAAI,MAAM;AAC1B,oBAAoB,OAAO;AAC3B,gBAAgB,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC;AACtD,oBAAoB,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AACtD,oBAAoB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;AACxD,oBAAoB,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC9D,iBAAiB,CAAC,CAAC;AACnB,aAAa,CAAC;AACd,iBAAiB,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtD,YAAY,OAAO,YAAY;AAC/B,gBAAgB,MAAM,GAAG,IAAI,CAAC;AAC9B,gBAAgB,IAAI,MAAM;AAC1B,oBAAoB,MAAM,CAAC,WAAW,EAAE,CAAC;AACzC,aAAa,CAAC;AACd,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP;;;;"}