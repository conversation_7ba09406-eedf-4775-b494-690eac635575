{"version": 3, "file": "createSignalIfSupported.js", "sourceRoot": "", "sources": ["../../../src/link/http/createSignalIfSupported.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG;IACrC,IAAI,OAAO,eAAe,KAAK,WAAW;QACxC,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;IAE9C,IAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;IACzC,IAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IACjC,OAAO,EAAE,UAAU,YAAA,EAAE,MAAM,QAAA,EAAE,CAAC;AAChC,CAAC,CAAC", "sourcesContent": ["/**\n * @deprecated\n * This is not used internally any more and will be removed in\n * the next major version of Apollo Client.\n */\nexport const createSignalIfSupported = () => {\n  if (typeof AbortController === \"undefined\")\n    return { controller: false, signal: false };\n\n  const controller = new AbortController();\n  const signal = controller.signal;\n  return { controller, signal };\n};\n"]}