{"version": 3, "file": "selectHttpOptionsAndBody.js", "sourceRoot": "", "sources": ["../../../src/link/http/selectHttpOptionsAndBody.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAkGjD,IAAM,kBAAkB,GAAqB;IAC3C,YAAY,EAAE,IAAI;IAClB,iBAAiB,EAAE,KAAK;IACxB,kBAAkB,EAAE,KAAK;CAC1B,CAAC;AAEF,IAAM,cAAc,GAAG;IACrB,qEAAqE;IACrE,MAAM,EAAE,KAAK;IACb,6EAA6E;IAC7E,4EAA4E;IAC5E,8EAA8E;IAC9E,6EAA6E;IAC7E,2EAA2E;IAC3E,wEAAwE;IACxE,4EAA4E;IAC5E,6EAA6E;IAC7E,2EAA2E;IAC3E,iEAAiE;IACjE,6GAA6G;IAC7G,oBAAoB;IACpB,cAAc,EAAE,kBAAkB;CACnC,CAAC;AAEF,IAAM,cAAc,GAAG;IACrB,MAAM,EAAE,MAAM;CACf,CAAC;AAEF,MAAM,CAAC,IAAM,kBAAkB,GAAG;IAChC,IAAI,EAAE,kBAAkB;IACxB,OAAO,EAAE,cAAc;IACvB,OAAO,EAAE,cAAc;CACxB,CAAC;AAEF,MAAM,CAAC,IAAM,cAAc,GAAY,UAAC,GAAG,EAAE,OAAO,IAAK,OAAA,OAAO,CAAC,GAAG,CAAC,EAAZ,CAAY,CAAC;AAEtE,MAAM,UAAU,wBAAwB,CACtC,SAAoB,EACpB,cAA0B;IAC1B,iBAA6B;SAA7B,UAA6B,EAA7B,qBAA6B,EAA7B,IAA6B;QAA7B,gCAA6B;;IAE7B,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;IAChC,OAAO,gCAAgC,8BACrC,SAAS;QACT,cAAc,GACX,OAAO,UACV;AACJ,CAAC;AAED,MAAM,UAAU,gCAAgC,CAC9C,SAAoB,EACpB,OAAgB;IAChB,iBAAwB;SAAxB,UAAwB,EAAxB,qBAAwB,EAAxB,IAAwB;QAAxB,gCAAwB;;IAExB,IAAI,OAAO,GAAG,EAAsC,CAAC;IACrD,IAAI,IAAI,GAAG,EAAsB,CAAC;IAElC,OAAO,CAAC,OAAO,CAAC,UAAC,MAAM;QACrB,OAAO,kCACF,OAAO,GACP,MAAM,CAAC,OAAO,KACjB,OAAO,wBACF,OAAO,CAAC,OAAO,GACf,MAAM,CAAC,OAAO,IAEpB,CAAC;QAEF,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACvB,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QAC3C,CAAC;QAED,IAAI,yBACC,IAAI,GACJ,MAAM,CAAC,IAAI,CACf,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,OAAO,CAAC,OAAO,GAAG,sBAAsB,CACtC,OAAO,CAAC,OAAO,EACf,IAAI,CAAC,kBAAkB,CACxB,CAAC;IACJ,CAAC;IAED,sCAAsC;IAC9B,IAAA,aAAa,GAAmC,SAAS,cAA5C,EAAE,UAAU,GAAuB,SAAS,WAAhC,EAAE,SAAS,GAAY,SAAS,UAArB,EAAE,KAAK,GAAK,SAAS,MAAd,CAAe;IAClE,IAAM,IAAI,GAAS,EAAE,aAAa,eAAA,EAAE,SAAS,WAAA,EAAE,CAAC;IAEhD,IAAI,IAAI,CAAC,iBAAiB;QAAG,IAAY,CAAC,UAAU,GAAG,UAAU,CAAC;IAElE,gDAAgD;IAChD,IAAI,IAAI,CAAC,YAAY;QAAG,IAAY,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAEnE,OAAO;QACL,OAAO,SAAA;QACP,IAAI,MAAA;KACL,CAAC;AACJ,CAAC;AAED,iFAAiF;AACjF,0EAA0E;AAC1E,wCAAwC;AACxC,SAAS,sBAAsB,CAC7B,OAA+B,EAC/B,kBAAuC;IAEvC,6EAA6E;IAC7E,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxB,IAAM,mBAAiB,GAA2B,EAAE,CAAC;QACrD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI;YACxC,mBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QACH,OAAO,mBAAiB,CAAC;IAC3B,CAAC;IAED,qEAAqE;IACrE,gCAAgC;IAChC,4EAA4E;IAC5E,wCAAwC;IACxC,IAAM,UAAU,GACd,EAAE,CAAC;IACL,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI;QACxC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG;YAC/B,YAAY,EAAE,IAAI;YAClB,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC;SACrB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAM,iBAAiB,GAA2B,EAAE,CAAC;IACrD,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI;QACnC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;IAC5E,CAAC,CAAC,CAAC;IACH,OAAO,iBAAiB,CAAC;AAC3B,CAAC", "sourcesContent": ["import type { ASTNode } from \"graphql\";\nimport { print } from \"../../utilities/index.js\";\n\nimport type { Operation } from \"../core/index.js\";\n\nexport interface Printer {\n  (node: ASTNode, originalPrint: typeof print): string;\n}\n\nexport interface UriFunction {\n  (operation: Operation): string;\n}\n\nexport interface Body {\n  query?: string;\n  operationName?: string;\n  variables?: Record<string, any>;\n  extensions?: Record<string, any>;\n}\n\nexport interface HttpOptions {\n  /**\n   * The URI to use when fetching operations.\n   *\n   * Defaults to '/graphql'.\n   */\n  uri?: string | UriFunction;\n\n  /**\n   * Passes the extensions field to your graphql server.\n   *\n   * Defaults to false.\n   */\n  includeExtensions?: boolean;\n\n  /**\n   * A `fetch`-compatible API to use when making requests.\n   */\n  fetch?: typeof fetch;\n\n  /**\n   * An object representing values to be sent as headers on the request.\n   */\n  headers?: Record<string, string>;\n\n  /**\n   * If set to true, header names won't be automatically normalized to\n   * lowercase. This allows for non-http-spec-compliant servers that might\n   * expect capitalized header names.\n   */\n  preserveHeaderCase?: boolean;\n\n  /**\n   * The credentials policy you want to use for the fetch call.\n   */\n  credentials?: string;\n\n  /**\n   * Any overrides of the fetch options argument to pass to the fetch call.\n   */\n  fetchOptions?: any;\n\n  /**\n   * If set to true, use the HTTP GET method for query operations. Mutations\n   * will still use the method specified in fetchOptions.method (which defaults\n   * to POST).\n   */\n  useGETForQueries?: boolean;\n\n  /**\n   * If set to true, the default behavior of stripping unused variables\n   * from the request will be disabled.\n   *\n   * Unused variables are likely to trigger server-side validation errors,\n   * per https://spec.graphql.org/draft/#sec-All-Variables-Used, but this\n   * includeUnusedVariables option can be useful if your server deviates\n   * from the GraphQL specification by not strictly enforcing that rule.\n   */\n  includeUnusedVariables?: boolean;\n  /**\n   * A function to substitute for the default query print function. Can be\n   * used to apply changes to the results of the print function.\n   */\n  print?: Printer;\n}\n\nexport interface HttpQueryOptions {\n  includeQuery?: boolean;\n  includeExtensions?: boolean;\n  preserveHeaderCase?: boolean;\n}\n\nexport interface HttpConfig {\n  http?: HttpQueryOptions;\n  options?: any;\n  headers?: Record<string, string>;\n  credentials?: any;\n}\n\nconst defaultHttpOptions: HttpQueryOptions = {\n  includeQuery: true,\n  includeExtensions: false,\n  preserveHeaderCase: false,\n};\n\nconst defaultHeaders = {\n  // headers are case insensitive (https://stackoverflow.com/a/5259004)\n  accept: \"*/*\",\n  // The content-type header describes the type of the body of the request, and\n  // so it typically only is sent with requests that actually have bodies. One\n  // could imagine that Apollo Client would remove this header when constructing\n  // a GET request (which has no body), but we historically have not done that.\n  // This means that browsers will preflight all Apollo Client requests (even\n  // GET requests). Apollo Server's CSRF prevention feature (introduced in\n  // AS3.7) takes advantage of this fact and does not block requests with this\n  // header. If you want to drop this header from GET requests, then you should\n  // probably replace it with a `apollo-require-preflight` header, or servers\n  // with CSRF prevention enabled might block your GET request. See\n  // https://www.apollographql.com/docs/apollo-server/security/cors/#preventing-cross-site-request-forgery-csrf\n  // for more details.\n  \"content-type\": \"application/json\",\n};\n\nconst defaultOptions = {\n  method: \"POST\",\n};\n\nexport const fallbackHttpConfig = {\n  http: defaultHttpOptions,\n  headers: defaultHeaders,\n  options: defaultOptions,\n};\n\nexport const defaultPrinter: Printer = (ast, printer) => printer(ast);\n\nexport function selectHttpOptionsAndBody(\n  operation: Operation,\n  fallbackConfig: HttpConfig,\n  ...configs: Array<HttpConfig>\n) {\n  configs.unshift(fallbackConfig);\n  return selectHttpOptionsAndBodyInternal(\n    operation,\n    defaultPrinter,\n    ...configs\n  );\n}\n\nexport function selectHttpOptionsAndBodyInternal(\n  operation: Operation,\n  printer: Printer,\n  ...configs: HttpConfig[]\n) {\n  let options = {} as HttpConfig & Record<string, any>;\n  let http = {} as HttpQueryOptions;\n\n  configs.forEach((config) => {\n    options = {\n      ...options,\n      ...config.options,\n      headers: {\n        ...options.headers,\n        ...config.headers,\n      },\n    };\n\n    if (config.credentials) {\n      options.credentials = config.credentials;\n    }\n\n    http = {\n      ...http,\n      ...config.http,\n    };\n  });\n\n  if (options.headers) {\n    options.headers = removeDuplicateHeaders(\n      options.headers,\n      http.preserveHeaderCase\n    );\n  }\n\n  //The body depends on the http options\n  const { operationName, extensions, variables, query } = operation;\n  const body: Body = { operationName, variables };\n\n  if (http.includeExtensions) (body as any).extensions = extensions;\n\n  // not sending the query (i.e persisted queries)\n  if (http.includeQuery) (body as any).query = printer(query, print);\n\n  return {\n    options,\n    body,\n  };\n}\n\n// Remove potential duplicate header names, preserving last (by insertion order).\n// This is done to prevent unintentionally duplicating a header instead of\n// overwriting it (See #8447 and #8449).\nfunction removeDuplicateHeaders(\n  headers: Record<string, string>,\n  preserveHeaderCase: boolean | undefined\n): typeof headers {\n  // If we're not preserving the case, just remove duplicates w/ normalization.\n  if (!preserveHeaderCase) {\n    const normalizedHeaders: Record<string, string> = {};\n    Object.keys(Object(headers)).forEach((name) => {\n      normalizedHeaders[name.toLowerCase()] = headers[name];\n    });\n    return normalizedHeaders;\n  }\n\n  // If we are preserving the case, remove duplicates w/ normalization,\n  // preserving the original name.\n  // This allows for non-http-spec-compliant servers that expect intentionally\n  // capitalized header names (See #6741).\n  const headerData: Record<string, { originalName: string; value: string }> =\n    {};\n  Object.keys(Object(headers)).forEach((name) => {\n    headerData[name.toLowerCase()] = {\n      originalName: name,\n      value: headers[name],\n    };\n  });\n\n  const normalizedHeaders: Record<string, string> = {};\n  Object.keys(headerData).forEach((name) => {\n    normalizedHeaders[headerData[name].originalName] = headerData[name].value;\n  });\n  return normalizedHeaders;\n}\n"]}