package logger

import (
	"fmt"
	"log"
	"os"
	"sync"
)

// LogLevel defines the severity of a log message.
type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARN
	ERROR
	FATAL
)

var (
	currentLevel LogLevel = INFO // Default log level
	logger       *log.Logger
	once         sync.Once
)

// InitLogger initializes the logger with a specified log level.
// This function should be called once at the application startup.
func InitLogger(level string) {
	once.Do(func() {
		logger = log.New(os.Stdout, "", log.Ldate|log.Ltime|log.Lshortfile)
		SetLogLevel(level)
		Infof("Logger initialized with level: %s", level)
	})
}

// SetLogLevel sets the global logging level.
func SetLogLevel(level string) {
	switch lower := toLower(level); lower {
	case "debug":
		currentLevel = DEBUG
	case "info":
		currentLevel = INFO
	case "warn", "warning":
		currentLevel = WARN
	case "error":
		currentLevel = ERROR
	case "fatal":
		currentLevel = FATAL
	default:
		Warnf("Unknown log level '%s', defaulting to INFO", level)
		currentLevel = INFO
	}
}

// Debugf logs a debug message if the current log level is DEBUG or lower.
func Debugf(format string, v ...interface{}) {
	if currentLevel <= DEBUG {
		logf("DEBUG", format, v...)
	}
}

// Infof logs an info message if the current log level is INFO or lower.
func Infof(format string, v ...interface{}) {
	if currentLevel <= INFO {
		logf("INFO", format, v...)
	}
}

// Warnf logs a warning message if the current log level is WARN or lower.
func Warnf(format string, v ...interface{}) {
	if currentLevel <= WARN {
		logf("WARN", format, v...)
	}
}

// Errorf logs an error message if the current log level is ERROR or lower.
func Errorf(format string, v ...interface{}) {
	if currentLevel <= ERROR {
		logf("ERROR", format, v...)
	}
}

// Fatalf logs a fatal message and then exits the application.
func Fatalf(format string, v ...interface{}) {
	logf("FATAL", format, v...)
	os.Exit(1)
}

// logf is a helper function to format and print log messages.
func logf(levelStr string, format string, v ...interface{}) {
	if logger == nil {
		// Fallback if InitLogger was not called
		log.Printf("[%s] %s\n", levelStr, fmt.Sprintf(format, v...))
		return
	}
	logger.Printf("[%s] %s\n", levelStr, fmt.Sprintf(format, v...))
}

// toLower is a simple helper to convert string to lowercase.
func toLower(s string) string {
	return fmt.Sprintf("%s", s) // Using fmt.Sprintf to avoid importing strings package for simple case
}
