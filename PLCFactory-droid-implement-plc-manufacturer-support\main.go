package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"plc-manufacturer-support/services/hmi/internal/models"
	"plc-manufacturer-support/services/hmi/pkg/logger"
	"syscall"
	"time"

	"github.com/gorilla/mux"
)

// TagValidationRequest represents a request to validate a PLC tag address
type TagValidationRequest struct {
	Manufacturer string `json:"manufacturer"`
	Address      string `json:"address"`
	DataType     string `json:"datatype"`
}

// TagValidationResponse represents the response from tag validation
type TagValidationResponse struct {
	Valid      bool                   `json:"valid"`
	Error      string                 `json:"error,omitempty"`
	Components map[string]interface{} `json:"components,omitempty"`
}

// ManufacturerInfo represents information about a supported manufacturer
type ManufacturerInfo struct {
	Name              string   `json:"name"`
	SupportedDataTypes []string `json:"supportedDataTypes"`
}

func main() {
	// Initialize logger
	logger.InitLogger("info")
	
	// Create router
	r := mux.NewRouter()
	
	// API routes
	api := r.PathPrefix("/api/v1").Subrouter()
	
	// Tag validation endpoints
	api.HandleFunc("/validate", validateTagHandler).Methods("POST")
	api.HandleFunc("/parse", parseTagHandler).Methods("POST")
	api.HandleFunc("/manufacturers", getManufacturersHandler).Methods("GET")
	api.HandleFunc("/manufacturers/{manufacturer}/datatypes", getDataTypesHandler).Methods("GET")
	
	// Health check endpoint
	api.HandleFunc("/health", healthCheckHandler).Methods("GET")
	
	// CORS middleware
	r.Use(corsMiddleware)
	
	// Create HTTP server
	srv := &http.Server{
		Handler:      r,
		Addr:         ":3000",
		WriteTimeout: 15 * time.Second,
		ReadTimeout:  15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}
	
	// Start server in a goroutine
	go func() {
		logger.Infof("🚀 PLC Manufacturer Support Server starting on port 3000")
		logger.Infof("📡 API endpoints available at http://localhost:3000/api/v1")
		logger.Infof("🏥 Health check: http://localhost:3000/api/v1/health")
		
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()
	
	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	logger.Info("🛑 Shutting down server...")
	
	// Create a deadline to wait for
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	// Attempt graceful shutdown
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}
	
	logger.Info("✅ Server gracefully stopped")
}

// validateTagHandler handles tag address validation requests
func validateTagHandler(w http.ResponseWriter, r *http.Request) {
	var req TagValidationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}
	
	// Create validator for the specified manufacturer
	manufacturer := models.ManufacturerType(req.Manufacturer)
	validator, err := models.CreateTagAddressValidator(manufacturer)
	if err != nil {
		response := TagValidationResponse{
			Valid: false,
			Error: fmt.Sprintf("Unsupported manufacturer: %s", req.Manufacturer),
		}
		sendJSONResponse(w, response, http.StatusBadRequest)
		return
	}
	
	// Validate the address
	validationErr := validator.ValidateAddress(req.Address, req.DataType)
	
	response := TagValidationResponse{
		Valid: validationErr == nil,
	}
	
	if validationErr != nil {
		response.Error = validationErr.Error()
	}
	
	sendJSONResponse(w, response, http.StatusOK)
}

// parseTagHandler handles tag address parsing requests
func parseTagHandler(w http.ResponseWriter, r *http.Request) {
	var req TagValidationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}
	
	// Create validator for the specified manufacturer
	manufacturer := models.ManufacturerType(req.Manufacturer)
	validator, err := models.CreateTagAddressValidator(manufacturer)
	if err != nil {
		response := TagValidationResponse{
			Valid: false,
			Error: fmt.Sprintf("Unsupported manufacturer: %s", req.Manufacturer),
		}
		sendJSONResponse(w, response, http.StatusBadRequest)
		return
	}
	
	// Parse the address
	components, parseErr := validator.ParseAddress(req.Address, req.DataType)
	
	response := TagValidationResponse{
		Valid:      parseErr == nil,
		Components: components,
	}
	
	if parseErr != nil {
		response.Error = parseErr.Error()
	}
	
	sendJSONResponse(w, response, http.StatusOK)
}

// getManufacturersHandler returns information about supported manufacturers
func getManufacturersHandler(w http.ResponseWriter, r *http.Request) {
	manufacturers := []ManufacturerInfo{
		{
			Name:              "allen_bradley",
			SupportedDataTypes: models.NewAllenBradleyValidator().GetSupportedDataTypes(),
		},
		{
			Name:              "siemens",
			SupportedDataTypes: models.NewSiemensValidator().GetSupportedDataTypes(),
		},
		{
			Name:              "mitsubishi",
			SupportedDataTypes: models.NewMitsubishiValidator().GetSupportedDataTypes(),
		},
		{
			Name:              "schneider",
			SupportedDataTypes: models.NewSchneiderValidator().GetSupportedDataTypes(),
		},
		{
			Name:              "omron",
			SupportedDataTypes: models.NewOmronValidator().GetSupportedDataTypes(),
		},
	}
	
	sendJSONResponse(w, manufacturers, http.StatusOK)
}

// getDataTypesHandler returns supported data types for a specific manufacturer
func getDataTypesHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	manufacturerName := vars["manufacturer"]
	
	manufacturer := models.ManufacturerType(manufacturerName)
	validator, err := models.CreateTagAddressValidator(manufacturer)
	if err != nil {
		http.Error(w, "Unsupported manufacturer", http.StatusBadRequest)
		return
	}
	
	dataTypes := validator.GetSupportedDataTypes()
	sendJSONResponse(w, dataTypes, http.StatusOK)
}

// healthCheckHandler returns the health status of the service
func healthCheckHandler(w http.ResponseWriter, r *http.Request) {
	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().UTC(),
		"service":   "PLC Manufacturer Support API",
		"version":   "1.0.0",
		"supported_manufacturers": []string{
			"allen_bradley",
			"siemens", 
			"mitsubishi",
			"schneider",
			"omron",
		},
	}
	
	sendJSONResponse(w, health, http.StatusOK)
}

// corsMiddleware adds CORS headers to allow cross-origin requests
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}
		
		next.ServeHTTP(w, r)
	})
}

// sendJSONResponse sends a JSON response with the specified status code
func sendJSONResponse(w http.ResponseWriter, data interface{}, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	
	if err := json.NewEncoder(w).Encode(data); err != nil {
		logger.Errorf("Failed to encode JSON response: %v", err)
	}
} 