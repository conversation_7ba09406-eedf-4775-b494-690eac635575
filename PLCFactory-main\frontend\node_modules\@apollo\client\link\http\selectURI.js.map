{"version": 3, "file": "selectURI.js", "sourceRoot": "", "sources": ["../../../src/link/http/selectURI.ts"], "names": [], "mappings": "AAEA,MAAM,CAAC,IAAM,SAAS,GAAG,UACvB,SAAoB,EACpB,WAAyD;IAEzD,IAAM,OAAO,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;IACvC,IAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC;IAE/B,IAAI,UAAU,EAAE,CAAC;QACf,OAAO,UAAU,CAAC;IACpB,CAAC;SAAM,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE,CAAC;QAC7C,OAAO,WAAW,CAAC,SAAS,CAAC,CAAC;IAChC,CAAC;SAAM,CAAC;QACN,OAAQ,WAAsB,IAAI,UAAU,CAAC;IAC/C,CAAC;AACH,CAAC,CAAC", "sourcesContent": ["import type { Operation } from \"../core/index.js\";\n\nexport const selectURI = (\n  operation: Operation,\n  fallbackURI?: string | ((operation: Operation) => string)\n) => {\n  const context = operation.getContext();\n  const contextURI = context.uri;\n\n  if (contextURI) {\n    return contextURI;\n  } else if (typeof fallbackURI === \"function\") {\n    return fallbackURI(operation);\n  } else {\n    return (fallbackURI as string) || \"/graphql\";\n  }\n};\n"]}