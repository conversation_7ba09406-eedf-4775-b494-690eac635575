// PLC Studio - Professional Industrial Automation Platform
class PLCStudio {
    constructor() {
        this.editor = null;
        this.isConnected = false;
        this.init();
    }

    async init() {
        await this.initializeMonacoEditor();
        this.setupEventListeners();
        this.setupResizeHandles();
        this.startLiveDataSimulation();
    }

    async initializeMonacoEditor() {
        require.config({ paths: { vs: 'https://unpkg.com/monaco-editor@0.44.0/min/vs' } });
        
        return new Promise((resolve) => {
            require(['vs/editor/editor.main'], () => {
                this.editor = monaco.editor.create(document.getElementById('monacoEditor'), {
                    value: this.getDefaultPLCCode(),
                    language: 'javascript',
                    theme: 'vs',
                    fontSize: 17,
                    fontFamily: 'Monaco, Consolas, monospace',
                    automaticLayout: true,
                    minimap: { enabled: true },
                    lineHeight: 26,
                    padding: { top: 18, bottom: 18 }
                });
                resolve();
            });
        });
    }

    getDefaultPLCCode() {
        return `// PLC Main Program - Professional Industrial Automation
PROGRAM Main
VAR
    // System Variables
    Motor1_Run : BOOL := FALSE;
    Motor1_Speed : INT := 0;
    Conveyor_Speed : INT := 1250;
    Temperature_1 : REAL := 65.4;
    Safety_OK : BOOL := TRUE;
    
    // Input/Output Variables
    StartButton : BOOL;
    StopButton : BOOL;
    EmergencyStop : BOOL;
    SafetyGate : BOOL;
    LightCurtain : BOOL;
    ProductSensor : BOOL;
    
    // Timer Variables
    StartupTimer : TON;
    CycleTimer : TON;
END_VAR

// ========================================
// SAFETY SYSTEM - HIGHEST PRIORITY
// ========================================

// Safety System Check - Must be first
IF EmergencyStop AND SafetyGate AND LightCurtain THEN
    Safety_OK := TRUE;
ELSE
    Safety_OK := FALSE;
    Motor1_Run := FALSE;  // Emergency stop all motors
END_IF;

// ========================================
// MOTOR CONTROL LOGIC
// ========================================

// Motor Start/Stop Control
IF StartButton AND Safety_OK AND NOT StopButton THEN
    Motor1_Run := TRUE;
    Motor1_Speed := 1500;  // Set motor speed in RPM
ELSIF StopButton OR NOT Safety_OK THEN
    Motor1_Run := FALSE;
    Motor1_Speed := 0;
END_IF;

// ========================================
// CONVEYOR SYSTEM
// ========================================

// Conveyor Speed Control based on Motor
IF Motor1_Run THEN
    Conveyor_Speed := Motor1_Speed / 2;  // Half of motor speed
ELSE
    Conveyor_Speed := 0;
END_IF;

// ========================================
// TEMPERATURE MONITORING
// ========================================

// Temperature Safety Check
IF Temperature_1 > 80.0 THEN
    TemperatureAlarm := TRUE;
    Motor1_Run := FALSE;  // Stop motor on overtemperature
ELSE
    TemperatureAlarm := FALSE;
END_IF;

END_PROGRAM`;
    }

    setupEventListeners() {
        // Connection toggle
        document.getElementById('connectBtn').addEventListener('click', () => {
            this.toggleConnection();
        });

        // Data refresh
        document.getElementById('refreshData').addEventListener('click', () => {
            this.refreshLiveData();
        });

        // Manufacturer selection
        document.getElementById('manufacturerSelect').addEventListener('change', (e) => {
            this.switchManufacturer(e.target.value);
        });
    }

    toggleConnection() {
        this.isConnected = !this.isConnected;
        const btn = document.getElementById('connectBtn');
        const statusIndicator = document.getElementById('connectionStatus');
        const statusText = document.getElementById('connectionText');

        if (this.isConnected) {
            btn.innerHTML = '<i class="fas fa-unlink"></i> Disconnect';
            statusIndicator.classList.add('connected');
            statusText.textContent = 'Connected';
            this.addDiagnosticMessage('success', 'PLC connection established successfully');
        } else {
            btn.innerHTML = '<i class="fas fa-plug"></i> Connect PLC';
            statusIndicator.classList.remove('connected');
            statusText.textContent = 'Disconnected';
            this.addDiagnosticMessage('info', 'PLC disconnected');
        }
    }

    startLiveDataSimulation() {
        setInterval(() => {
            this.updateLiveData();
        }, 1500);
    }

    updateLiveData() {
        // Simulate realistic PLC scan cycle data
        const scanTime = (2.0 + Math.random() * 1.0).toFixed(1) + 'ms';
        const cpuLoad = Math.floor(10 + Math.random() * 20) + '%';
        const memoryUsage = Math.floor(40 + Math.random() * 20) + '%';
        
        document.getElementById('scanTime').textContent = scanTime;
        document.getElementById('cpuLoad').textContent = cpuLoad;
        document.getElementById('memoryUsage').textContent = memoryUsage;

        // Simulate live tag data updates
        this.updateTagValues();
    }

    updateTagValues() {
        const tags = document.querySelectorAll('.tag-item');
        tags.forEach(tag => {
            const nameElement = tag.querySelector('.tag-name');
            const valueElement = tag.querySelector('.tag-value');
            const tagName = nameElement.textContent;

            switch (tagName) {
                case 'Motor_1_Run':
                    // Simulate motor state changes
                    if (Math.random() < 0.15) {
                        const isTrue = valueElement.textContent === 'TRUE';
                        valueElement.textContent = isTrue ? 'FALSE' : 'TRUE';
                        valueElement.className = isTrue ? 'tag-value bool-false' : 'tag-value bool-true';
                    }
                    break;
                    
                case 'Conveyor_Speed':
                    // Simulate speed variations
                    const currentSpeed = parseInt(valueElement.textContent) || 1250;
                    const newSpeed = currentSpeed + Math.floor((Math.random() - 0.5) * 100);
                    valueElement.textContent = Math.max(1000, Math.min(1500, newSpeed));
                    break;
                    
                case 'Temperature_1':
                    // Simulate temperature drift
                    const currentTemp = parseFloat(valueElement.textContent) || 65.4;
                    const newTemp = currentTemp + (Math.random() - 0.5) * 2;
                    valueElement.textContent = Math.max(60, Math.min(75, newTemp)).toFixed(1);
                    break;

                case 'Safety_OK':
                    // Keep safety mostly OK but occasionally trigger
                    if (Math.random() < 0.05) {
                        const isTrue = valueElement.textContent === 'TRUE';
                        valueElement.textContent = isTrue ? 'FALSE' : 'TRUE';
                        valueElement.className = isTrue ? 'tag-value bool-false' : 'tag-value bool-true';
                    }
                    break;
            }
        });
    }

    refreshLiveData() {
        const btn = document.getElementById('refreshData');
        const icon = btn.querySelector('i');
        
        icon.classList.add('spinning');
        this.updateLiveData();
        
        setTimeout(() => {
            icon.classList.remove('spinning');
        }, 500);
    }

    addDiagnosticMessage(type, message) {
        const diagnosticsPanel = document.querySelector('.diagnostics-panel');
        const timestamp = new Date().toLocaleTimeString();
        
        const messageElement = document.createElement('div');
        messageElement.className = `diagnostic-item ${type}`;
        messageElement.innerHTML = `
            <i class="fas fa-${this.getDiagnosticIcon(type)}"></i>
            <span>${message}</span>
            <span class="timestamp">${timestamp}</span>
        `;
        
        diagnosticsPanel.insertBefore(messageElement, diagnosticsPanel.firstChild);
        
        // Keep max 10 messages
        const messages = diagnosticsPanel.querySelectorAll('.diagnostic-item');
        if (messages.length > 10) {
            messages[messages.length - 1].remove();
        }
    }

    getDiagnosticIcon(type) {
        const icons = {
            'info': 'info-circle',
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'error': 'exclamation-circle'
        };
        return icons[type] || 'info-circle';
    }

    switchManufacturer(manufacturer) {
        this.addDiagnosticMessage('info', `Switched to ${manufacturer.replace('_', ' ')} configuration`);
        
        // Update editor with manufacturer-specific template
        if (this.editor) {
            const template = this.getManufacturerTemplate(manufacturer);
            if (template) {
                // Add comment about manufacturer switch
                const currentCode = this.editor.getValue();
                const newCode = `${template}\n\n// Previous code:\n/* ${currentCode} */`;
                this.editor.setValue(newCode);
            }
        }
    }

    getManufacturerTemplate(manufacturer) {
        const templates = {
            'allen_bradley': `// Allen-Bradley Rockwell Automation Template
PROGRAM Main
VAR
    Motor_Start : BOOL;
    Motor_Run : BOOL;
    LocalTag : BOOL;
END_VAR

// Allen-Bradley style addressing
Motor_Run := Program:Main.LocalTag;
END_PROGRAM`,

            'siemens': `// Siemens S7 TIA Portal Template  
PROGRAM Main
VAR
    Motor_Start : BOOL;
    Motor_Run : BOOL;
END_VAR

// Siemens S7 addressing
Motor_Run := "DB1".DBX0.0;
END_PROGRAM`,

            'mitsubishi': `// Mitsubishi iQ Platform Template
PROGRAM Main
VAR
    Motor_Start : BOOL;
    Motor_Run : BOOL;
END_VAR

// Mitsubishi addressing
Motor_Run := X0;
END_PROGRAM`,

            'schneider': `// Schneider Electric EcoStruxure Template
PROGRAM Main
VAR
    Motor_Start : BOOL;
    Motor_Run : BOOL;
END_VAR

// Modicon addressing
Motor_Run := %IX0.0;
END_PROGRAM`,

            'omron': `// Omron Sysmac Studio Template
PROGRAM Main
VAR
    Motor_Start : BOOL;
    Motor_Run : BOOL;
END_VAR

// Omron CJ/CS addressing
Motor_Run := CIO0.0;
END_PROGRAM`
        };
        
        return templates[manufacturer];
    }

    setupResizeHandles() {
        // Sidebar resize
        this.setupHorizontalResize('sidebarResize', 'sidebar', 'left');
        
        // Data panel resize
        this.setupHorizontalResize('dataPanelResize', 'data-panel', 'right');
        
        // Bottom panel resize
        this.setupVerticalResize('bottomPanelResize', 'bottom-panel');
    }

    setupHorizontalResize(handleId, panelClass, side) {
        const handle = document.getElementById(handleId);
        const panel = document.querySelector(`.${panelClass}`);
        let isResizing = false;
        let startX = 0;
        let startWidth = 0;

        handle.addEventListener('mousedown', (e) => {
            isResizing = true;
            startX = e.clientX;
            startWidth = parseInt(window.getComputedStyle(panel).width, 10);
            
            document.body.classList.add('resizing-active');
            handle.classList.add('resizing');
            
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isResizing) return;

            let newWidth;
            if (side === 'left') {
                newWidth = startWidth + (e.clientX - startX);
            } else {
                newWidth = startWidth - (e.clientX - startX);
            }

            // Apply min/max constraints
            const minWidth = parseInt(window.getComputedStyle(panel).minWidth, 10);
            const maxWidth = parseInt(window.getComputedStyle(panel).maxWidth, 10);
            
            newWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));
            panel.style.width = newWidth + 'px';

            // Trigger Monaco editor resize if needed
            if (this.editor) {
                setTimeout(() => this.editor.layout(), 10);
            }
        });

        document.addEventListener('mouseup', () => {
            if (isResizing) {
                isResizing = false;
                document.body.classList.remove('resizing-active');
                handle.classList.remove('resizing');
            }
        });
    }

    setupVerticalResize(handleId, panelClass) {
        const handle = document.getElementById(handleId);
        const panel = document.querySelector(`.${panelClass}`);
        let isResizing = false;
        let startY = 0;
        let startHeight = 0;

        handle.addEventListener('mousedown', (e) => {
            isResizing = true;
            startY = e.clientY;
            startHeight = parseInt(window.getComputedStyle(panel).height, 10);
            
            document.body.classList.add('resizing-active-vertical');
            handle.classList.add('resizing');
            
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isResizing) return;

            const newHeight = startHeight - (e.clientY - startY);

            // Apply min/max constraints
            const minHeight = parseInt(window.getComputedStyle(panel).minHeight, 10);
            const maxHeight = parseInt(window.getComputedStyle(panel).maxHeight, 10);
            
            const constrainedHeight = Math.max(minHeight, Math.min(maxHeight, newHeight));
            panel.style.height = constrainedHeight + 'px';

            // Trigger Monaco editor resize if needed
            if (this.editor) {
                setTimeout(() => this.editor.layout(), 10);
            }
        });

        document.addEventListener('mouseup', () => {
            if (isResizing) {
                isResizing = false;
                document.body.classList.remove('resizing-active-vertical');
                handle.classList.remove('resizing');
            }
        });
    }
}

// Initialize PLC Studio when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('🏭 PLC Studio - Professional Industrial Automation Platform');
    console.log('🚀 Initializing Monaco Editor and Live Data Systems...');
    new PLCStudio();
});
