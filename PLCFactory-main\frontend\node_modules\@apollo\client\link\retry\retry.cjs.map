{"version": 3, "file": "retry.cjs", "sources": ["delayFunction.js", "retryFunction.js", "retryLink.js"], "sourcesContent": ["export function buildDelayFunction(delayOptions) {\n    var _a = delayOptions || {}, _b = _a.initial, initial = _b === void 0 ? 300 : _b, _c = _a.jitter, jitter = _c === void 0 ? true : _c, _d = _a.max, max = _d === void 0 ? Infinity : _d;\n    // If we're jittering, baseDelay is half of the maximum delay for that\n    // attempt (and is, on average, the delay we will encounter).\n    // If we're not jittering, adjust baseDelay so that the first attempt\n    // lines up with initialDelay, for everyone's sanity.\n    var baseDelay = jitter ? initial : initial / 2;\n    return function delayFunction(count) {\n        var delay = Math.min(max, baseDelay * Math.pow(2, count));\n        if (jitter) {\n            // We opt for a full jitter approach for a mostly uniform distribution,\n            // but bound it within initialDelay and delay for everyone's sanity.\n            delay = Math.random() * delay;\n        }\n        return delay;\n    };\n}\n//# sourceMappingURL=delayFunction.js.map", "export function buildRetryFunction(retryOptions) {\n    var _a = retryOptions || {}, retryIf = _a.retryIf, _b = _a.max, max = _b === void 0 ? 5 : _b;\n    return function retryFunction(count, operation, error) {\n        if (count >= max)\n            return false;\n        return retryIf ? retryIf(error, operation) : !!error;\n    };\n}\n//# sourceMappingURL=retryFunction.js.map", "import { __awaiter, __extends, __generator } from \"tslib\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport { buildDelayFunction } from \"./delayFunction.js\";\nimport { buildRetryFunction } from \"./retryFunction.js\";\nimport { ApolloError, graphQLResultHasProtocolErrors, PROTOCOL_ERRORS_SYMBOL, } from \"../../errors/index.js\";\n/**\n * Tracking and management of operations that may be (or currently are) retried.\n */\nvar RetryableOperation = /** @class */ (function () {\n    function RetryableOperation(observer, operation, forward, delayFor, retryIf) {\n        var _this = this;\n        this.observer = observer;\n        this.operation = operation;\n        this.forward = forward;\n        this.delayFor = delayFor;\n        this.retryIf = retryIf;\n        this.retryCount = 0;\n        this.currentSubscription = null;\n        this.onError = function (error) { return __awaiter(_this, void 0, void 0, function () {\n            var shouldRetry;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this.retryCount += 1;\n                        return [4 /*yield*/, this.retryIf(this.retryCount, this.operation, error)];\n                    case 1:\n                        shouldRetry = _a.sent();\n                        if (shouldRetry) {\n                            this.scheduleRetry(this.delayFor(this.retryCount, this.operation, error));\n                            return [2 /*return*/];\n                        }\n                        this.observer.error(error);\n                        return [2 /*return*/];\n                }\n            });\n        }); };\n        this.try();\n    }\n    /**\n     * Stop retrying for the operation, and cancel any in-progress requests.\n     */\n    RetryableOperation.prototype.cancel = function () {\n        if (this.currentSubscription) {\n            this.currentSubscription.unsubscribe();\n        }\n        clearTimeout(this.timerId);\n        this.timerId = undefined;\n        this.currentSubscription = null;\n    };\n    RetryableOperation.prototype.try = function () {\n        var _this = this;\n        this.currentSubscription = this.forward(this.operation).subscribe({\n            next: function (result) {\n                var _a;\n                if (graphQLResultHasProtocolErrors(result)) {\n                    _this.onError(new ApolloError({\n                        protocolErrors: result.extensions[PROTOCOL_ERRORS_SYMBOL],\n                    }));\n                    // Unsubscribe from the current subscription to prevent the `complete`\n                    // handler to be called as a result of the stream closing.\n                    (_a = _this.currentSubscription) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n                    return;\n                }\n                _this.observer.next(result);\n            },\n            error: this.onError,\n            complete: this.observer.complete.bind(this.observer),\n        });\n    };\n    RetryableOperation.prototype.scheduleRetry = function (delay) {\n        var _this = this;\n        if (this.timerId) {\n            throw new Error(\"RetryLink BUG! Encountered overlapping retries\");\n        }\n        this.timerId = setTimeout(function () {\n            _this.timerId = undefined;\n            _this.try();\n        }, delay);\n    };\n    return RetryableOperation;\n}());\nvar RetryLink = /** @class */ (function (_super) {\n    __extends(RetryLink, _super);\n    function RetryLink(options) {\n        var _this = _super.call(this) || this;\n        var _a = options || {}, attempts = _a.attempts, delay = _a.delay;\n        _this.delayFor =\n            typeof delay === \"function\" ? delay : buildDelayFunction(delay);\n        _this.retryIf =\n            typeof attempts === \"function\" ? attempts : buildRetryFunction(attempts);\n        return _this;\n    }\n    RetryLink.prototype.request = function (operation, nextLink) {\n        var _this = this;\n        return new Observable(function (observer) {\n            var retryable = new RetryableOperation(observer, operation, nextLink, _this.delayFor, _this.retryIf);\n            return function () {\n                retryable.cancel();\n            };\n        });\n    };\n    return RetryLink;\n}(ApolloLink));\nexport { RetryLink };\n//# sourceMappingURL=retryLink.js.map"], "names": ["__awaiter", "__generator", "graphQLResultHasProtocolErrors", "ApolloError", "PROTOCOL_ERRORS_SYMBOL", "__extends", "Observable", "ApolloLink"], "mappings": ";;;;;;;;;AAAO,SAAS,kBAAkB,CAAC,YAAY,EAAE;AACjD,IAAI,IAAI,EAAE,GAAG,YAAY,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,CAAC;AAK3L,IAAI,IAAI,SAAS,GAAG,MAAM,GAAG,OAAO,GAAG,OAAO,GAAG,CAAC,CAAC;AACnD,IAAI,OAAO,SAAS,aAAa,CAAC,KAAK,EAAE;AACzC,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AAClE,QAAQ,IAAI,MAAM,EAAE;AAGpB,YAAY,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC;AAC1C,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN;;AChBO,SAAS,kBAAkB,CAAC,YAAY,EAAE;AACjD,IAAI,IAAI,EAAE,GAAG,YAAY,IAAI,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AACjG,IAAI,OAAO,SAAS,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE;AAC3D,QAAQ,IAAI,KAAK,IAAI,GAAG;AACxB,YAAY,OAAO,KAAK,CAAC;AACzB,QAAQ,OAAO,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;AAC7D,KAAK,CAAC;AACN;;ACEA,IAAI,kBAAkB,KAAkB,YAAY;AACpD,IAAI,SAAS,kBAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE;AACjF,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACnC,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC/B,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC/B,QAAQ,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AAC5B,QAAQ,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AACxC,QAAQ,IAAI,CAAC,OAAO,GAAG,UAAU,KAAK,EAAE,EAAE,OAAOA,eAAS,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;AAC9F,YAAY,IAAI,WAAW,CAAC;AAC5B,YAAY,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AACnD,gBAAgB,QAAQ,EAAE,CAAC,KAAK;AAChC,oBAAoB,KAAK,CAAC;AAC1B,wBAAwB,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;AAC7C,wBAAwB,OAAO,CAAC,CAAC,GAAY,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;AACnG,oBAAoB,KAAK,CAAC;AAC1B,wBAAwB,WAAW,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;AAChD,wBAAwB,IAAI,WAAW,EAAE;AACzC,4BAA4B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;AACtG,4BAA4B,OAAO,CAAC,CAAC,EAAY,CAAC;AAClD,yBAAyB;AACzB,wBAAwB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACnD,wBAAwB,OAAO,CAAC,CAAC,EAAY,CAAC;AAC9C,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC,EAAE,CAAC;AACd,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC;AACnB,KAAK;AAIL,IAAI,kBAAkB,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY;AACtD,QAAQ,IAAI,IAAI,CAAC,mBAAmB,EAAE;AACtC,YAAY,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AACnD,SAAS;AACT,QAAQ,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACnC,QAAQ,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;AACjC,QAAQ,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AACxC,KAAK,CAAC;AACN,IAAI,kBAAkB,CAAC,SAAS,CAAC,GAAG,GAAG,YAAY;AACnD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC;AAC1E,YAAY,IAAI,EAAE,UAAU,MAAM,EAAE;AACpC,gBAAgB,IAAI,EAAE,CAAC;AACvB,gBAAgB,IAAIC,qCAA8B,CAAC,MAAM,CAAC,EAAE;AAC5D,oBAAoB,KAAK,CAAC,OAAO,CAAC,IAAIC,kBAAW,CAAC;AAClD,wBAAwB,cAAc,EAAE,MAAM,CAAC,UAAU,CAACC,6BAAsB,CAAC;AACjF,qBAAqB,CAAC,CAAC,CAAC;AAGxB,oBAAoB,CAAC,EAAE,GAAG,KAAK,CAAC,mBAAmB,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC;AAC3G,oBAAoB,OAAO;AAC3B,iBAAiB;AACjB,gBAAgB,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5C,aAAa;AACb,YAAY,KAAK,EAAE,IAAI,CAAC,OAAO;AAC/B,YAAY,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AAChE,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,kBAAkB,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,KAAK,EAAE;AAClE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE;AAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;AAC9E,SAAS;AACT,QAAQ,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,YAAY;AAC9C,YAAY,KAAK,CAAC,OAAO,GAAG,SAAS,CAAC;AACtC,YAAY,KAAK,CAAC,GAAG,EAAE,CAAC;AACxB,SAAS,EAAE,KAAK,CAAC,CAAC;AAClB,KAAK,CAAC;AACN,IAAI,OAAO,kBAAkB,CAAC;AAC9B,CAAC,EAAE,CAAC,CAAC;AACF,IAAC,SAAS,KAAkB,UAAU,MAAM,EAAE;AACjD,IAAIC,eAAS,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AACjC,IAAI,SAAS,SAAS,CAAC,OAAO,EAAE;AAChC,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;AAC9C,QAAQ,IAAI,EAAE,GAAG,OAAO,IAAI,EAAE,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;AACzE,QAAQ,KAAK,CAAC,QAAQ;AACtB,YAAY,OAAO,KAAK,KAAK,UAAU,GAAG,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAC5E,QAAQ,KAAK,CAAC,OAAO;AACrB,YAAY,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACrF,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,SAAS,EAAE,QAAQ,EAAE;AACjE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,OAAO,IAAIC,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAClD,YAAY,IAAI,SAAS,GAAG,IAAI,kBAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AACjH,YAAY,OAAO,YAAY;AAC/B,gBAAgB,SAAS,CAAC,MAAM,EAAE,CAAC;AACnC,aAAa,CAAC;AACd,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,OAAO,SAAS,CAAC;AACrB,CAAC,CAACC,eAAU,CAAC;;;;"}