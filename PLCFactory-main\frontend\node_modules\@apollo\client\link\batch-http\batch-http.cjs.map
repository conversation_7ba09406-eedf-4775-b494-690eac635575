{"version": 3, "file": "batch-http.cjs", "sources": ["../utils/filterOperationVariables.js", "batchHttpLink.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { visit } from \"graphql\";\nexport function filterOperationVariables(variables, query) {\n    var result = __assign({}, variables);\n    var unusedNames = new Set(Object.keys(variables));\n    visit(query, {\n        Variable: function (node, _key, parent) {\n            // A variable type definition at the top level of a query is not\n            // enough to silence server-side errors about the variable being\n            // unused, so variable definitions do not count as usage.\n            // https://spec.graphql.org/draft/#sec-All-Variables-Used\n            if (parent &&\n                parent.kind !== \"VariableDefinition\") {\n                unusedNames.delete(node.name.value);\n            }\n        },\n    });\n    unusedNames.forEach(function (name) {\n        delete result[name];\n    });\n    return result;\n}\n//# sourceMappingURL=filterOperationVariables.js.map", "import { __assign, __extends, __rest } from \"tslib\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { Observable, hasDirectives, maybe, removeClientSetsFromDocument, } from \"../../utilities/index.js\";\nimport { fromError } from \"../utils/index.js\";\nimport { serializeFetchParameter, selectURI, parseAndCheckHttpResponse, checkFetcher, selectHttpOptionsAndBodyInternal, defaultPrinter, fallbackHttpConfig, } from \"../http/index.js\";\nimport { BatchLink } from \"../batch/index.js\";\nimport { filterOperationVariables } from \"../utils/filterOperationVariables.js\";\nvar backupFetch = maybe(function () { return fetch; });\n/**\n * Transforms Operation for into HTTP results.\n * context can include the headers property, which will be passed to the fetch function\n */\nvar BatchHttpLink = /** @class */ (function (_super) {\n    __extends(BatchHttpLink, _super);\n    function BatchHttpLink(fetchParams) {\n        var _this = _super.call(this) || this;\n        var _a = fetchParams || {}, _b = _a.uri, uri = _b === void 0 ? \"/graphql\" : _b, \n        // use default global fetch if nothing is passed in\n        preferredFetch = _a.fetch, _c = _a.print, print = _c === void 0 ? defaultPrinter : _c, includeExtensions = _a.includeExtensions, preserveHeaderCase = _a.preserveHeaderCase, batchInterval = _a.batchInterval, batchDebounce = _a.batchDebounce, batchMax = _a.batchMax, batchKey = _a.batchKey, _d = _a.includeUnusedVariables, includeUnusedVariables = _d === void 0 ? false : _d, requestOptions = __rest(_a, [\"uri\", \"fetch\", \"print\", \"includeExtensions\", \"preserveHeaderCase\", \"batchInterval\", \"batchDebounce\", \"batchMax\", \"batchKey\", \"includeUnusedVariables\"]);\n        if (globalThis.__DEV__ !== false) {\n            // Make sure at least one of preferredFetch, window.fetch, or backupFetch\n            // is defined, so requests won't fail at runtime.\n            checkFetcher(preferredFetch || backupFetch);\n        }\n        var linkConfig = {\n            http: { includeExtensions: includeExtensions, preserveHeaderCase: preserveHeaderCase },\n            options: requestOptions.fetchOptions,\n            credentials: requestOptions.credentials,\n            headers: requestOptions.headers,\n        };\n        _this.batchDebounce = batchDebounce;\n        _this.batchInterval = batchInterval || 10;\n        _this.batchMax = batchMax || 10;\n        var batchHandler = function (operations) {\n            var chosenURI = selectURI(operations[0], uri);\n            var context = operations[0].getContext();\n            var clientAwarenessHeaders = {};\n            if (context.clientAwareness) {\n                var _a = context.clientAwareness, name_1 = _a.name, version = _a.version;\n                if (name_1) {\n                    clientAwarenessHeaders[\"apollographql-client-name\"] = name_1;\n                }\n                if (version) {\n                    clientAwarenessHeaders[\"apollographql-client-version\"] = version;\n                }\n            }\n            var contextConfig = {\n                http: context.http,\n                options: context.fetchOptions,\n                credentials: context.credentials,\n                headers: __assign(__assign({}, clientAwarenessHeaders), context.headers),\n            };\n            var queries = operations.map(function (_a) {\n                var query = _a.query;\n                if (hasDirectives([\"client\"], query)) {\n                    return removeClientSetsFromDocument(query);\n                }\n                return query;\n            });\n            // If we have a query that returned `null` after removing client-only\n            // fields, it indicates a query that is using all client-only fields.\n            if (queries.some(function (query) { return !query; })) {\n                return fromError(new Error(\"BatchHttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or enable the `transformOptions.removeClientFields` option.\"));\n            }\n            //uses fallback, link, and then context to build options\n            var optsAndBody = operations.map(function (operation, index) {\n                var result = selectHttpOptionsAndBodyInternal(__assign(__assign({}, operation), { query: queries[index] }), print, fallbackHttpConfig, linkConfig, contextConfig);\n                if (result.body.variables && !includeUnusedVariables) {\n                    result.body.variables = filterOperationVariables(result.body.variables, operation.query);\n                }\n                return result;\n            });\n            var loadedBody = optsAndBody.map(function (_a) {\n                var body = _a.body;\n                return body;\n            });\n            var options = optsAndBody[0].options;\n            // There's no spec for using GET with batches.\n            if (options.method === \"GET\") {\n                return fromError(new Error(\"apollo-link-batch-http does not support GET requests\"));\n            }\n            try {\n                options.body = serializeFetchParameter(loadedBody, \"Payload\");\n            }\n            catch (parseError) {\n                return fromError(parseError);\n            }\n            var controller;\n            if (!options.signal && typeof AbortController !== \"undefined\") {\n                controller = new AbortController();\n                options.signal = controller.signal;\n            }\n            return new Observable(function (observer) {\n                // Prefer BatchHttpLink.Options.fetch (preferredFetch) if provided, and\n                // otherwise fall back to the *current* global window.fetch function\n                // (see issue #7832), or (if all else fails) the backupFetch function we\n                // saved when this module was first evaluated. This last option protects\n                // against the removal of window.fetch, which is unlikely but not\n                // impossible.\n                var currentFetch = preferredFetch || maybe(function () { return fetch; }) || backupFetch;\n                currentFetch(chosenURI, options)\n                    .then(function (response) {\n                    // Make the raw response available in the context.\n                    operations.forEach(function (operation) {\n                        return operation.setContext({ response: response });\n                    });\n                    return response;\n                })\n                    .then(parseAndCheckHttpResponse(operations))\n                    .then(function (result) {\n                    controller = undefined;\n                    // we have data and can send it to back up the link chain\n                    observer.next(result);\n                    observer.complete();\n                    return result;\n                })\n                    .catch(function (err) {\n                    controller = undefined;\n                    // if it is a network error, BUT there is graphql result info\n                    // fire the next observer before calling error\n                    // this gives apollo-client (and react-apollo) the `graphqlErrors` and `networkErrors`\n                    // to pass to UI\n                    // this should only happen if we *also* have data as part of the response key per\n                    // the spec\n                    if (err.result && err.result.errors && err.result.data) {\n                        // if we dont' call next, the UI can only show networkError because AC didn't\n                        // get andy graphqlErrors\n                        // this is graphql execution result info (i.e errors and possibly data)\n                        // this is because there is no formal spec how errors should translate to\n                        // http status codes. So an auth error (401) could have both data\n                        // from a public field, errors from a private field, and a status of 401\n                        // {\n                        //  user { // this will have errors\n                        //    firstName\n                        //  }\n                        //  products { // this is public so will have data\n                        //    cost\n                        //  }\n                        // }\n                        //\n                        // the result of above *could* look like this:\n                        // {\n                        //   data: { products: [{ cost: \"$10\" }] },\n                        //   errors: [{\n                        //      message: 'your session has timed out',\n                        //      path: []\n                        //   }]\n                        // }\n                        // status code of above would be a 401\n                        // in the UI you want to show data where you can, errors as data where you can\n                        // and use correct http status codes\n                        observer.next(err.result);\n                    }\n                    observer.error(err);\n                });\n                return function () {\n                    // XXX support canceling this request\n                    // https://developers.google.com/web/updates/2017/09/abortable-fetch\n                    if (controller)\n                        controller.abort();\n                };\n            });\n        };\n        batchKey =\n            batchKey ||\n                (function (operation) {\n                    var context = operation.getContext();\n                    var contextConfig = {\n                        http: context.http,\n                        options: context.fetchOptions,\n                        credentials: context.credentials,\n                        headers: context.headers,\n                    };\n                    //may throw error if config not serializable\n                    return selectURI(operation, uri) + JSON.stringify(contextConfig);\n                });\n        _this.batcher = new BatchLink({\n            batchDebounce: _this.batchDebounce,\n            batchInterval: _this.batchInterval,\n            batchMax: _this.batchMax,\n            batchKey: batchKey,\n            batchHandler: batchHandler,\n        });\n        return _this;\n    }\n    BatchHttpLink.prototype.request = function (operation) {\n        return this.batcher.request(operation);\n    };\n    return BatchHttpLink;\n}(ApolloLink));\nexport { BatchHttpLink };\n//# sourceMappingURL=batchHttpLink.js.map"], "names": ["__assign", "visit", "maybe", "__extends", "defaultPrinter", "__rest", "checkFetcher", "selectURI", "hasDirectives", "removeClientSetsFromDocument", "fromError", "selectHttpOptionsAndBodyInternal", "fallbackHttpConfig", "serializeFetchParameter", "Observable", "parseAndCheckHttpResponse", "BatchLink", "ApolloLink"], "mappings": ";;;;;;;;;;;;AAEO,SAAS,wBAAwB,CAAC,SAAS,EAAE,KAAK,EAAE;AAC3D,IAAI,IAAI,MAAM,GAAGA,cAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;AACzC,IAAI,IAAI,WAAW,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AACtD,IAAIC,aAAK,CAAC,KAAK,EAAE;AACjB,QAAQ,QAAQ,EAAE,UAAU,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAKhD,YAAY,IAAI,MAAM;AACtB,gBAAgB,MAAM,CAAC,IAAI,KAAK,oBAAoB,EAAE;AACtD,gBAAgB,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpD,aAAa;AACb,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AACxC,QAAQ,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5B,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM,CAAC;AAClB;;ACdA,IAAI,WAAW,GAAGC,eAAK,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC,CAAC;AAKpD,IAAC,aAAa,KAAkB,UAAU,MAAM,EAAE;AACrD,IAAIC,eAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AACrC,IAAI,SAAS,aAAa,CAAC,WAAW,EAAE;AACxC,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;AAC9C,QAAQ,IAAI,EAAE,GAAG,WAAW,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE;AAEtF,QAAQ,cAAc,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,KAAK,CAAC,GAAGC,mBAAc,GAAG,EAAE,EAAE,iBAAiB,GAAG,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,GAAG,EAAE,CAAC,kBAAkB,EAAE,aAAa,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,GAAG,EAAE,CAAC,aAAa,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,CAAC,sBAAsB,EAAE,sBAAsB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE,cAAc,GAAGC,YAAM,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,eAAe,EAAE,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,wBAAwB,CAAC,CAAC,CAAC;AACpjB,QAAQ,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAG1C,YAAYC,iBAAY,CAAC,cAAc,IAAI,WAAW,CAAC,CAAC;AACxD,SAAS;AACT,QAAQ,IAAI,UAAU,GAAG;AACzB,YAAY,IAAI,EAAE,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE;AAClG,YAAY,OAAO,EAAE,cAAc,CAAC,YAAY;AAChD,YAAY,WAAW,EAAE,cAAc,CAAC,WAAW;AACnD,YAAY,OAAO,EAAE,cAAc,CAAC,OAAO;AAC3C,SAAS,CAAC;AACV,QAAQ,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;AAC5C,QAAQ,KAAK,CAAC,aAAa,GAAG,aAAa,IAAI,EAAE,CAAC;AAClD,QAAQ,KAAK,CAAC,QAAQ,GAAG,QAAQ,IAAI,EAAE,CAAC;AACxC,QAAQ,IAAI,YAAY,GAAG,UAAU,UAAU,EAAE;AACjD,YAAY,IAAI,SAAS,GAAGC,cAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC1D,YAAY,IAAI,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;AACrD,YAAY,IAAI,sBAAsB,GAAG,EAAE,CAAC;AAC5C,YAAY,IAAI,OAAO,CAAC,eAAe,EAAE;AACzC,gBAAgB,IAAI,EAAE,GAAG,OAAO,CAAC,eAAe,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC;AACzF,gBAAgB,IAAI,MAAM,EAAE;AAC5B,oBAAoB,sBAAsB,CAAC,2BAA2B,CAAC,GAAG,MAAM,CAAC;AACjF,iBAAiB;AACjB,gBAAgB,IAAI,OAAO,EAAE;AAC7B,oBAAoB,sBAAsB,CAAC,8BAA8B,CAAC,GAAG,OAAO,CAAC;AACrF,iBAAiB;AACjB,aAAa;AACb,YAAY,IAAI,aAAa,GAAG;AAChC,gBAAgB,IAAI,EAAE,OAAO,CAAC,IAAI;AAClC,gBAAgB,OAAO,EAAE,OAAO,CAAC,YAAY;AAC7C,gBAAgB,WAAW,EAAE,OAAO,CAAC,WAAW;AAChD,gBAAgB,OAAO,EAAEP,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,sBAAsB,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC;AACxF,aAAa,CAAC;AACd,YAAY,IAAI,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE;AACvD,gBAAgB,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;AACrC,gBAAgB,IAAIQ,uBAAa,CAAC,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,EAAE;AACtD,oBAAoB,OAAOC,sCAA4B,CAAC,KAAK,CAAC,CAAC;AAC/D,iBAAiB;AACjB,gBAAgB,OAAO,KAAK,CAAC;AAC7B,aAAa,CAAC,CAAC;AAGf,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;AACnE,gBAAgB,OAAOC,eAAS,CAAC,IAAI,KAAK,CAAC,qMAAqM,CAAC,CAAC,CAAC;AACnP,aAAa;AAEb,YAAY,IAAI,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,UAAU,SAAS,EAAE,KAAK,EAAE;AACzE,gBAAgB,IAAI,MAAM,GAAGC,qCAAgC,CAACX,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,EAAEY,uBAAkB,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;AAClL,gBAAgB,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,sBAAsB,EAAE;AACtE,oBAAoB,MAAM,CAAC,IAAI,CAAC,SAAS,GAAG,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;AAC7G,iBAAiB;AACjB,gBAAgB,OAAO,MAAM,CAAC;AAC9B,aAAa,CAAC,CAAC;AACf,YAAY,IAAI,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE;AAC3D,gBAAgB,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACnC,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa,CAAC,CAAC;AACf,YAAY,IAAI,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AAEjD,YAAY,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE;AAC1C,gBAAgB,OAAOF,eAAS,CAAC,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC,CAAC;AACpG,aAAa;AACb,YAAY,IAAI;AAChB,gBAAgB,OAAO,CAAC,IAAI,GAAGG,4BAAuB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AAC9E,aAAa;AACb,YAAY,OAAO,UAAU,EAAE;AAC/B,gBAAgB,OAAOH,eAAS,CAAC,UAAU,CAAC,CAAC;AAC7C,aAAa;AACb,YAAY,IAAI,UAAU,CAAC;AAC3B,YAAY,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE;AAC3E,gBAAgB,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;AACnD,gBAAgB,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;AACnD,aAAa;AACb,YAAY,OAAO,IAAII,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAOtD,gBAAgB,IAAI,YAAY,GAAG,cAAc,IAAIZ,eAAK,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI,WAAW,CAAC;AACzG,gBAAgB,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC;AAChD,qBAAqB,IAAI,CAAC,UAAU,QAAQ,EAAE;AAE9C,oBAAoB,UAAU,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AAC5D,wBAAwB,OAAO,SAAS,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC5E,qBAAqB,CAAC,CAAC;AACvB,oBAAoB,OAAO,QAAQ,CAAC;AACpC,iBAAiB,CAAC;AAClB,qBAAqB,IAAI,CAACa,8BAAyB,CAAC,UAAU,CAAC,CAAC;AAChE,qBAAqB,IAAI,CAAC,UAAU,MAAM,EAAE;AAC5C,oBAAoB,UAAU,GAAG,SAAS,CAAC;AAE3C,oBAAoB,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1C,oBAAoB,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACxC,oBAAoB,OAAO,MAAM,CAAC;AAClC,iBAAiB,CAAC;AAClB,qBAAqB,KAAK,CAAC,UAAU,GAAG,EAAE;AAC1C,oBAAoB,UAAU,GAAG,SAAS,CAAC;AAO3C,oBAAoB,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE;AA2B5E,wBAAwB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAClD,qBAAqB;AACrB,oBAAoB,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACxC,iBAAiB,CAAC,CAAC;AACnB,gBAAgB,OAAO,YAAY;AAGnC,oBAAoB,IAAI,UAAU;AAClC,wBAAwB,UAAU,CAAC,KAAK,EAAE,CAAC;AAC3C,iBAAiB,CAAC;AAClB,aAAa,CAAC,CAAC;AACf,SAAS,CAAC;AACV,QAAQ,QAAQ;AAChB,YAAY,QAAQ;AACpB,iBAAiB,UAAU,SAAS,EAAE;AACtC,oBAAoB,IAAI,OAAO,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AACzD,oBAAoB,IAAI,aAAa,GAAG;AACxC,wBAAwB,IAAI,EAAE,OAAO,CAAC,IAAI;AAC1C,wBAAwB,OAAO,EAAE,OAAO,CAAC,YAAY;AACrD,wBAAwB,WAAW,EAAE,OAAO,CAAC,WAAW;AACxD,wBAAwB,OAAO,EAAE,OAAO,CAAC,OAAO;AAChD,qBAAqB,CAAC;AAEtB,oBAAoB,OAAOR,cAAS,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;AACrF,iBAAiB,CAAC,CAAC;AACnB,QAAQ,KAAK,CAAC,OAAO,GAAG,IAAIS,eAAS,CAAC;AACtC,YAAY,aAAa,EAAE,KAAK,CAAC,aAAa;AAC9C,YAAY,aAAa,EAAE,KAAK,CAAC,aAAa;AAC9C,YAAY,QAAQ,EAAE,KAAK,CAAC,QAAQ;AACpC,YAAY,QAAQ,EAAE,QAAQ;AAC9B,YAAY,YAAY,EAAE,YAAY;AACtC,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,aAAa,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,SAAS,EAAE;AAC3D,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAC/C,KAAK,CAAC;AACN,IAAI,OAAO,aAAa,CAAC;AACzB,CAAC,CAACC,eAAU,CAAC;;;;"}