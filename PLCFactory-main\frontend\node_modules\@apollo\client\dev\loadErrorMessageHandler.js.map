{"version": 3, "file": "loadErrorMessageHandler.js", "sourceRoot": "", "sources": ["../../src/dev/loadErrorMessageHandler.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,+BAA+B,CAAC;AACvD,OAAO,EAAE,yBAAyB,EAAE,MAAM,2CAA2C,CAAC;AAEtF,OAAO,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAC;AAErE;;;GAGG;AACH,MAAM,UAAU,uBAAuB;IAAC,oBAA2B;SAA3B,UAA2B,EAA3B,qBAA2B,EAA3B,IAA2B;QAA3B,+BAA2B;;IACjE,sBAAsB,CAAC,OAAsC,CAAC,CAAC;IAE/D,KAAoB,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU,EAAE,CAAC;QAA5B,IAAM,KAAK,mBAAA;QACd,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAChC,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,IAAM,OAAO,GAAG,CAAC,UAAC,OAAwB,EAAE,IAAe;IACzD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAChC,IAAM,UAAU,GAAG,MAAM,CAAC,yBAAyB,CAAE,CAAC,OAAO,CAAC,CAAC;QAC/D,IAAI,CAAC,OAAO,IAAI,CAAC,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,OAAO,CAAA;YAAE,OAAO;QAC7C,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;IAC/B,CAAC;IACD,OAAO,IAAI,CAAC,MAAM,CAChB,UAAC,GAAG,EAAE,GAAG,IAAK,OAAA,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAnC,CAAmC,EACjD,MAAM,CAAC,OAAO,CAAC,CAChB,CAAC;AACJ,CAAC,CAAqC,CAAC", "sourcesContent": ["import type { ErrorCodes } from \"../invariantErrorCodes.js\";\nimport { global } from \"../utilities/globals/index.js\";\nimport { ApolloErrorMessageHandler } from \"../utilities/globals/invariantWrappers.js\";\nimport type { ErrorMessageHandler } from \"./setErrorMessageHandler.js\";\nimport { setErrorMessageHandler } from \"./setErrorMessageHandler.js\";\n\n/**\n * Injects Apollo Client's default error message handler into the application and\n * also loads the error codes that are passed in as arguments.\n */\nexport function loadErrorMessageHandler(...errorCodes: ErrorCodes[]) {\n  setErrorMessageHandler(handler as typeof handler & ErrorCodes);\n\n  for (const codes of errorCodes) {\n    Object.assign(handler, codes);\n  }\n\n  return handler;\n}\n\nconst handler = ((message: string | number, args: unknown[]) => {\n  if (typeof message === \"number\") {\n    const definition = global[ApolloErrorMessageHandler]![message];\n    if (!message || !definition?.message) return;\n    message = definition.message;\n  }\n  return args.reduce<string>(\n    (msg, arg) => msg.replace(/%[sdfo]/, String(arg)),\n    String(message)\n  );\n}) as ErrorMessageHandler & ErrorCodes;\n"]}