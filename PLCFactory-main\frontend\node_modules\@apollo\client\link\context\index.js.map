{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/link/context/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAE9C,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAQtD,MAAM,UAAU,UAAU,CAAC,MAAqB;IAC9C,OAAO,IAAI,UAAU,CAAC,UAAC,SAAoB,EAAE,OAAiB;QAC5D,IAAW,OAAO,UAAK,SAAS,EAA1B,EAAc,CAAY,CAAC;QAEjC,OAAO,IAAI,UAAU,CAAC,UAAC,QAAQ;YAC7B,IAAI,MAA8B,CAAC;YACnC,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;iBACrB,IAAI,CAAC,UAAC,GAAG,IAAK,OAAA,MAAM,CAAC,GAAG,EAAE,SAAS,CAAC,UAAU,EAAE,CAAC,EAAnC,CAAmC,CAAC;iBAClD,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;iBAC1B,IAAI,CAAC;gBACJ,2DAA2D;gBAC3D,IAAI,MAAM;oBAAE,OAAO;gBACnB,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC;oBACpC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAClC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;oBACpC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC3C,CAAC,CAAC;YACL,CAAC,CAAC;iBACD,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAExC,OAAO;gBACL,MAAM,GAAG,IAAI,CAAC;gBACd,IAAI,MAAM;oBAAE,MAAM,CAAC,WAAW,EAAE,CAAC;YACnC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import type { Operation, GraphQLRequest, NextLink } from \"../core/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport type { ObservableSubscription } from \"../../utilities/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport type { DefaultContext } from \"../../core/index.js\";\n\nexport type ContextSetter = (\n  operation: GraphQLRequest,\n  prevContext: DefaultContext\n) => Promise<DefaultContext> | DefaultContext;\n\nexport function setContext(setter: ContextSetter): ApolloLink {\n  return new ApolloLink((operation: Operation, forward: NextLink) => {\n    const { ...request } = operation;\n\n    return new Observable((observer) => {\n      let handle: ObservableSubscription;\n      let closed = false;\n      Promise.resolve(request)\n        .then((req) => setter(req, operation.getContext()))\n        .then(operation.setContext)\n        .then(() => {\n          // if the observer is already closed, no need to subscribe.\n          if (closed) return;\n          handle = forward(operation).subscribe({\n            next: observer.next.bind(observer),\n            error: observer.error.bind(observer),\n            complete: observer.complete.bind(observer),\n          });\n        })\n        .catch(observer.error.bind(observer));\n\n      return () => {\n        closed = true;\n        if (handle) handle.unsubscribe();\n      };\n    });\n  });\n}\n"]}