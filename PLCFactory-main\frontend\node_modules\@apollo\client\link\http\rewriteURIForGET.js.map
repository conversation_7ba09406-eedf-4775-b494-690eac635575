{"version": 3, "file": "rewriteURIForGET.js", "sourceRoot": "", "sources": ["../../../src/link/http/rewriteURIForGET.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,uBAAuB,EAAE,MAAM,8BAA8B,CAAC;AAGvE,4EAA4E;AAC5E,eAAe;AACf,MAAM,UAAU,gBAAgB,CAAC,SAAiB,EAAE,IAAU;IAC5D,yEAAyE;IACzE,yCAAyC;IACzC,IAAM,WAAW,GAAa,EAAE,CAAC;IACjC,IAAM,aAAa,GAAG,UAAC,GAAW,EAAE,KAAa;QAC/C,WAAW,CAAC,IAAI,CAAC,UAAG,GAAG,cAAI,kBAAkB,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC;IAC1D,CAAC,CAAC;IAEF,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,KAAM,CAAC,CAAC;IACtC,CAAC;IACD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACrD,CAAC;IACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,IAAI,mBAAmB,SAAA,CAAC;QACxB,IAAI,CAAC;YACH,mBAAmB,GAAG,uBAAuB,CAC3C,IAAI,CAAC,SAAS,EACd,eAAe,CAChB,CAAC;QACJ,CAAC;QAAC,OAAO,UAAU,EAAE,CAAC;YACpB,OAAO,EAAE,UAAU,YAAA,EAAE,CAAC;QACxB,CAAC;QACD,aAAa,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;IAClD,CAAC;IACD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,IAAI,oBAAoB,SAAA,CAAC;QACzB,IAAI,CAAC;YACH,oBAAoB,GAAG,uBAAuB,CAC5C,IAAI,CAAC,UAAU,EACf,gBAAgB,CACjB,CAAC;QACJ,CAAC;QAAC,OAAO,UAAU,EAAE,CAAC;YACpB,OAAO,EAAE,UAAU,YAAA,EAAE,CAAC;QACxB,CAAC;QACD,aAAa,CAAC,YAAY,EAAE,oBAAoB,CAAC,CAAC;IACpD,CAAC;IAED,+CAA+C;IAC/C,mEAAmE;IACnE,0EAA0E;IAC1E,yEAAyE;IACzE,kEAAkE;IAClE,mEAAmE;IACnE,IAAI,QAAQ,GAAG,EAAE,EACf,WAAW,GAAG,SAAS,CAAC;IAC1B,IAAM,aAAa,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC7C,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC;QACzB,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAC3C,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;IACnD,CAAC;IACD,IAAM,iBAAiB,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACtE,IAAM,MAAM,GACV,WAAW,GAAG,iBAAiB,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;IACrE,OAAO,EAAE,MAAM,QAAA,EAAE,CAAC;AACpB,CAAC", "sourcesContent": ["import { serializeFetchParameter } from \"./serializeFetchParameter.js\";\nimport type { Body } from \"./selectHttpOptionsAndBody.js\";\n\n// For GET operations, returns the given URI rewritten with parameters, or a\n// parse error.\nexport function rewriteURIForGET(chosenURI: string, body: Body) {\n  // Implement the standard HTTP GET serialization, plus 'extensions'. Note\n  // the extra level of JSON serialization!\n  const queryParams: string[] = [];\n  const addQueryParam = (key: string, value: string) => {\n    queryParams.push(`${key}=${encodeURIComponent(value)}`);\n  };\n\n  if (\"query\" in body) {\n    addQueryParam(\"query\", body.query!);\n  }\n  if (body.operationName) {\n    addQueryParam(\"operationName\", body.operationName);\n  }\n  if (body.variables) {\n    let serializedVariables;\n    try {\n      serializedVariables = serializeFetchParameter(\n        body.variables,\n        \"Variables map\"\n      );\n    } catch (parseError) {\n      return { parseError };\n    }\n    addQueryParam(\"variables\", serializedVariables);\n  }\n  if (body.extensions) {\n    let serializedExtensions;\n    try {\n      serializedExtensions = serializeFetchParameter(\n        body.extensions,\n        \"Extensions map\"\n      );\n    } catch (parseError) {\n      return { parseError };\n    }\n    addQueryParam(\"extensions\", serializedExtensions);\n  }\n\n  // Reconstruct the URI with added query params.\n  // XXX This assumes that the URI is well-formed and that it doesn't\n  //     already contain any of these query params. We could instead use the\n  //     URL API and take a polyfill (whatwg-url@6) for older browsers that\n  //     don't support URLSearchParams. Note that some browsers (and\n  //     versions of whatwg-url) support URL but not URLSearchParams!\n  let fragment = \"\",\n    preFragment = chosenURI;\n  const fragmentStart = chosenURI.indexOf(\"#\");\n  if (fragmentStart !== -1) {\n    fragment = chosenURI.substr(fragmentStart);\n    preFragment = chosenURI.substr(0, fragmentStart);\n  }\n  const queryParamsPrefix = preFragment.indexOf(\"?\") === -1 ? \"?\" : \"&\";\n  const newURI =\n    preFragment + queryParamsPrefix + queryParams.join(\"&\") + fragment;\n  return { newURI };\n}\n"]}