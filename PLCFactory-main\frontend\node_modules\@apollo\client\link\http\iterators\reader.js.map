{"version": 3, "file": "reader.js", "sourceRoot": "", "sources": ["../../../../src/link/http/iterators/reader.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAOxE,MAAM,CAAC,OAAO,UAAU,cAAc,CACpC,MAAsC;IAEtC,IAAM,QAAQ,GAAsB;QAClC,IAAI;YACF,OAAO,MAAM,CAAC,IAAI,EAKjB,CAAC;QACJ,CAAC;KACF,CAAC;IAEF,IAAI,yBAAyB,EAAE,CAAC;QAC9B,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG;YAI/B,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;IACJ,CAAC;IAED,OAAO,QAAoC,CAAC;AAC9C,CAAC", "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/reader.ts\n */\n\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\n\ninterface ReaderIterator<T> {\n  next(): Promise<IteratorResult<T, T | undefined>>;\n  [Symbol.asyncIterator]?(): AsyncIterator<T>;\n}\n\nexport default function readerIterator<T>(\n  reader: ReadableStreamDefaultReader<T>\n): AsyncIterableIterator<T> {\n  const iterator: ReaderIterator<T> = {\n    next() {\n      return reader.read() as Promise<\n        | ReadableStreamReadValueResult<T>\n        // DoneR<PERSON>ult has `value` optional, which doesn't comply with an\n        // `IteratorResult`, so we assert it to `T | undefined` instead\n        | Required<ReadableStreamReadDoneResult<T | undefined>>\n      >;\n    },\n  };\n\n  if (canUseAsyncIteratorSymbol) {\n    iterator[Symbol.asyncIterator] = function (): AsyncIterator<\n      T,\n      T | undefined\n    > {\n      return this;\n    };\n  }\n\n  return iterator as AsyncIterableIterator<T>;\n}\n"]}