{"version": 3, "sources": ["../../graphql-ws/lib/utils.mjs", "../../graphql-ws/lib/common.mjs", "../../graphql-ws/lib/client.mjs", "../../graphql-ws/lib/server.mjs"], "sourcesContent": ["/** @private */\nexport function extendedTypeof(val) {\n    if (val === null) {\n        return 'null';\n    }\n    if (Array.isArray(val)) {\n        return 'array';\n    }\n    return typeof val;\n}\n/** @private */\nexport function isObject(val) {\n    return extendedTypeof(val) === 'object';\n}\n/** @private */\nexport function isAsyncIterable(val) {\n    return typeof Object(val)[Symbol.asyncIterator] === 'function';\n}\n/** @private */\nexport function isAsyncGenerator(val) {\n    return (isObject(val) &&\n        typeof Object(val)[Symbol.asyncIterator] === 'function' &&\n        typeof val.return === 'function'\n    // for lazy ones, we only need the return anyway\n    // typeof val.throw === 'function' &&\n    // typeof val.next === 'function'\n    );\n}\n/** @private */\nexport function areGraphQLErrors(obj) {\n    return (Array.isArray(obj) &&\n        // must be at least one error\n        obj.length > 0 &&\n        // error has at least a message\n        obj.every((ob) => 'message' in ob));\n}\n/**\n * Limits the WebSocket close event reason to not exceed a length of one frame.\n * Reference: https://datatracker.ietf.org/doc/html/rfc6455#section-5.2.\n *\n * @private\n */\nexport function limitCloseReason(reason, whenTooLong) {\n    return reason.length < 124 ? reason : whenTooLong;\n}\n", "/**\n *\n * common\n *\n */\nimport { areGraphQLErrors, extendedTypeof, isObject } from './utils.mjs';\n/**\n * The WebSocket sub-protocol used for the [GraphQL over WebSocket Protocol](https://github.com/graphql/graphql-over-http/blob/main/rfcs/GraphQLOverWebSocket.md).\n *\n * @category Common\n */\nexport const GRAPHQL_TRANSPORT_WS_PROTOCOL = 'graphql-transport-ws';\n/**\n * The deprecated subprotocol used by [subscriptions-transport-ws](https://github.com/apollographql/subscriptions-transport-ws).\n *\n * @private\n */\nexport const DEPRECATED_GRAPHQL_WS_PROTOCOL = 'graphql-ws';\n/**\n * `graphql-ws` expected and standard close codes of the [GraphQL over WebSocket Protocol](https://github.com/graphql/graphql-over-http/blob/main/rfcs/GraphQLOverWebSocket.md).\n *\n * @category Common\n */\nexport var CloseCode;\n(function (CloseCode) {\n    CloseCode[CloseCode[\"InternalServerError\"] = 4500] = \"InternalServerError\";\n    CloseCode[CloseCode[\"InternalClientError\"] = 4005] = \"InternalClientError\";\n    CloseCode[CloseCode[\"BadRequest\"] = 4400] = \"BadRequest\";\n    CloseCode[CloseCode[\"BadResponse\"] = 4004] = \"BadResponse\";\n    /** Tried subscribing before connect ack */\n    CloseCode[CloseCode[\"Unauthorized\"] = 4401] = \"Unauthorized\";\n    CloseCode[CloseCode[\"Forbidden\"] = 4403] = \"Forbidden\";\n    CloseCode[CloseCode[\"SubprotocolNotAcceptable\"] = 4406] = \"SubprotocolNotAcceptable\";\n    CloseCode[CloseCode[\"ConnectionInitialisationTimeout\"] = 4408] = \"ConnectionInitialisationTimeout\";\n    CloseCode[CloseCode[\"ConnectionAcknowledgementTimeout\"] = 4504] = \"ConnectionAcknowledgementTimeout\";\n    /** Subscriber distinction is very important */\n    CloseCode[CloseCode[\"SubscriberAlreadyExists\"] = 4409] = \"SubscriberAlreadyExists\";\n    CloseCode[CloseCode[\"TooManyInitialisationRequests\"] = 4429] = \"TooManyInitialisationRequests\";\n})(CloseCode || (CloseCode = {}));\n/**\n * Types of messages allowed to be sent by the client/server over the WS protocol.\n *\n * @category Common\n */\nexport var MessageType;\n(function (MessageType) {\n    MessageType[\"ConnectionInit\"] = \"connection_init\";\n    MessageType[\"ConnectionAck\"] = \"connection_ack\";\n    MessageType[\"Ping\"] = \"ping\";\n    MessageType[\"Pong\"] = \"pong\";\n    MessageType[\"Subscribe\"] = \"subscribe\";\n    MessageType[\"Next\"] = \"next\";\n    MessageType[\"Error\"] = \"error\";\n    MessageType[\"Complete\"] = \"complete\";\n})(MessageType || (MessageType = {}));\n/**\n * Validates the message against the GraphQL over WebSocket Protocol.\n *\n * Invalid messages will throw descriptive errors.\n *\n * @category Common\n */\nexport function validateMessage(val) {\n    if (!isObject(val)) {\n        throw new Error(`Message is expected to be an object, but got ${extendedTypeof(val)}`);\n    }\n    if (!val.type) {\n        throw new Error(`Message is missing the 'type' property`);\n    }\n    if (typeof val.type !== 'string') {\n        throw new Error(`Message is expects the 'type' property to be a string, but got ${extendedTypeof(val.type)}`);\n    }\n    switch (val.type) {\n        case MessageType.ConnectionInit:\n        case MessageType.ConnectionAck:\n        case MessageType.Ping:\n        case MessageType.Pong: {\n            if (val.payload != null && !isObject(val.payload)) {\n                throw new Error(`\"${val.type}\" message expects the 'payload' property to be an object or nullish or missing, but got \"${val.payload}\"`);\n            }\n            break;\n        }\n        case MessageType.Subscribe: {\n            if (typeof val.id !== 'string') {\n                throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${extendedTypeof(val.id)}`);\n            }\n            if (!val.id) {\n                throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n            }\n            if (!isObject(val.payload)) {\n                throw new Error(`\"${val.type}\" message expects the 'payload' property to be an object, but got ${extendedTypeof(val.payload)}`);\n            }\n            if (typeof val.payload.query !== 'string') {\n                throw new Error(`\"${val.type}\" message payload expects the 'query' property to be a string, but got ${extendedTypeof(val.payload.query)}`);\n            }\n            if (val.payload.variables != null && !isObject(val.payload.variables)) {\n                throw new Error(`\"${val.type}\" message payload expects the 'variables' property to be a an object or nullish or missing, but got ${extendedTypeof(val.payload.variables)}`);\n            }\n            if (val.payload.operationName != null &&\n                extendedTypeof(val.payload.operationName) !== 'string') {\n                throw new Error(`\"${val.type}\" message payload expects the 'operationName' property to be a string or nullish or missing, but got ${extendedTypeof(val.payload.operationName)}`);\n            }\n            if (val.payload.extensions != null && !isObject(val.payload.extensions)) {\n                throw new Error(`\"${val.type}\" message payload expects the 'extensions' property to be a an object or nullish or missing, but got ${extendedTypeof(val.payload.extensions)}`);\n            }\n            break;\n        }\n        case MessageType.Next: {\n            if (typeof val.id !== 'string') {\n                throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${extendedTypeof(val.id)}`);\n            }\n            if (!val.id) {\n                throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n            }\n            if (!isObject(val.payload)) {\n                throw new Error(`\"${val.type}\" message expects the 'payload' property to be an object, but got ${extendedTypeof(val.payload)}`);\n            }\n            break;\n        }\n        case MessageType.Error: {\n            if (typeof val.id !== 'string') {\n                throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${extendedTypeof(val.id)}`);\n            }\n            if (!val.id) {\n                throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n            }\n            if (!areGraphQLErrors(val.payload)) {\n                throw new Error(`\"${val.type}\" message expects the 'payload' property to be an array of GraphQL errors, but got ${JSON.stringify(val.payload)}`);\n            }\n            break;\n        }\n        case MessageType.Complete: {\n            if (typeof val.id !== 'string') {\n                throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${extendedTypeof(val.id)}`);\n            }\n            if (!val.id) {\n                throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n            }\n            break;\n        }\n        default:\n            throw new Error(`Invalid message 'type' property \"${val.type}\"`);\n    }\n    return val;\n}\n/**\n * Checks if the provided value is a valid GraphQL over WebSocket message.\n *\n * @deprecated Use `validateMessage` instead.\n *\n * @category Common\n */\nexport function isMessage(val) {\n    try {\n        validateMessage(val);\n        return true;\n    }\n    catch (_a) {\n        return false;\n    }\n}\n/**\n * Parses the raw websocket message data to a valid message.\n *\n * @category Common\n */\nexport function parseMessage(data, reviver) {\n    return validateMessage(typeof data === 'string' ? JSON.parse(data, reviver) : data);\n}\n/**\n * Stringifies a valid message ready to be sent through the socket.\n *\n * @category Common\n */\nexport function stringifyMessage(msg, replacer) {\n    validateMessage(msg);\n    return JSON.stringify(msg, replacer);\n}\n", "/**\n *\n * client\n *\n */\nvar __await = (this && this.__await) || function (v) { return this instanceof __await ? (this.v = v, this) : new __await(v); }\nvar __asyncGenerator = (this && this.__asyncGenerator) || function (thisArg, _arguments, generator) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\n    return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n    function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n    function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n    function fulfill(value) { resume(\"next\", value); }\n    function reject(value) { resume(\"throw\", value); }\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n};\nimport { CloseCode, GRAPHQL_TRANSPORT_WS_PROTOCOL, MessageType, parseMessage, stringifyMessage, } from './common.mjs';\nimport { isObject, limitCloseReason } from './utils.mjs';\n/** This file is the entry point for browsers, re-export common elements. */\nexport * from './common.mjs';\n/**\n * Creates a disposable GraphQL over WebSocket client.\n *\n * @category Client\n */\nexport function createClient(options) {\n    const { url, connectionParams, lazy = true, onNonLazyError = console.error, lazyCloseTimeout: lazyCloseTimeoutMs = 0, keepAlive = 0, disablePong, connectionAckWaitTimeout = 0, retryAttempts = 5, retryWait = async function randomisedExponentialBackoff(retries) {\n        let retryDelay = 1000; // start with 1s delay\n        for (let i = 0; i < retries; i++) {\n            retryDelay *= 2;\n        }\n        await new Promise((resolve) => setTimeout(resolve, retryDelay +\n            // add random timeout from 300ms to 3s\n            Math.floor(Math.random() * (3000 - 300) + 300)));\n    }, shouldRetry = isLikeCloseEvent, isFatalConnectionProblem, on, webSocketImpl, \n    /**\n     * Generates a v4 UUID to be used as the ID using `Math`\n     * as the random number generator. Supply your own generator\n     * in case you need more uniqueness.\n     *\n     * Reference: https://gist.github.com/jed/982883\n     */\n    generateID = function generateUUID() {\n        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n            const r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n            return v.toString(16);\n        });\n    }, jsonMessageReplacer: replacer, jsonMessageReviver: reviver, } = options;\n    let ws;\n    if (webSocketImpl) {\n        if (!isWebSocket(webSocketImpl)) {\n            throw new Error('Invalid WebSocket implementation provided');\n        }\n        ws = webSocketImpl;\n    }\n    else if (typeof WebSocket !== 'undefined') {\n        ws = WebSocket;\n    }\n    else if (typeof global !== 'undefined') {\n        ws =\n            global.WebSocket ||\n                // @ts-expect-error: Support more browsers\n                global.MozWebSocket;\n    }\n    else if (typeof window !== 'undefined') {\n        ws =\n            window.WebSocket ||\n                // @ts-expect-error: Support more browsers\n                window.MozWebSocket;\n    }\n    if (!ws)\n        throw new Error(\"WebSocket implementation missing; on Node you can `import WebSocket from 'ws';` and pass `webSocketImpl: WebSocket` to `createClient`\");\n    const WebSocketImpl = ws;\n    // websocket status emitter, subscriptions are handled differently\n    const emitter = (() => {\n        const message = (() => {\n            const listeners = {};\n            return {\n                on(id, listener) {\n                    listeners[id] = listener;\n                    return () => {\n                        delete listeners[id];\n                    };\n                },\n                emit(message) {\n                    var _a;\n                    if ('id' in message)\n                        (_a = listeners[message.id]) === null || _a === void 0 ? void 0 : _a.call(listeners, message);\n                },\n            };\n        })();\n        const listeners = {\n            connecting: (on === null || on === void 0 ? void 0 : on.connecting) ? [on.connecting] : [],\n            opened: (on === null || on === void 0 ? void 0 : on.opened) ? [on.opened] : [],\n            connected: (on === null || on === void 0 ? void 0 : on.connected) ? [on.connected] : [],\n            ping: (on === null || on === void 0 ? void 0 : on.ping) ? [on.ping] : [],\n            pong: (on === null || on === void 0 ? void 0 : on.pong) ? [on.pong] : [],\n            message: (on === null || on === void 0 ? void 0 : on.message) ? [message.emit, on.message] : [message.emit],\n            closed: (on === null || on === void 0 ? void 0 : on.closed) ? [on.closed] : [],\n            error: (on === null || on === void 0 ? void 0 : on.error) ? [on.error] : [],\n        };\n        return {\n            onMessage: message.on,\n            on(event, listener) {\n                const l = listeners[event];\n                l.push(listener);\n                return () => {\n                    l.splice(l.indexOf(listener), 1);\n                };\n            },\n            emit(event, ...args) {\n                // we copy the listeners so that unlistens dont \"pull the rug under our feet\"\n                for (const listener of [...listeners[event]]) {\n                    // @ts-expect-error: The args should fit\n                    listener(...args);\n                }\n            },\n        };\n    })();\n    // invokes the callback either when an error or closed event is emitted,\n    // first one that gets called prevails, other emissions are ignored\n    function errorOrClosed(cb) {\n        const listening = [\n            // errors are fatal and more critical than close events, throw them first\n            emitter.on('error', (err) => {\n                listening.forEach((unlisten) => unlisten());\n                cb(err);\n            }),\n            // closes can be graceful and not fatal, throw them second (if error didnt throw)\n            emitter.on('closed', (event) => {\n                listening.forEach((unlisten) => unlisten());\n                cb(event);\n            }),\n        ];\n    }\n    let connecting, locks = 0, lazyCloseTimeout, retrying = false, retries = 0, disposed = false;\n    async function connect() {\n        // clear the lazy close timeout immediatelly so that close gets debounced\n        // see: https://github.com/enisdenjo/graphql-ws/issues/388\n        clearTimeout(lazyCloseTimeout);\n        const [socket, throwOnClose] = await (connecting !== null && connecting !== void 0 ? connecting : (connecting = new Promise((connected, denied) => (async () => {\n            if (retrying) {\n                await retryWait(retries);\n                // subscriptions might complete while waiting for retry\n                if (!locks) {\n                    connecting = undefined;\n                    return denied({ code: 1000, reason: 'All Subscriptions Gone' });\n                }\n                retries++;\n            }\n            emitter.emit('connecting', retrying);\n            const socket = new WebSocketImpl(typeof url === 'function' ? await url() : url, GRAPHQL_TRANSPORT_WS_PROTOCOL);\n            let connectionAckTimeout, queuedPing;\n            function enqueuePing() {\n                if (isFinite(keepAlive) && keepAlive > 0) {\n                    clearTimeout(queuedPing); // in case where a pong was received before a ping (this is valid behaviour)\n                    queuedPing = setTimeout(() => {\n                        if (socket.readyState === WebSocketImpl.OPEN) {\n                            socket.send(stringifyMessage({ type: MessageType.Ping }));\n                            emitter.emit('ping', false, undefined);\n                        }\n                    }, keepAlive);\n                }\n            }\n            errorOrClosed((errOrEvent) => {\n                connecting = undefined;\n                clearTimeout(connectionAckTimeout);\n                clearTimeout(queuedPing);\n                denied(errOrEvent);\n                if (errOrEvent instanceof TerminatedCloseEvent) {\n                    socket.close(4499, 'Terminated'); // close event is artificial and emitted manually, see `Client.terminate()` below\n                    socket.onerror = null;\n                    socket.onclose = null;\n                }\n            });\n            socket.onerror = (err) => emitter.emit('error', err);\n            socket.onclose = (event) => emitter.emit('closed', event);\n            socket.onopen = async () => {\n                try {\n                    emitter.emit('opened', socket);\n                    const payload = typeof connectionParams === 'function'\n                        ? await connectionParams()\n                        : connectionParams;\n                    // connectionParams might take too long causing the server to kick off the client\n                    // the necessary error/close event is already reported - simply stop execution\n                    if (socket.readyState !== WebSocketImpl.OPEN)\n                        return;\n                    socket.send(stringifyMessage(payload\n                        ? {\n                            type: MessageType.ConnectionInit,\n                            payload,\n                        }\n                        : {\n                            type: MessageType.ConnectionInit,\n                            // payload is completely absent if not provided\n                        }, replacer));\n                    if (isFinite(connectionAckWaitTimeout) &&\n                        connectionAckWaitTimeout > 0) {\n                        connectionAckTimeout = setTimeout(() => {\n                            socket.close(CloseCode.ConnectionAcknowledgementTimeout, 'Connection acknowledgement timeout');\n                        }, connectionAckWaitTimeout);\n                    }\n                    enqueuePing(); // enqueue ping (noop if disabled)\n                }\n                catch (err) {\n                    emitter.emit('error', err);\n                    socket.close(CloseCode.InternalClientError, limitCloseReason(err instanceof Error ? err.message : new Error(err).message, 'Internal client error'));\n                }\n            };\n            let acknowledged = false;\n            socket.onmessage = ({ data }) => {\n                try {\n                    const message = parseMessage(data, reviver);\n                    emitter.emit('message', message);\n                    if (message.type === 'ping' || message.type === 'pong') {\n                        emitter.emit(message.type, true, message.payload); // received\n                        if (message.type === 'pong') {\n                            enqueuePing(); // enqueue next ping (noop if disabled)\n                        }\n                        else if (!disablePong) {\n                            // respond with pong on ping\n                            socket.send(stringifyMessage(message.payload\n                                ? {\n                                    type: MessageType.Pong,\n                                    payload: message.payload,\n                                }\n                                : {\n                                    type: MessageType.Pong,\n                                    // payload is completely absent if not provided\n                                }));\n                            emitter.emit('pong', false, message.payload);\n                        }\n                        return; // ping and pongs can be received whenever\n                    }\n                    if (acknowledged)\n                        return; // already connected and acknowledged\n                    if (message.type !== MessageType.ConnectionAck)\n                        throw new Error(`First message cannot be of type ${message.type}`);\n                    clearTimeout(connectionAckTimeout);\n                    acknowledged = true;\n                    emitter.emit('connected', socket, message.payload, retrying); // connected = socket opened + acknowledged\n                    retrying = false; // future lazy connects are not retries\n                    retries = 0; // reset the retries on connect\n                    connected([\n                        socket,\n                        new Promise((_, reject) => errorOrClosed(reject)),\n                    ]);\n                }\n                catch (err) {\n                    socket.onmessage = null; // stop reading messages as soon as reading breaks once\n                    emitter.emit('error', err);\n                    socket.close(CloseCode.BadResponse, limitCloseReason(err instanceof Error ? err.message : new Error(err).message, 'Bad response'));\n                }\n            };\n        })())));\n        // if the provided socket is in a closing state, wait for the throw on close\n        if (socket.readyState === WebSocketImpl.CLOSING)\n            await throwOnClose;\n        let release = () => {\n            // releases this connection\n        };\n        const released = new Promise((resolve) => (release = resolve));\n        return [\n            socket,\n            release,\n            Promise.race([\n                // wait for\n                released.then(() => {\n                    if (!locks) {\n                        // and if no more locks are present, complete the connection\n                        const complete = () => socket.close(1000, 'Normal Closure');\n                        if (isFinite(lazyCloseTimeoutMs) && lazyCloseTimeoutMs > 0) {\n                            // if the keepalive is set, allow for the specified calmdown time and\n                            // then complete if the socket is still open.\n                            lazyCloseTimeout = setTimeout(() => {\n                                if (socket.readyState === WebSocketImpl.OPEN)\n                                    complete();\n                            }, lazyCloseTimeoutMs);\n                        }\n                        else {\n                            // otherwise complete immediately\n                            complete();\n                        }\n                    }\n                }),\n                // or\n                throwOnClose,\n            ]),\n        ];\n    }\n    /**\n     * Checks the `connect` problem and evaluates if the client should retry.\n     */\n    function shouldRetryConnectOrThrow(errOrCloseEvent) {\n        // some close codes are worth reporting immediately\n        if (isLikeCloseEvent(errOrCloseEvent) &&\n            (isFatalInternalCloseCode(errOrCloseEvent.code) ||\n                [\n                    CloseCode.InternalServerError,\n                    CloseCode.InternalClientError,\n                    CloseCode.BadRequest,\n                    CloseCode.BadResponse,\n                    CloseCode.Unauthorized,\n                    // CloseCode.Forbidden, might grant access out after retry\n                    CloseCode.SubprotocolNotAcceptable,\n                    // CloseCode.ConnectionInitialisationTimeout, might not time out after retry\n                    // CloseCode.ConnectionAcknowledgementTimeout, might not time out after retry\n                    CloseCode.SubscriberAlreadyExists,\n                    CloseCode.TooManyInitialisationRequests,\n                    // 4499, // Terminated, probably because the socket froze, we want to retry\n                ].includes(errOrCloseEvent.code)))\n            throw errOrCloseEvent;\n        // client was disposed, no retries should proceed regardless\n        if (disposed)\n            return false;\n        // normal closure (possibly all subscriptions have completed)\n        // if no locks were acquired in the meantime, shouldnt try again\n        if (isLikeCloseEvent(errOrCloseEvent) && errOrCloseEvent.code === 1000)\n            return locks > 0;\n        // retries are not allowed or we tried to many times, report error\n        if (!retryAttempts || retries >= retryAttempts)\n            throw errOrCloseEvent;\n        // throw non-retryable connection problems\n        if (!shouldRetry(errOrCloseEvent))\n            throw errOrCloseEvent;\n        // @deprecated throw fatal connection problems immediately\n        if (isFatalConnectionProblem === null || isFatalConnectionProblem === void 0 ? void 0 : isFatalConnectionProblem(errOrCloseEvent))\n            throw errOrCloseEvent;\n        // looks good, start retrying\n        return (retrying = true);\n    }\n    // in non-lazy (hot?) mode always hold one connection lock to persist the socket\n    if (!lazy) {\n        (async () => {\n            locks++;\n            for (;;) {\n                try {\n                    const [, , throwOnClose] = await connect();\n                    await throwOnClose; // will always throw because releaser is not used\n                }\n                catch (errOrCloseEvent) {\n                    try {\n                        if (!shouldRetryConnectOrThrow(errOrCloseEvent))\n                            return;\n                    }\n                    catch (errOrCloseEvent) {\n                        // report thrown error, no further retries\n                        return onNonLazyError === null || onNonLazyError === void 0 ? void 0 : onNonLazyError(errOrCloseEvent);\n                    }\n                }\n            }\n        })();\n    }\n    function subscribe(payload, sink) {\n        const id = generateID(payload);\n        let done = false, errored = false, releaser = () => {\n            // for handling completions before connect\n            locks--;\n            done = true;\n        };\n        (async () => {\n            locks++;\n            for (;;) {\n                try {\n                    const [socket, release, waitForReleaseOrThrowOnClose] = await connect();\n                    // if done while waiting for connect, release the connection lock right away\n                    if (done)\n                        return release();\n                    const unlisten = emitter.onMessage(id, (message) => {\n                        switch (message.type) {\n                            case MessageType.Next: {\n                                // eslint-disable-next-line @typescript-eslint/no-explicit-any -- payload will fit type\n                                sink.next(message.payload);\n                                return;\n                            }\n                            case MessageType.Error: {\n                                (errored = true), (done = true);\n                                sink.error(message.payload);\n                                releaser();\n                                return;\n                            }\n                            case MessageType.Complete: {\n                                done = true;\n                                releaser(); // release completes the sink\n                                return;\n                            }\n                        }\n                    });\n                    socket.send(stringifyMessage({\n                        id,\n                        type: MessageType.Subscribe,\n                        payload,\n                    }, replacer));\n                    releaser = () => {\n                        if (!done && socket.readyState === WebSocketImpl.OPEN)\n                            // if not completed already and socket is open, send complete message to server on release\n                            socket.send(stringifyMessage({\n                                id,\n                                type: MessageType.Complete,\n                            }, replacer));\n                        locks--;\n                        done = true;\n                        release();\n                    };\n                    // either the releaser will be called, connection completed and\n                    // the promise resolved or the socket closed and the promise rejected.\n                    // whatever happens though, we want to stop listening for messages\n                    await waitForReleaseOrThrowOnClose.finally(unlisten);\n                    return; // completed, shouldnt try again\n                }\n                catch (errOrCloseEvent) {\n                    if (!shouldRetryConnectOrThrow(errOrCloseEvent))\n                        return;\n                }\n            }\n        })()\n            .then(() => {\n            // delivering either an error or a complete terminates the sequence\n            if (!errored)\n                sink.complete();\n        }) // resolves on release or normal closure\n            .catch((err) => {\n            sink.error(err);\n        }); // rejects on close events and errors\n        return () => {\n            // dispose only of active subscriptions\n            if (!done)\n                releaser();\n        };\n    }\n    return {\n        on: emitter.on,\n        subscribe,\n        iterate(request) {\n            const pending = [];\n            const deferred = {\n                done: false,\n                error: null,\n                resolve: () => {\n                    // noop\n                },\n            };\n            const dispose = subscribe(request, {\n                next(val) {\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- payload will fit type\n                    pending.push(val);\n                    deferred.resolve();\n                },\n                error(err) {\n                    deferred.done = true;\n                    deferred.error = err;\n                    deferred.resolve();\n                },\n                complete() {\n                    deferred.done = true;\n                    deferred.resolve();\n                },\n            });\n            const iterator = (function iterator() {\n                return __asyncGenerator(this, arguments, function* iterator_1() {\n                    for (;;) {\n                        if (!pending.length) {\n                            // only wait if there are no pending messages available\n                            yield __await(new Promise((resolve) => (deferred.resolve = resolve)));\n                        }\n                        // first flush\n                        while (pending.length) {\n                            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                            yield yield __await(pending.shift());\n                        }\n                        // then error\n                        if (deferred.error) {\n                            throw deferred.error;\n                        }\n                        // or complete\n                        if (deferred.done) {\n                            return yield __await(void 0);\n                        }\n                    }\n                });\n            })();\n            iterator.throw = async (err) => {\n                if (!deferred.done) {\n                    deferred.done = true;\n                    deferred.error = err;\n                    deferred.resolve();\n                }\n                return { done: true, value: undefined };\n            };\n            iterator.return = async () => {\n                dispose();\n                return { done: true, value: undefined };\n            };\n            return iterator;\n        },\n        async dispose() {\n            disposed = true;\n            if (connecting) {\n                // if there is a connection, close it\n                const [socket] = await connecting;\n                socket.close(1000, 'Normal Closure');\n            }\n        },\n        terminate() {\n            if (connecting) {\n                // only if there is a connection\n                emitter.emit('closed', new TerminatedCloseEvent());\n            }\n        },\n    };\n}\n/**\n * A synthetic close event `4499: Terminated` is issued to the current to immediately\n * close the connection without waiting for the one coming from `WebSocket.onclose`.\n *\n * Terminating is not considered fatal and a connection retry will occur as expected.\n *\n * Useful in cases where the WebSocket is stuck and not emitting any events;\n * can happen on iOS Safari, see: https://github.com/enisdenjo/graphql-ws/discussions/290.\n */\nexport class TerminatedCloseEvent extends Error {\n    constructor() {\n        super(...arguments);\n        this.name = 'TerminatedCloseEvent';\n        this.message = '4499: Terminated';\n        this.code = 4499;\n        this.reason = 'Terminated';\n        this.wasClean = false;\n    }\n}\nfunction isLikeCloseEvent(val) {\n    return isObject(val) && 'code' in val && 'reason' in val;\n}\nfunction isFatalInternalCloseCode(code) {\n    if ([\n        1000, // Normal Closure is not an erroneous close code\n        1001, // Going Away\n        1006, // Abnormal Closure\n        1005, // No Status Received\n        1012, // Service Restart\n        1013, // Try Again Later\n        1014, // Bad Gateway\n    ].includes(code))\n        return false;\n    // all other internal errors are fatal\n    return code >= 1000 && code <= 1999;\n}\nfunction isWebSocket(val) {\n    return (typeof val === 'function' &&\n        'constructor' in val &&\n        'CLOSED' in val &&\n        'CLOSING' in val &&\n        'CONNECTING' in val &&\n        'OPEN' in val);\n}\n", "/**\n *\n * server\n *\n */\nvar __asyncValues = (this && this.__asyncValues) || function (o) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var m = o[Symbol.asyncIterator], i;\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n};\nimport { getOperationAST, GraphQLError, execute as graphqlExecute, subscribe as graphqlSubscribe, validate as graphqlValidate, parse, } from 'graphql';\nimport { CloseCode, GRAPHQL_TRANSPORT_WS_PROTOCOL, MessageType, parseMessage, stringifyMessage, } from './common.mjs';\nimport { areGraphQLErrors, isAsyncGenerator, isAsyncIterable, isObject, } from './utils.mjs';\n/**\n * Makes a Protocol compliant WebSocket GraphQL server. The server\n * is actually an API which is to be used with your favourite WebSocket\n * server library!\n *\n * Read more about the [GraphQL over WebSocket Protocol](https://github.com/graphql/graphql-over-http/blob/main/rfcs/GraphQLOverWebSocket.md).\n *\n * @category Server\n */\nexport function makeServer(options) {\n    const { schema, context, roots, validate, execute, subscribe, connectionInitWaitTimeout = 3000, // 3 seconds\n    onConnect, onDisconnect, onClose, onSubscribe, onOperation, onNext, onError, onComplete, jsonMessageReviver: reviver, jsonMessageReplacer: replacer, } = options;\n    return {\n        opened(socket, extra) {\n            const ctx = {\n                connectionInitReceived: false,\n                acknowledged: false,\n                subscriptions: {},\n                extra,\n            };\n            if (socket.protocol !== GRAPHQL_TRANSPORT_WS_PROTOCOL) {\n                socket.close(CloseCode.SubprotocolNotAcceptable, 'Subprotocol not acceptable');\n                return async (code, reason) => {\n                    /* nothing was set up, just notify the closure */\n                    await (onClose === null || onClose === void 0 ? void 0 : onClose(ctx, code, reason));\n                };\n            }\n            // kick the client off (close socket) if the connection has\n            // not been initialised after the specified wait timeout\n            const connectionInitWait = connectionInitWaitTimeout > 0 && isFinite(connectionInitWaitTimeout)\n                ? setTimeout(() => {\n                    if (!ctx.connectionInitReceived)\n                        socket.close(CloseCode.ConnectionInitialisationTimeout, 'Connection initialisation timeout');\n                }, connectionInitWaitTimeout)\n                : null;\n            socket.onMessage(async function onMessage(data) {\n                var _a, e_1, _b, _c;\n                var _d;\n                let message;\n                try {\n                    message = parseMessage(data, reviver);\n                }\n                catch (err) {\n                    return socket.close(CloseCode.BadRequest, 'Invalid message received');\n                }\n                switch (message.type) {\n                    case MessageType.ConnectionInit: {\n                        if (ctx.connectionInitReceived)\n                            return socket.close(CloseCode.TooManyInitialisationRequests, 'Too many initialisation requests');\n                        // @ts-expect-error: I can write\n                        ctx.connectionInitReceived = true;\n                        if (isObject(message.payload))\n                            // @ts-expect-error: I can write\n                            ctx.connectionParams = message.payload;\n                        const permittedOrPayload = await (onConnect === null || onConnect === void 0 ? void 0 : onConnect(ctx));\n                        if (permittedOrPayload === false)\n                            return socket.close(CloseCode.Forbidden, 'Forbidden');\n                        // we should acknowledge before send to avoid race conditions (like those exampled in https://github.com/enisdenjo/graphql-ws/issues/501)\n                        // even if the send fails/throws, the connection should be closed because its malfunctioning\n                        // @ts-expect-error: I can write\n                        ctx.acknowledged = true;\n                        await socket.send(stringifyMessage(isObject(permittedOrPayload)\n                            ? {\n                                type: MessageType.ConnectionAck,\n                                payload: permittedOrPayload,\n                            }\n                            : {\n                                type: MessageType.ConnectionAck,\n                                // payload is completely absent if not provided\n                            }, replacer));\n                        return;\n                    }\n                    case MessageType.Ping: {\n                        if (socket.onPing)\n                            // if the onPing listener is registered, automatic pong is disabled\n                            return await socket.onPing(message.payload);\n                        await socket.send(stringifyMessage(message.payload\n                            ? { type: MessageType.Pong, payload: message.payload }\n                            : {\n                                type: MessageType.Pong,\n                                // payload is completely absent if not provided\n                            }));\n                        return;\n                    }\n                    case MessageType.Pong:\n                        return await ((_d = socket.onPong) === null || _d === void 0 ? void 0 : _d.call(socket, message.payload));\n                    case MessageType.Subscribe: {\n                        if (!ctx.acknowledged)\n                            return socket.close(CloseCode.Unauthorized, 'Unauthorized');\n                        const { id, payload } = message;\n                        if (id in ctx.subscriptions)\n                            return socket.close(CloseCode.SubscriberAlreadyExists, `Subscriber for ${id} already exists`);\n                        // if this turns out to be a streaming operation, the subscription value\n                        // will change to an `AsyncIterable`, otherwise it will stay as is\n                        ctx.subscriptions[id] = null;\n                        const emit = {\n                            next: async (result, args) => {\n                                let nextMessage = {\n                                    id,\n                                    type: MessageType.Next,\n                                    payload: result,\n                                };\n                                const maybeResult = await (onNext === null || onNext === void 0 ? void 0 : onNext(ctx, nextMessage, args, result));\n                                if (maybeResult)\n                                    nextMessage = Object.assign(Object.assign({}, nextMessage), { payload: maybeResult });\n                                await socket.send(stringifyMessage(nextMessage, replacer));\n                            },\n                            error: async (errors) => {\n                                let errorMessage = {\n                                    id,\n                                    type: MessageType.Error,\n                                    payload: errors,\n                                };\n                                const maybeErrors = await (onError === null || onError === void 0 ? void 0 : onError(ctx, errorMessage, errors));\n                                if (maybeErrors)\n                                    errorMessage = Object.assign(Object.assign({}, errorMessage), { payload: maybeErrors });\n                                await socket.send(stringifyMessage(errorMessage, replacer));\n                            },\n                            complete: async (notifyClient) => {\n                                const completeMessage = {\n                                    id,\n                                    type: MessageType.Complete,\n                                };\n                                await (onComplete === null || onComplete === void 0 ? void 0 : onComplete(ctx, completeMessage));\n                                if (notifyClient)\n                                    await socket.send(stringifyMessage(completeMessage, replacer));\n                            },\n                        };\n                        try {\n                            let execArgs;\n                            const maybeExecArgsOrErrors = await (onSubscribe === null || onSubscribe === void 0 ? void 0 : onSubscribe(ctx, message));\n                            if (maybeExecArgsOrErrors) {\n                                if (areGraphQLErrors(maybeExecArgsOrErrors))\n                                    return id in ctx.subscriptions\n                                        ? await emit.error(maybeExecArgsOrErrors)\n                                        : void 0;\n                                else if (Array.isArray(maybeExecArgsOrErrors))\n                                    throw new Error('Invalid return value from onSubscribe hook, expected an array of GraphQLError objects');\n                                // not errors, is exec args\n                                execArgs = maybeExecArgsOrErrors;\n                            }\n                            else {\n                                // you either provide a schema dynamically through\n                                // `onSubscribe` or you set one up during the server setup\n                                if (!schema)\n                                    throw new Error('The GraphQL schema is not provided');\n                                const args = {\n                                    operationName: payload.operationName,\n                                    document: parse(payload.query),\n                                    variableValues: payload.variables,\n                                };\n                                execArgs = Object.assign(Object.assign({}, args), { schema: typeof schema === 'function'\n                                        ? await schema(ctx, message, args)\n                                        : schema });\n                                const validationErrors = (validate !== null && validate !== void 0 ? validate : graphqlValidate)(execArgs.schema, execArgs.document);\n                                if (validationErrors.length > 0)\n                                    return id in ctx.subscriptions\n                                        ? await emit.error(validationErrors)\n                                        : void 0;\n                            }\n                            const operationAST = getOperationAST(execArgs.document, execArgs.operationName);\n                            if (!operationAST)\n                                return id in ctx.subscriptions\n                                    ? await emit.error([\n                                        new GraphQLError('Unable to identify operation'),\n                                    ])\n                                    : void 0;\n                            // if `onSubscribe` didn't specify a rootValue, inject one\n                            if (!('rootValue' in execArgs))\n                                execArgs.rootValue = roots === null || roots === void 0 ? void 0 : roots[operationAST.operation];\n                            // if `onSubscribe` didn't specify a context, inject one\n                            if (!('contextValue' in execArgs))\n                                execArgs.contextValue =\n                                    typeof context === 'function'\n                                        ? await context(ctx, message, execArgs)\n                                        : context;\n                            // the execution arguments have been prepared\n                            // perform the operation and act accordingly\n                            let operationResult;\n                            if (operationAST.operation === 'subscription')\n                                operationResult = await (subscribe !== null && subscribe !== void 0 ? subscribe : graphqlSubscribe)(execArgs);\n                            // operation === 'query' || 'mutation'\n                            else\n                                operationResult = await (execute !== null && execute !== void 0 ? execute : graphqlExecute)(execArgs);\n                            const maybeResult = await (onOperation === null || onOperation === void 0 ? void 0 : onOperation(ctx, message, execArgs, operationResult));\n                            if (maybeResult)\n                                operationResult = maybeResult;\n                            if (isAsyncIterable(operationResult)) {\n                                /** multiple emitted results */\n                                if (!(id in ctx.subscriptions)) {\n                                    // subscription was completed/canceled before the operation settled\n                                    if (isAsyncGenerator(operationResult))\n                                        operationResult.return(undefined);\n                                }\n                                else {\n                                    ctx.subscriptions[id] = operationResult;\n                                    try {\n                                        for (var _e = true, operationResult_1 = __asyncValues(operationResult), operationResult_1_1; operationResult_1_1 = await operationResult_1.next(), _a = operationResult_1_1.done, !_a; _e = true) {\n                                            _c = operationResult_1_1.value;\n                                            _e = false;\n                                            const result = _c;\n                                            await emit.next(result, execArgs);\n                                        }\n                                    }\n                                    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                                    finally {\n                                        try {\n                                            if (!_e && !_a && (_b = operationResult_1.return)) await _b.call(operationResult_1);\n                                        }\n                                        finally { if (e_1) throw e_1.error; }\n                                    }\n                                }\n                            }\n                            else {\n                                /** single emitted result */\n                                // if the client completed the subscription before the single result\n                                // became available, he effectively canceled it and no data should be sent\n                                if (id in ctx.subscriptions)\n                                    await emit.next(operationResult, execArgs);\n                            }\n                            // lack of subscription at this point indicates that the client\n                            // completed the subscription, he doesn't need to be reminded\n                            await emit.complete(id in ctx.subscriptions);\n                        }\n                        finally {\n                            // whatever happens to the subscription, we finally want to get rid of the reservation\n                            delete ctx.subscriptions[id];\n                        }\n                        return;\n                    }\n                    case MessageType.Complete: {\n                        const subscription = ctx.subscriptions[message.id];\n                        delete ctx.subscriptions[message.id]; // deleting the subscription means no further activity should take place\n                        if (isAsyncGenerator(subscription))\n                            await subscription.return(undefined);\n                        return;\n                    }\n                    default:\n                        throw new Error(`Unexpected message of type ${message.type} received`);\n                }\n            });\n            // wait for close, cleanup and the disconnect callback\n            return async (code, reason) => {\n                if (connectionInitWait)\n                    clearTimeout(connectionInitWait);\n                const subs = Object.assign({}, ctx.subscriptions);\n                // @ts-expect-error: I can write\n                ctx.subscriptions = {}; // deleting the subscription means no further activity should take place\n                // we return all iterable subscriptions immediatelly, independant of the order\n                await Promise.all(Object.values(subs)\n                    .filter(isAsyncGenerator)\n                    .map((sub) => sub.return(undefined)));\n                if (ctx.acknowledged)\n                    await (onDisconnect === null || onDisconnect === void 0 ? void 0 : onDisconnect(ctx, code, reason));\n                await (onClose === null || onClose === void 0 ? void 0 : onClose(ctx, code, reason));\n            };\n        },\n    };\n}\n/**\n * Helper utility for choosing the \"graphql-transport-ws\" subprotocol from\n * a set of WebSocket subprotocols.\n *\n * Accepts a set of already extracted WebSocket subprotocols or the raw\n * Sec-WebSocket-Protocol header value. In either case, if the right\n * protocol appears, it will be returned.\n *\n * By specification, the server should not provide a value with Sec-WebSocket-Protocol\n * if it does not agree with client's subprotocols. The client has a responsibility\n * to handle the connection afterwards.\n *\n * @category Server\n */\nexport function handleProtocols(protocols) {\n    switch (true) {\n        case protocols instanceof Set &&\n            protocols.has(GRAPHQL_TRANSPORT_WS_PROTOCOL):\n        case Array.isArray(protocols) &&\n            protocols.includes(GRAPHQL_TRANSPORT_WS_PROTOCOL):\n        case typeof protocols === 'string' &&\n            protocols\n                .split(',')\n                .map((p) => p.trim())\n                .includes(GRAPHQL_TRANSPORT_WS_PROTOCOL):\n            return GRAPHQL_TRANSPORT_WS_PROTOCOL;\n        default:\n            return false;\n    }\n}\n"], "mappings": ";;;;;;;;;;;AACO,SAAS,eAAe,KAAK;AAChC,MAAI,QAAQ,MAAM;AACd,WAAO;AAAA,EACX;AACA,MAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,WAAO;AAAA,EACX;AACA,SAAO,OAAO;AAClB;AAEO,SAAS,SAAS,KAAK;AAC1B,SAAO,eAAe,GAAG,MAAM;AACnC;AAEO,SAAS,gBAAgB,KAAK;AACjC,SAAO,OAAO,OAAO,GAAG,EAAE,OAAO,aAAa,MAAM;AACxD;AAEO,SAAS,iBAAiB,KAAK;AAClC,SAAQ,SAAS,GAAG,KAChB,OAAO,OAAO,GAAG,EAAE,OAAO,aAAa,MAAM,cAC7C,OAAO,IAAI,WAAW;AAK9B;AAEO,SAAS,iBAAiB,KAAK;AAClC,SAAQ,MAAM,QAAQ,GAAG;AAAA,EAErB,IAAI,SAAS;AAAA,EAEb,IAAI,MAAM,CAAC,OAAO,aAAa,EAAE;AACzC;AAOO,SAAS,iBAAiB,QAAQ,aAAa;AAClD,SAAO,OAAO,SAAS,MAAM,SAAS;AAC1C;;;ACjCO,IAAM,gCAAgC;AAMtC,IAAM,iCAAiC;AAMvC,IAAI;AAAA,CACV,SAAUA,YAAW;AAClB,EAAAA,WAAUA,WAAU,qBAAqB,IAAI,IAAI,IAAI;AACrD,EAAAA,WAAUA,WAAU,qBAAqB,IAAI,IAAI,IAAI;AACrD,EAAAA,WAAUA,WAAU,YAAY,IAAI,IAAI,IAAI;AAC5C,EAAAA,WAAUA,WAAU,aAAa,IAAI,IAAI,IAAI;AAE7C,EAAAA,WAAUA,WAAU,cAAc,IAAI,IAAI,IAAI;AAC9C,EAAAA,WAAUA,WAAU,WAAW,IAAI,IAAI,IAAI;AAC3C,EAAAA,WAAUA,WAAU,0BAA0B,IAAI,IAAI,IAAI;AAC1D,EAAAA,WAAUA,WAAU,iCAAiC,IAAI,IAAI,IAAI;AACjE,EAAAA,WAAUA,WAAU,kCAAkC,IAAI,IAAI,IAAI;AAElE,EAAAA,WAAUA,WAAU,yBAAyB,IAAI,IAAI,IAAI;AACzD,EAAAA,WAAUA,WAAU,+BAA+B,IAAI,IAAI,IAAI;AACnE,GAAG,cAAc,YAAY,CAAC,EAAE;AAMzB,IAAI;AAAA,CACV,SAAUC,cAAa;AACpB,EAAAA,aAAY,gBAAgB,IAAI;AAChC,EAAAA,aAAY,eAAe,IAAI;AAC/B,EAAAA,aAAY,MAAM,IAAI;AACtB,EAAAA,aAAY,MAAM,IAAI;AACtB,EAAAA,aAAY,WAAW,IAAI;AAC3B,EAAAA,aAAY,MAAM,IAAI;AACtB,EAAAA,aAAY,OAAO,IAAI;AACvB,EAAAA,aAAY,UAAU,IAAI;AAC9B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAQ7B,SAAS,gBAAgB,KAAK;AACjC,MAAI,CAAC,SAAS,GAAG,GAAG;AAChB,UAAM,IAAI,MAAM,gDAAgD,eAAe,GAAG,CAAC,EAAE;AAAA,EACzF;AACA,MAAI,CAAC,IAAI,MAAM;AACX,UAAM,IAAI,MAAM,wCAAwC;AAAA,EAC5D;AACA,MAAI,OAAO,IAAI,SAAS,UAAU;AAC9B,UAAM,IAAI,MAAM,kEAAkE,eAAe,IAAI,IAAI,CAAC,EAAE;AAAA,EAChH;AACA,UAAQ,IAAI,MAAM;AAAA,IACd,KAAK,YAAY;AAAA,IACjB,KAAK,YAAY;AAAA,IACjB,KAAK,YAAY;AAAA,IACjB,KAAK,YAAY,MAAM;AACnB,UAAI,IAAI,WAAW,QAAQ,CAAC,SAAS,IAAI,OAAO,GAAG;AAC/C,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,4FAA4F,IAAI,OAAO,GAAG;AAAA,MAC1I;AACA;AAAA,IACJ;AAAA,IACA,KAAK,YAAY,WAAW;AACxB,UAAI,OAAO,IAAI,OAAO,UAAU;AAC5B,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,+DAA+D,eAAe,IAAI,EAAE,CAAC,EAAE;AAAA,MACvH;AACA,UAAI,CAAC,IAAI,IAAI;AACT,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,8CAA8C;AAAA,MAC9E;AACA,UAAI,CAAC,SAAS,IAAI,OAAO,GAAG;AACxB,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,qEAAqE,eAAe,IAAI,OAAO,CAAC,EAAE;AAAA,MAClI;AACA,UAAI,OAAO,IAAI,QAAQ,UAAU,UAAU;AACvC,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,0EAA0E,eAAe,IAAI,QAAQ,KAAK,CAAC,EAAE;AAAA,MAC7I;AACA,UAAI,IAAI,QAAQ,aAAa,QAAQ,CAAC,SAAS,IAAI,QAAQ,SAAS,GAAG;AACnE,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,uGAAuG,eAAe,IAAI,QAAQ,SAAS,CAAC,EAAE;AAAA,MAC9K;AACA,UAAI,IAAI,QAAQ,iBAAiB,QAC7B,eAAe,IAAI,QAAQ,aAAa,MAAM,UAAU;AACxD,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,wGAAwG,eAAe,IAAI,QAAQ,aAAa,CAAC,EAAE;AAAA,MACnL;AACA,UAAI,IAAI,QAAQ,cAAc,QAAQ,CAAC,SAAS,IAAI,QAAQ,UAAU,GAAG;AACrE,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,wGAAwG,eAAe,IAAI,QAAQ,UAAU,CAAC,EAAE;AAAA,MAChL;AACA;AAAA,IACJ;AAAA,IACA,KAAK,YAAY,MAAM;AACnB,UAAI,OAAO,IAAI,OAAO,UAAU;AAC5B,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,+DAA+D,eAAe,IAAI,EAAE,CAAC,EAAE;AAAA,MACvH;AACA,UAAI,CAAC,IAAI,IAAI;AACT,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,8CAA8C;AAAA,MAC9E;AACA,UAAI,CAAC,SAAS,IAAI,OAAO,GAAG;AACxB,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,qEAAqE,eAAe,IAAI,OAAO,CAAC,EAAE;AAAA,MAClI;AACA;AAAA,IACJ;AAAA,IACA,KAAK,YAAY,OAAO;AACpB,UAAI,OAAO,IAAI,OAAO,UAAU;AAC5B,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,+DAA+D,eAAe,IAAI,EAAE,CAAC,EAAE;AAAA,MACvH;AACA,UAAI,CAAC,IAAI,IAAI;AACT,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,8CAA8C;AAAA,MAC9E;AACA,UAAI,CAAC,iBAAiB,IAAI,OAAO,GAAG;AAChC,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,sFAAsF,KAAK,UAAU,IAAI,OAAO,CAAC,EAAE;AAAA,MACnJ;AACA;AAAA,IACJ;AAAA,IACA,KAAK,YAAY,UAAU;AACvB,UAAI,OAAO,IAAI,OAAO,UAAU;AAC5B,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,+DAA+D,eAAe,IAAI,EAAE,CAAC,EAAE;AAAA,MACvH;AACA,UAAI,CAAC,IAAI,IAAI;AACT,cAAM,IAAI,MAAM,IAAI,IAAI,IAAI,8CAA8C;AAAA,MAC9E;AACA;AAAA,IACJ;AAAA,IACA;AACI,YAAM,IAAI,MAAM,oCAAoC,IAAI,IAAI,GAAG;AAAA,EACvE;AACA,SAAO;AACX;AAQO,SAAS,UAAU,KAAK;AAC3B,MAAI;AACA,oBAAgB,GAAG;AACnB,WAAO;AAAA,EACX,SACO,IAAI;AACP,WAAO;AAAA,EACX;AACJ;AAMO,SAAS,aAAa,MAAM,SAAS;AACxC,SAAO,gBAAgB,OAAO,SAAS,WAAW,KAAK,MAAM,MAAM,OAAO,IAAI,IAAI;AACtF;AAMO,SAAS,iBAAiB,KAAK,UAAU;AAC5C,kBAAgB,GAAG;AACnB,SAAO,KAAK,UAAU,KAAK,QAAQ;AACvC;;;AC5KA,IAAI,UAAoC,SAAU,GAAG;AAAE,SAAO,gBAAgB,WAAW,KAAK,IAAI,GAAG,QAAQ,IAAI,QAAQ,CAAC;AAAG;AAC7H,IAAI,mBAAsD,SAAU,SAAS,YAAY,WAAW;AAChG,MAAI,CAAC,OAAO;AAAe,UAAM,IAAI,UAAU,sCAAsC;AACrF,MAAI,IAAI,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;AAC5D,SAAO,IAAI,OAAO,QAAQ,OAAO,kBAAkB,aAAa,gBAAgB,QAAQ,SAAS,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,UAAU,WAAW,GAAG,EAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AACtN,WAAS,YAAY,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,QAAQ,QAAQ,CAAC,EAAE,KAAK,GAAG,MAAM;AAAA,IAAG;AAAA,EAAG;AAC9F,WAAS,KAAK,GAAG,GAAG;AAAE,QAAI,EAAE,CAAC,GAAG;AAAE,QAAE,CAAC,IAAI,SAAU,GAAG;AAAE,eAAO,IAAI,QAAQ,SAAU,GAAG,GAAG;AAAE,YAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,OAAO,GAAG,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAAG,UAAI;AAAG,UAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IAAG;AAAA,EAAE;AACvK,WAAS,OAAO,GAAG,GAAG;AAAE,QAAI;AAAE,WAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,IAAG,SAAS,GAAG;AAAE,aAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,IAAG;AAAA,EAAE;AACjF,WAAS,KAAK,GAAG;AAAE,MAAE,iBAAiB,UAAU,QAAQ,QAAQ,EAAE,MAAM,CAAC,EAAE,KAAK,SAAS,MAAM,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,EAAG;AACvH,WAAS,QAAQ,OAAO;AAAE,WAAO,QAAQ,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,OAAO;AAAE,WAAO,SAAS,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,GAAG,GAAG;AAAE,QAAI,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE;AAAQ,aAAO,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,EAAG;AACrF;AAUO,SAAS,aAAa,SAAS;AAClC,QAAM;AAAA,IAAE;AAAA,IAAK;AAAA,IAAkB,OAAO;AAAA,IAAM,iBAAiB,QAAQ;AAAA,IAAO,kBAAkB,qBAAqB;AAAA,IAAG,YAAY;AAAA,IAAG;AAAA,IAAa,2BAA2B;AAAA,IAAG,gBAAgB;AAAA,IAAG,YAAY,eAAe,6BAA6BC,UAAS;AAChQ,UAAI,aAAa;AACjB,eAAS,IAAI,GAAG,IAAIA,UAAS,KAAK;AAC9B,sBAAc;AAAA,MAClB;AACA,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS;AAAA,MAE/C,KAAK,MAAM,KAAK,OAAO,KAAK,MAAO,OAAO,GAAG,CAAC,CAAC;AAAA,IACvD;AAAA,IAAG,cAAc;AAAA,IAAkB;AAAA,IAA0B;AAAA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQjE,aAAa,SAAS,eAAe;AACjC,aAAO,uCAAuC,QAAQ,SAAS,CAAC,MAAM;AAClE,cAAM,IAAK,KAAK,OAAO,IAAI,KAAM,GAAG,IAAI,KAAK,MAAM,IAAK,IAAI,IAAO;AACnE,eAAO,EAAE,SAAS,EAAE;AAAA,MACxB,CAAC;AAAA,IACL;AAAA,IAAG,qBAAqB;AAAA,IAAU,oBAAoB;AAAA,EAAS,IAAI;AACnE,MAAI;AACJ,MAAI,eAAe;AACf,QAAI,CAAC,YAAY,aAAa,GAAG;AAC7B,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC/D;AACA,SAAK;AAAA,EACT,WACS,OAAO,cAAc,aAAa;AACvC,SAAK;AAAA,EACT,WACS,OAAO,WAAW,aAAa;AACpC,SACI,OAAO;AAAA,IAEH,OAAO;AAAA,EACnB,WACS,OAAO,WAAW,aAAa;AACpC,SACI,OAAO;AAAA,IAEH,OAAO;AAAA,EACnB;AACA,MAAI,CAAC;AACD,UAAM,IAAI,MAAM,uIAAuI;AAC3J,QAAM,gBAAgB;AAEtB,QAAM,WAAW,MAAM;AACnB,UAAM,WAAW,MAAM;AACnB,YAAMC,aAAY,CAAC;AACnB,aAAO;AAAA,QACH,GAAG,IAAI,UAAU;AACb,UAAAA,WAAU,EAAE,IAAI;AAChB,iBAAO,MAAM;AACT,mBAAOA,WAAU,EAAE;AAAA,UACvB;AAAA,QACJ;AAAA,QACA,KAAKC,UAAS;AACV,cAAI;AACJ,cAAI,QAAQA;AACR,aAAC,KAAKD,WAAUC,SAAQ,EAAE,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAKD,YAAWC,QAAO;AAAA,QACpG;AAAA,MACJ;AAAA,IACJ,GAAG;AACH,UAAM,YAAY;AAAA,MACd,aAAa,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,CAAC,GAAG,UAAU,IAAI,CAAC;AAAA,MACzF,SAAS,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,MAC7E,YAAY,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,CAAC,GAAG,SAAS,IAAI,CAAC;AAAA,MACtF,OAAO,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,CAAC,GAAG,IAAI,IAAI,CAAC;AAAA,MACvE,OAAO,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,CAAC,GAAG,IAAI,IAAI,CAAC;AAAA,MACvE,UAAU,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,CAAC,QAAQ,MAAM,GAAG,OAAO,IAAI,CAAC,QAAQ,IAAI;AAAA,MAC1G,SAAS,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,MAC7E,QAAQ,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,CAAC,GAAG,KAAK,IAAI,CAAC;AAAA,IAC9E;AACA,WAAO;AAAA,MACH,WAAW,QAAQ;AAAA,MACnB,GAAG,OAAO,UAAU;AAChB,cAAM,IAAI,UAAU,KAAK;AACzB,UAAE,KAAK,QAAQ;AACf,eAAO,MAAM;AACT,YAAE,OAAO,EAAE,QAAQ,QAAQ,GAAG,CAAC;AAAA,QACnC;AAAA,MACJ;AAAA,MACA,KAAK,UAAU,MAAM;AAEjB,mBAAW,YAAY,CAAC,GAAG,UAAU,KAAK,CAAC,GAAG;AAE1C,mBAAS,GAAG,IAAI;AAAA,QACpB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG;AAGH,WAAS,cAAc,IAAI;AACvB,UAAM,YAAY;AAAA;AAAA,MAEd,QAAQ,GAAG,SAAS,CAAC,QAAQ;AACzB,kBAAU,QAAQ,CAAC,aAAa,SAAS,CAAC;AAC1C,WAAG,GAAG;AAAA,MACV,CAAC;AAAA;AAAA,MAED,QAAQ,GAAG,UAAU,CAAC,UAAU;AAC5B,kBAAU,QAAQ,CAAC,aAAa,SAAS,CAAC;AAC1C,WAAG,KAAK;AAAA,MACZ,CAAC;AAAA,IACL;AAAA,EACJ;AACA,MAAI,YAAY,QAAQ,GAAG,kBAAkB,WAAW,OAAO,UAAU,GAAG,WAAW;AACvF,iBAAe,UAAU;AAGrB,iBAAa,gBAAgB;AAC7B,UAAM,CAAC,QAAQ,YAAY,IAAI,OAAO,eAAe,QAAQ,eAAe,SAAS,aAAc,aAAa,IAAI,QAAQ,CAAC,WAAW,YAAY,YAAY;AAC5J,UAAI,UAAU;AACV,cAAM,UAAU,OAAO;AAEvB,YAAI,CAAC,OAAO;AACR,uBAAa;AACb,iBAAO,OAAO,EAAE,MAAM,KAAM,QAAQ,yBAAyB,CAAC;AAAA,QAClE;AACA;AAAA,MACJ;AACA,cAAQ,KAAK,cAAc,QAAQ;AACnC,YAAMC,UAAS,IAAI,cAAc,OAAO,QAAQ,aAAa,MAAM,IAAI,IAAI,KAAK,6BAA6B;AAC7G,UAAI,sBAAsB;AAC1B,eAAS,cAAc;AACnB,YAAI,SAAS,SAAS,KAAK,YAAY,GAAG;AACtC,uBAAa,UAAU;AACvB,uBAAa,WAAW,MAAM;AAC1B,gBAAIA,QAAO,eAAe,cAAc,MAAM;AAC1C,cAAAA,QAAO,KAAK,iBAAiB,EAAE,MAAM,YAAY,KAAK,CAAC,CAAC;AACxD,sBAAQ,KAAK,QAAQ,OAAO,MAAS;AAAA,YACzC;AAAA,UACJ,GAAG,SAAS;AAAA,QAChB;AAAA,MACJ;AACA,oBAAc,CAAC,eAAe;AAC1B,qBAAa;AACb,qBAAa,oBAAoB;AACjC,qBAAa,UAAU;AACvB,eAAO,UAAU;AACjB,YAAI,sBAAsB,sBAAsB;AAC5C,UAAAA,QAAO,MAAM,MAAM,YAAY;AAC/B,UAAAA,QAAO,UAAU;AACjB,UAAAA,QAAO,UAAU;AAAA,QACrB;AAAA,MACJ,CAAC;AACD,MAAAA,QAAO,UAAU,CAAC,QAAQ,QAAQ,KAAK,SAAS,GAAG;AACnD,MAAAA,QAAO,UAAU,CAAC,UAAU,QAAQ,KAAK,UAAU,KAAK;AACxD,MAAAA,QAAO,SAAS,YAAY;AACxB,YAAI;AACA,kBAAQ,KAAK,UAAUA,OAAM;AAC7B,gBAAM,UAAU,OAAO,qBAAqB,aACtC,MAAM,iBAAiB,IACvB;AAGN,cAAIA,QAAO,eAAe,cAAc;AACpC;AACJ,UAAAA,QAAO,KAAK,iBAAiB,UACvB;AAAA,YACE,MAAM,YAAY;AAAA,YAClB;AAAA,UACJ,IACE;AAAA,YACE,MAAM,YAAY;AAAA;AAAA,UAEtB,GAAG,QAAQ,CAAC;AAChB,cAAI,SAAS,wBAAwB,KACjC,2BAA2B,GAAG;AAC9B,mCAAuB,WAAW,MAAM;AACpC,cAAAA,QAAO,MAAM,UAAU,kCAAkC,oCAAoC;AAAA,YACjG,GAAG,wBAAwB;AAAA,UAC/B;AACA,sBAAY;AAAA,QAChB,SACO,KAAK;AACR,kBAAQ,KAAK,SAAS,GAAG;AACzB,UAAAA,QAAO,MAAM,UAAU,qBAAqB,iBAAiB,eAAe,QAAQ,IAAI,UAAU,IAAI,MAAM,GAAG,EAAE,SAAS,uBAAuB,CAAC;AAAA,QACtJ;AAAA,MACJ;AACA,UAAI,eAAe;AACnB,MAAAA,QAAO,YAAY,CAAC,EAAE,KAAK,MAAM;AAC7B,YAAI;AACA,gBAAM,UAAU,aAAa,MAAM,OAAO;AAC1C,kBAAQ,KAAK,WAAW,OAAO;AAC/B,cAAI,QAAQ,SAAS,UAAU,QAAQ,SAAS,QAAQ;AACpD,oBAAQ,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO;AAChD,gBAAI,QAAQ,SAAS,QAAQ;AACzB,0BAAY;AAAA,YAChB,WACS,CAAC,aAAa;AAEnB,cAAAA,QAAO,KAAK,iBAAiB,QAAQ,UAC/B;AAAA,gBACE,MAAM,YAAY;AAAA,gBAClB,SAAS,QAAQ;AAAA,cACrB,IACE;AAAA,gBACE,MAAM,YAAY;AAAA;AAAA,cAEtB,CAAC,CAAC;AACN,sBAAQ,KAAK,QAAQ,OAAO,QAAQ,OAAO;AAAA,YAC/C;AACA;AAAA,UACJ;AACA,cAAI;AACA;AACJ,cAAI,QAAQ,SAAS,YAAY;AAC7B,kBAAM,IAAI,MAAM,mCAAmC,QAAQ,IAAI,EAAE;AACrE,uBAAa,oBAAoB;AACjC,yBAAe;AACf,kBAAQ,KAAK,aAAaA,SAAQ,QAAQ,SAAS,QAAQ;AAC3D,qBAAW;AACX,oBAAU;AACV,oBAAU;AAAA,YACNA;AAAA,YACA,IAAI,QAAQ,CAAC,GAAG,WAAW,cAAc,MAAM,CAAC;AAAA,UACpD,CAAC;AAAA,QACL,SACO,KAAK;AACR,UAAAA,QAAO,YAAY;AACnB,kBAAQ,KAAK,SAAS,GAAG;AACzB,UAAAA,QAAO,MAAM,UAAU,aAAa,iBAAiB,eAAe,QAAQ,IAAI,UAAU,IAAI,MAAM,GAAG,EAAE,SAAS,cAAc,CAAC;AAAA,QACrI;AAAA,MACJ;AAAA,IACJ,GAAG,CAAC;AAEJ,QAAI,OAAO,eAAe,cAAc;AACpC,YAAM;AACV,QAAI,UAAU,MAAM;AAAA,IAEpB;AACA,UAAM,WAAW,IAAI,QAAQ,CAAC,YAAa,UAAU,OAAQ;AAC7D,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA,QAAQ,KAAK;AAAA;AAAA,QAET,SAAS,KAAK,MAAM;AAChB,cAAI,CAAC,OAAO;AAER,kBAAM,WAAW,MAAM,OAAO,MAAM,KAAM,gBAAgB;AAC1D,gBAAI,SAAS,kBAAkB,KAAK,qBAAqB,GAAG;AAGxD,iCAAmB,WAAW,MAAM;AAChC,oBAAI,OAAO,eAAe,cAAc;AACpC,2BAAS;AAAA,cACjB,GAAG,kBAAkB;AAAA,YACzB,OACK;AAED,uBAAS;AAAA,YACb;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA;AAAA,QAED;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAIA,WAAS,0BAA0B,iBAAiB;AAEhD,QAAI,iBAAiB,eAAe,MAC/B,yBAAyB,gBAAgB,IAAI,KAC1C;AAAA,MACI,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA,MAEV,UAAU;AAAA;AAAA;AAAA,MAGV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA,IAEd,EAAE,SAAS,gBAAgB,IAAI;AACnC,YAAM;AAEV,QAAI;AACA,aAAO;AAGX,QAAI,iBAAiB,eAAe,KAAK,gBAAgB,SAAS;AAC9D,aAAO,QAAQ;AAEnB,QAAI,CAAC,iBAAiB,WAAW;AAC7B,YAAM;AAEV,QAAI,CAAC,YAAY,eAAe;AAC5B,YAAM;AAEV,QAAI,6BAA6B,QAAQ,6BAA6B,SAAS,SAAS,yBAAyB,eAAe;AAC5H,YAAM;AAEV,WAAQ,WAAW;AAAA,EACvB;AAEA,MAAI,CAAC,MAAM;AACP,KAAC,YAAY;AACT;AACA,iBAAS;AACL,YAAI;AACA,gBAAM,CAAC,EAAE,EAAE,YAAY,IAAI,MAAM,QAAQ;AACzC,gBAAM;AAAA,QACV,SACO,iBAAiB;AACpB,cAAI;AACA,gBAAI,CAAC,0BAA0B,eAAe;AAC1C;AAAA,UACR,SACOC,kBAAiB;AAEpB,mBAAO,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAeA,gBAAe;AAAA,UACzG;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,GAAG;AAAA,EACP;AACA,WAASC,WAAU,SAAS,MAAM;AAC9B,UAAM,KAAK,WAAW,OAAO;AAC7B,QAAI,OAAO,OAAO,UAAU,OAAO,WAAW,MAAM;AAEhD;AACA,aAAO;AAAA,IACX;AACA,KAAC,YAAY;AACT;AACA,iBAAS;AACL,YAAI;AACA,gBAAM,CAAC,QAAQ,SAAS,4BAA4B,IAAI,MAAM,QAAQ;AAEtE,cAAI;AACA,mBAAO,QAAQ;AACnB,gBAAM,WAAW,QAAQ,UAAU,IAAI,CAAC,YAAY;AAChD,oBAAQ,QAAQ,MAAM;AAAA,cAClB,KAAK,YAAY,MAAM;AAEnB,qBAAK,KAAK,QAAQ,OAAO;AACzB;AAAA,cACJ;AAAA,cACA,KAAK,YAAY,OAAO;AACpB,gBAAC,UAAU,MAAQ,OAAO;AAC1B,qBAAK,MAAM,QAAQ,OAAO;AAC1B,yBAAS;AACT;AAAA,cACJ;AAAA,cACA,KAAK,YAAY,UAAU;AACvB,uBAAO;AACP,yBAAS;AACT;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ,CAAC;AACD,iBAAO,KAAK,iBAAiB;AAAA,YACzB;AAAA,YACA,MAAM,YAAY;AAAA,YAClB;AAAA,UACJ,GAAG,QAAQ,CAAC;AACZ,qBAAW,MAAM;AACb,gBAAI,CAAC,QAAQ,OAAO,eAAe,cAAc;AAE7C,qBAAO,KAAK,iBAAiB;AAAA,gBACzB;AAAA,gBACA,MAAM,YAAY;AAAA,cACtB,GAAG,QAAQ,CAAC;AAChB;AACA,mBAAO;AACP,oBAAQ;AAAA,UACZ;AAIA,gBAAM,6BAA6B,QAAQ,QAAQ;AACnD;AAAA,QACJ,SACO,iBAAiB;AACpB,cAAI,CAAC,0BAA0B,eAAe;AAC1C;AAAA,QACR;AAAA,MACJ;AAAA,IACJ,GAAG,EACE,KAAK,MAAM;AAEZ,UAAI,CAAC;AACD,aAAK,SAAS;AAAA,IACtB,CAAC,EACI,MAAM,CAAC,QAAQ;AAChB,WAAK,MAAM,GAAG;AAAA,IAClB,CAAC;AACD,WAAO,MAAM;AAET,UAAI,CAAC;AACD,iBAAS;AAAA,IACjB;AAAA,EACJ;AACA,SAAO;AAAA,IACH,IAAI,QAAQ;AAAA,IACZ,WAAAA;AAAA,IACA,QAAQ,SAAS;AACb,YAAM,UAAU,CAAC;AACjB,YAAM,WAAW;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS,MAAM;AAAA,QAEf;AAAA,MACJ;AACA,YAAM,UAAUA,WAAU,SAAS;AAAA,QAC/B,KAAK,KAAK;AAEN,kBAAQ,KAAK,GAAG;AAChB,mBAAS,QAAQ;AAAA,QACrB;AAAA,QACA,MAAM,KAAK;AACP,mBAAS,OAAO;AAChB,mBAAS,QAAQ;AACjB,mBAAS,QAAQ;AAAA,QACrB;AAAA,QACA,WAAW;AACP,mBAAS,OAAO;AAChB,mBAAS,QAAQ;AAAA,QACrB;AAAA,MACJ,CAAC;AACD,YAAM,WAAY,SAASC,YAAW;AAClC,eAAO,iBAAiB,MAAM,WAAW,UAAU,aAAa;AAC5D,qBAAS;AACL,gBAAI,CAAC,QAAQ,QAAQ;AAEjB,oBAAM,QAAQ,IAAI,QAAQ,CAAC,YAAa,SAAS,UAAU,OAAQ,CAAC;AAAA,YACxE;AAEA,mBAAO,QAAQ,QAAQ;AAEnB,oBAAM,MAAM,QAAQ,QAAQ,MAAM,CAAC;AAAA,YACvC;AAEA,gBAAI,SAAS,OAAO;AAChB,oBAAM,SAAS;AAAA,YACnB;AAEA,gBAAI,SAAS,MAAM;AACf,qBAAO,MAAM,QAAQ,MAAM;AAAA,YAC/B;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL,EAAG;AACH,eAAS,QAAQ,OAAO,QAAQ;AAC5B,YAAI,CAAC,SAAS,MAAM;AAChB,mBAAS,OAAO;AAChB,mBAAS,QAAQ;AACjB,mBAAS,QAAQ;AAAA,QACrB;AACA,eAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,MAC1C;AACA,eAAS,SAAS,YAAY;AAC1B,gBAAQ;AACR,eAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,MAC1C;AACA,aAAO;AAAA,IACX;AAAA,IACA,MAAM,UAAU;AACZ,iBAAW;AACX,UAAI,YAAY;AAEZ,cAAM,CAAC,MAAM,IAAI,MAAM;AACvB,eAAO,MAAM,KAAM,gBAAgB;AAAA,MACvC;AAAA,IACJ;AAAA,IACA,YAAY;AACR,UAAI,YAAY;AAEZ,gBAAQ,KAAK,UAAU,IAAI,qBAAqB,CAAC;AAAA,MACrD;AAAA,IACJ;AAAA,EACJ;AACJ;AAUO,IAAM,uBAAN,cAAmC,MAAM;AAAA,EAC5C,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EACpB;AACJ;AACA,SAAS,iBAAiB,KAAK;AAC3B,SAAO,SAAS,GAAG,KAAK,UAAU,OAAO,YAAY;AACzD;AACA,SAAS,yBAAyB,MAAM;AACpC,MAAI;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,EACJ,EAAE,SAAS,IAAI;AACX,WAAO;AAEX,SAAO,QAAQ,OAAQ,QAAQ;AACnC;AACA,SAAS,YAAY,KAAK;AACtB,SAAQ,OAAO,QAAQ,cACnB,iBAAiB,OACjB,YAAY,OACZ,aAAa,OACb,gBAAgB,OAChB,UAAU;AAClB;;;ACviBA,IAAI,gBAAgD,SAAU,GAAG;AAC7D,MAAI,CAAC,OAAO;AAAe,UAAM,IAAI,UAAU,sCAAsC;AACrF,MAAI,IAAI,EAAE,OAAO,aAAa,GAAG;AACjC,SAAO,IAAI,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO,aAAa,aAAa,SAAS,CAAC,IAAI,EAAE,OAAO,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AAC9M,WAAS,KAAK,GAAG;AAAE,MAAE,CAAC,IAAI,EAAE,CAAC,KAAK,SAAU,GAAG;AAAE,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAAE,YAAI,EAAE,CAAC,EAAE,CAAC,GAAG,OAAO,SAAS,QAAQ,EAAE,MAAM,EAAE,KAAK;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAG;AAC/J,WAAS,OAAO,SAAS,QAAQ,GAAG,GAAG;AAAE,YAAQ,QAAQ,CAAC,EAAE,KAAK,SAASC,IAAG;AAAE,cAAQ,EAAE,OAAOA,IAAG,MAAM,EAAE,CAAC;AAAA,IAAG,GAAG,MAAM;AAAA,EAAG;AAC/H;AAaO,SAAS,WAAW,SAAS;AAChC,QAAM;AAAA,IAAE;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAO,UAAAC;AAAA,IAAU,SAAAC;AAAA,IAAS,WAAAC;AAAA,IAAW,4BAA4B;AAAA;AAAA,IAC1F;AAAA,IAAW;AAAA,IAAc;AAAA,IAAS;AAAA,IAAa;AAAA,IAAa;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAY,oBAAoB;AAAA,IAAS,qBAAqB;AAAA,EAAU,IAAI;AACzJ,SAAO;AAAA,IACH,OAAO,QAAQ,OAAO;AAClB,YAAM,MAAM;AAAA,QACR,wBAAwB;AAAA,QACxB,cAAc;AAAA,QACd,eAAe,CAAC;AAAA,QAChB;AAAA,MACJ;AACA,UAAI,OAAO,aAAa,+BAA+B;AACnD,eAAO,MAAM,UAAU,0BAA0B,4BAA4B;AAC7E,eAAO,OAAO,MAAM,WAAW;AAE3B,iBAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK,MAAM,MAAM;AAAA,QACtF;AAAA,MACJ;AAGA,YAAM,qBAAqB,4BAA4B,KAAK,SAAS,yBAAyB,IACxF,WAAW,MAAM;AACf,YAAI,CAAC,IAAI;AACL,iBAAO,MAAM,UAAU,iCAAiC,mCAAmC;AAAA,MACnG,GAAG,yBAAyB,IAC1B;AACN,aAAO,UAAU,eAAe,UAAU,MAAM;AAC5C,YAAI,IAAI,KAAK,IAAI;AACjB,YAAI;AACJ,YAAI;AACJ,YAAI;AACA,oBAAU,aAAa,MAAM,OAAO;AAAA,QACxC,SACO,KAAK;AACR,iBAAO,OAAO,MAAM,UAAU,YAAY,0BAA0B;AAAA,QACxE;AACA,gBAAQ,QAAQ,MAAM;AAAA,UAClB,KAAK,YAAY,gBAAgB;AAC7B,gBAAI,IAAI;AACJ,qBAAO,OAAO,MAAM,UAAU,+BAA+B,kCAAkC;AAEnG,gBAAI,yBAAyB;AAC7B,gBAAI,SAAS,QAAQ,OAAO;AAExB,kBAAI,mBAAmB,QAAQ;AACnC,kBAAM,qBAAqB,OAAO,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,GAAG;AACrG,gBAAI,uBAAuB;AACvB,qBAAO,OAAO,MAAM,UAAU,WAAW,WAAW;AAIxD,gBAAI,eAAe;AACnB,kBAAM,OAAO,KAAK,iBAAiB,SAAS,kBAAkB,IACxD;AAAA,cACE,MAAM,YAAY;AAAA,cAClB,SAAS;AAAA,YACb,IACE;AAAA,cACE,MAAM,YAAY;AAAA;AAAA,YAEtB,GAAG,QAAQ,CAAC;AAChB;AAAA,UACJ;AAAA,UACA,KAAK,YAAY,MAAM;AACnB,gBAAI,OAAO;AAEP,qBAAO,MAAM,OAAO,OAAO,QAAQ,OAAO;AAC9C,kBAAM,OAAO,KAAK,iBAAiB,QAAQ,UACrC,EAAE,MAAM,YAAY,MAAM,SAAS,QAAQ,QAAQ,IACnD;AAAA,cACE,MAAM,YAAY;AAAA;AAAA,YAEtB,CAAC,CAAC;AACN;AAAA,UACJ;AAAA,UACA,KAAK,YAAY;AACb,mBAAO,QAAQ,KAAK,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,QAAQ,QAAQ,OAAO;AAAA,UAC3G,KAAK,YAAY,WAAW;AACxB,gBAAI,CAAC,IAAI;AACL,qBAAO,OAAO,MAAM,UAAU,cAAc,cAAc;AAC9D,kBAAM,EAAE,IAAI,QAAQ,IAAI;AACxB,gBAAI,MAAM,IAAI;AACV,qBAAO,OAAO,MAAM,UAAU,yBAAyB,kBAAkB,EAAE,iBAAiB;AAGhG,gBAAI,cAAc,EAAE,IAAI;AACxB,kBAAM,OAAO;AAAA,cACT,MAAM,OAAO,QAAQ,SAAS;AAC1B,oBAAI,cAAc;AAAA,kBACd;AAAA,kBACA,MAAM,YAAY;AAAA,kBAClB,SAAS;AAAA,gBACb;AACA,sBAAM,cAAc,OAAO,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,KAAK,aAAa,MAAM,MAAM;AAChH,oBAAI;AACA,gCAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG,EAAE,SAAS,YAAY,CAAC;AACxF,sBAAM,OAAO,KAAK,iBAAiB,aAAa,QAAQ,CAAC;AAAA,cAC7D;AAAA,cACA,OAAO,OAAO,WAAW;AACrB,oBAAI,eAAe;AAAA,kBACf;AAAA,kBACA,MAAM,YAAY;AAAA,kBAClB,SAAS;AAAA,gBACb;AACA,sBAAM,cAAc,OAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK,cAAc,MAAM;AAC9G,oBAAI;AACA,iCAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,SAAS,YAAY,CAAC;AAC1F,sBAAM,OAAO,KAAK,iBAAiB,cAAc,QAAQ,CAAC;AAAA,cAC9D;AAAA,cACA,UAAU,OAAO,iBAAiB;AAC9B,sBAAM,kBAAkB;AAAA,kBACpB;AAAA,kBACA,MAAM,YAAY;AAAA,gBACtB;AACA,uBAAO,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,KAAK,eAAe;AAC9F,oBAAI;AACA,wBAAM,OAAO,KAAK,iBAAiB,iBAAiB,QAAQ,CAAC;AAAA,cACrE;AAAA,YACJ;AACA,gBAAI;AACA,kBAAI;AACJ,oBAAM,wBAAwB,OAAO,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,KAAK,OAAO;AACvH,kBAAI,uBAAuB;AACvB,oBAAI,iBAAiB,qBAAqB;AACtC,yBAAO,MAAM,IAAI,gBACX,MAAM,KAAK,MAAM,qBAAqB,IACtC;AAAA,yBACD,MAAM,QAAQ,qBAAqB;AACxC,wBAAM,IAAI,MAAM,uFAAuF;AAE3G,2BAAW;AAAA,cACf,OACK;AAGD,oBAAI,CAAC;AACD,wBAAM,IAAI,MAAM,oCAAoC;AACxD,sBAAM,OAAO;AAAA,kBACT,eAAe,QAAQ;AAAA,kBACvB,UAAU,MAAM,QAAQ,KAAK;AAAA,kBAC7B,gBAAgB,QAAQ;AAAA,gBAC5B;AACA,2BAAW,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,QAAQ,OAAO,WAAW,aACpE,MAAM,OAAO,KAAK,SAAS,IAAI,IAC/B,OAAO,CAAC;AAClB,sBAAM,oBAAoBF,cAAa,QAAQA,cAAa,SAASA,YAAW,UAAiB,SAAS,QAAQ,SAAS,QAAQ;AACnI,oBAAI,iBAAiB,SAAS;AAC1B,yBAAO,MAAM,IAAI,gBACX,MAAM,KAAK,MAAM,gBAAgB,IACjC;AAAA,cACd;AACA,oBAAM,eAAe,gBAAgB,SAAS,UAAU,SAAS,aAAa;AAC9E,kBAAI,CAAC;AACD,uBAAO,MAAM,IAAI,gBACX,MAAM,KAAK,MAAM;AAAA,kBACf,IAAI,aAAa,8BAA8B;AAAA,gBACnD,CAAC,IACC;AAEV,kBAAI,EAAE,eAAe;AACjB,yBAAS,YAAY,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,aAAa,SAAS;AAEnG,kBAAI,EAAE,kBAAkB;AACpB,yBAAS,eACL,OAAO,YAAY,aACb,MAAM,QAAQ,KAAK,SAAS,QAAQ,IACpC;AAGd,kBAAI;AACJ,kBAAI,aAAa,cAAc;AAC3B,kCAAkB,OAAOE,eAAc,QAAQA,eAAc,SAASA,aAAY,WAAkB,QAAQ;AAAA;AAG5G,kCAAkB,OAAOD,aAAY,QAAQA,aAAY,SAASA,WAAU,SAAgB,QAAQ;AACxG,oBAAM,cAAc,OAAO,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,KAAK,SAAS,UAAU,eAAe;AACxI,kBAAI;AACA,kCAAkB;AACtB,kBAAI,gBAAgB,eAAe,GAAG;AAElC,oBAAI,EAAE,MAAM,IAAI,gBAAgB;AAE5B,sBAAI,iBAAiB,eAAe;AAChC,oCAAgB,OAAO,MAAS;AAAA,gBACxC,OACK;AACD,sBAAI,cAAc,EAAE,IAAI;AACxB,sBAAI;AACA,6BAAS,KAAK,MAAM,oBAAoB,cAAc,eAAe,GAAG,qBAAqB,sBAAsB,MAAM,kBAAkB,KAAK,GAAG,KAAK,oBAAoB,MAAM,CAAC,IAAI,KAAK,MAAM;AAC9L,2BAAK,oBAAoB;AACzB,2BAAK;AACL,4BAAM,SAAS;AACf,4BAAM,KAAK,KAAK,QAAQ,QAAQ;AAAA,oBACpC;AAAA,kBACJ,SACO,OAAO;AAAE,0BAAM,EAAE,OAAO,MAAM;AAAA,kBAAG,UACxC;AACI,wBAAI;AACA,0BAAI,CAAC,MAAM,CAAC,OAAO,KAAK,kBAAkB;AAAS,8BAAM,GAAG,KAAK,iBAAiB;AAAA,oBACtF,UACA;AAAU,0BAAI;AAAK,8BAAM,IAAI;AAAA,oBAAO;AAAA,kBACxC;AAAA,gBACJ;AAAA,cACJ,OACK;AAID,oBAAI,MAAM,IAAI;AACV,wBAAM,KAAK,KAAK,iBAAiB,QAAQ;AAAA,cACjD;AAGA,oBAAM,KAAK,SAAS,MAAM,IAAI,aAAa;AAAA,YAC/C,UACA;AAEI,qBAAO,IAAI,cAAc,EAAE;AAAA,YAC/B;AACA;AAAA,UACJ;AAAA,UACA,KAAK,YAAY,UAAU;AACvB,kBAAM,eAAe,IAAI,cAAc,QAAQ,EAAE;AACjD,mBAAO,IAAI,cAAc,QAAQ,EAAE;AACnC,gBAAI,iBAAiB,YAAY;AAC7B,oBAAM,aAAa,OAAO,MAAS;AACvC;AAAA,UACJ;AAAA,UACA;AACI,kBAAM,IAAI,MAAM,8BAA8B,QAAQ,IAAI,WAAW;AAAA,QAC7E;AAAA,MACJ,CAAC;AAED,aAAO,OAAO,MAAM,WAAW;AAC3B,YAAI;AACA,uBAAa,kBAAkB;AACnC,cAAM,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,aAAa;AAEhD,YAAI,gBAAgB,CAAC;AAErB,cAAM,QAAQ,IAAI,OAAO,OAAO,IAAI,EAC/B,OAAO,gBAAgB,EACvB,IAAI,CAAC,QAAQ,IAAI,OAAO,MAAS,CAAC,CAAC;AACxC,YAAI,IAAI;AACJ,iBAAO,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,KAAK,MAAM,MAAM;AACrG,eAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK,MAAM,MAAM;AAAA,MACtF;AAAA,IACJ;AAAA,EACJ;AACJ;AAeO,SAAS,gBAAgB,WAAW;AACvC,UAAQ,MAAM;AAAA,IACV,MAAK,qBAAqB,OACtB,UAAU,IAAI,6BAA6B;AAAA,IAC/C,MAAK,MAAM,QAAQ,SAAS,KACxB,UAAU,SAAS,6BAA6B;AAAA,IACpD,MAAK,OAAO,cAAc,YACtB,UACK,MAAM,GAAG,EACT,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,EACnB,SAAS,6BAA6B;AAC3C,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;", "names": ["CloseCode", "MessageType", "retries", "listeners", "message", "socket", "errOrCloseEvent", "subscribe", "iterator", "v", "validate", "execute", "subscribe"]}