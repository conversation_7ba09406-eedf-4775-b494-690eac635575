{"version": 3, "file": "removeTypenameFromVariables.js", "sourceRoot": "", "sources": ["../../../src/link/remove-typename/removeTypenameFromVariables.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAEhC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EACL,aAAa,EACb,aAAa,EACb,UAAU,GAEX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,MAAM,CAAC,IAAM,IAAI,GAAG,QAAQ,CAAC;AAU7B,MAAM,UAAU,2BAA2B,CACzC,OAAiE;IAAjE,wBAAA,EAAA,UAA8C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IAEjE,OAAO,MAAM,CAAC,MAAM,CAClB,IAAI,UAAU,CAAC,UAAC,SAAS,EAAE,OAAO;QACxB,IAAA,MAAM,GAAK,OAAO,OAAZ,CAAa;QACnB,IAAA,KAAK,GAAgB,SAAS,MAAzB,EAAE,SAAS,GAAK,SAAS,UAAd,CAAe;QAEvC,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,SAAS;gBACjB,MAAM,CAAC,CAAC;oBACN,6BAA6B,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;oBACzD,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;IAC5B,CAAC,CAAC,EACF,OAAO,CAAC,CAAC;QACP;YACE,kBAAkB;;gBAChB,OAAO;oBACL,2BAA2B,EAAE;wBAC3B,sBAAsB,EAAE,MAAA,sBAAsB,aAAtB,sBAAsB,uBAAtB,sBAAsB,CAAE,IAAI,mCAAI,CAAC;qBAC1D;iBACF,CAAC;YACJ,CAAC;SACF;QACH,CAAC,CAAC,EAAE,CACL,CAAC;AACJ,CAAC;AAED,SAAS,6BAA6B,CACpC,KAAmB,EACnB,SAA6B,EAC7B,MAA0B;IAE1B,IAAM,mBAAmB,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;IAE1D,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAC,MAAM;QAC5B,IAAA,GAAG,GAAW,MAAM,GAAjB,EAAE,KAAK,GAAI,MAAM,GAAV,CAAW;QAC5B,IAAM,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAExC,MAAM,CAAC,CAAC,CAAC;YACP,cAAc,CAAC,CAAC;gBACd,kBAAkB,CAAC,KAAK,EAAE,cAAc,CAAC;gBAC3C,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAEzB,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC,CACH,CAAC;AACJ,CAAC;AAKD,SAAS,kBAAkB,CACzB,KAAgB,EAChB,MAAkC;IAElC,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,EAAhC,CAAgC,CAAC,CAAC;IAC/D,CAAC;IAED,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,IAAM,UAAQ,GAAwB,EAAE,CAAC;QAEzC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;YAC7B,IAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;YAEzB,IAAI,GAAG,KAAK,YAAY,EAAE,CAAC;gBACzB,OAAO;YACT,CAAC;YAED,IAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAEhC,UAAQ,CAAC,GAAG,CAAC;gBACX,WAAW,CAAC,CAAC;oBACX,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC;oBACxC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,OAAO,UAAQ,CAAC;IAClB,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,IAAM,sBAAsB,GAAG,IAAI,CACjC,UAAC,QAAsB;IACrB,IAAM,WAAW,GAA2B,EAAE,CAAC;IAE/C,KAAK,CAAC,QAAQ,EAAE;QACd,kBAAkB,YAAC,IAAI;YACrB,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC;KACF,CAAC,CAAC;IAEH,OAAO,WAAW,CAAC;AACrB,CAAC,EACD;IACE,GAAG,EACD,UAAU,CAAC,oDAAoD,CAAC;0FACO;IACzE,KAAK,EAAE,SAAS;CACjB,CACF,CAAC;AAEF,SAAS,UAAU,CAAC,IAAc;IAChC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,aAAa;YACrB,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,KAAK,IAAI,CAAC,SAAS;YACjB,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,KAAK,IAAI,CAAC,UAAU;YAClB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;IAC3B,CAAC;AACH,CAAC", "sourcesContent": ["import { wrap } from \"optimism\";\nimport type { DocumentNode, TypeNode } from \"graphql\";\nimport { Kind, visit } from \"graphql\";\nimport { ApolloLink } from \"../core/index.js\";\nimport {\n  stripTypename,\n  isPlainObject,\n  cacheSizes,\n  defaultCacheSizes,\n} from \"../../utilities/index.js\";\nimport type { OperationVariables } from \"../../core/index.js\";\nimport { WeakCache } from \"@wry/caches\";\n\nexport const KEEP = \"__KEEP\";\n\ninterface KeepTypenameConfig {\n  [key: string]: typeof KEEP | KeepTypenameConfig;\n}\n\nexport interface RemoveTypenameFromVariablesOptions {\n  except?: KeepTypenameConfig;\n}\n\nexport function removeTypenameFromVariables(\n  options: RemoveTypenameFromVariablesOptions = Object.create(null)\n) {\n  return Object.assign(\n    new ApolloLink((operation, forward) => {\n      const { except } = options;\n      const { query, variables } = operation;\n\n      if (variables) {\n        operation.variables =\n          except ?\n            maybeStripTypenameUsingConfig(query, variables, except)\n          : stripTypename(variables);\n      }\n\n      return forward(operation);\n    }),\n    __DEV__ ?\n      {\n        getMemoryInternals() {\n          return {\n            removeTypenameFromVariables: {\n              getVariableDefinitions: getVariableDefinitions?.size ?? 0,\n            },\n          };\n        },\n      }\n    : {}\n  );\n}\n\nfunction maybeStripTypenameUsingConfig(\n  query: DocumentNode,\n  variables: OperationVariables,\n  config: KeepTypenameConfig\n) {\n  const variableDefinitions = getVariableDefinitions(query);\n\n  return Object.fromEntries(\n    Object.entries(variables).map((keyVal) => {\n      const [key, value] = keyVal;\n      const typename = variableDefinitions[key];\n      const typenameConfig = config[typename];\n\n      keyVal[1] =\n        typenameConfig ?\n          maybeStripTypename(value, typenameConfig)\n        : stripTypename(value);\n\n      return keyVal;\n    })\n  );\n}\n\ntype JSONPrimitive = string | number | null | boolean;\ntype JSONValue = JSONPrimitive | JSONValue[] | { [key: string]: JSONValue };\n\nfunction maybeStripTypename(\n  value: JSONValue,\n  config: KeepTypenameConfig[string]\n): JSONValue {\n  if (config === KEEP) {\n    return value;\n  }\n\n  if (Array.isArray(value)) {\n    return value.map((item) => maybeStripTypename(item, config));\n  }\n\n  if (isPlainObject(value)) {\n    const modified: Record<string, any> = {};\n\n    Object.keys(value).forEach((key) => {\n      const child = value[key];\n\n      if (key === \"__typename\") {\n        return;\n      }\n\n      const fieldConfig = config[key];\n\n      modified[key] =\n        fieldConfig ?\n          maybeStripTypename(child, fieldConfig)\n        : stripTypename(child);\n    });\n\n    return modified;\n  }\n\n  return value;\n}\n\nconst getVariableDefinitions = wrap(\n  (document: DocumentNode) => {\n    const definitions: Record<string, string> = {};\n\n    visit(document, {\n      VariableDefinition(node) {\n        definitions[node.variable.name.value] = unwrapType(node.type);\n      },\n    });\n\n    return definitions;\n  },\n  {\n    max:\n      cacheSizes[\"removeTypenameFromVariables.getVariableDefinitions\"] ||\n      defaultCacheSizes[\"removeTypenameFromVariables.getVariableDefinitions\"],\n    cache: WeakCache,\n  }\n);\n\nfunction unwrapType(node: TypeNode): string {\n  switch (node.kind) {\n    case Kind.NON_NULL_TYPE:\n      return unwrapType(node.type);\n    case Kind.LIST_TYPE:\n      return unwrapType(node.type);\n    case Kind.NAMED_TYPE:\n      return node.name.value;\n  }\n}\n"]}