{"version": 3, "file": "createOperation.js", "sourceRoot": "", "sources": ["../../../src/link/utils/createOperation.ts"], "names": [], "mappings": ";AAEA,MAAM,UAAU,eAAe,CAC7B,QAAa,EACb,SAAyB;IAEzB,IAAI,OAAO,gBAAQ,QAAQ,CAAE,CAAC;IAC9B,IAAM,UAAU,GAA4B,UAAC,IAAI;QAC/C,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;YAC/B,OAAO,yBAAQ,OAAO,GAAK,IAAI,CAAC,OAAO,CAAC,CAAE,CAAC;QAC7C,CAAC;aAAM,CAAC;YACN,OAAO,yBAAQ,OAAO,GAAK,IAAI,CAAE,CAAC;QACpC,CAAC;IACH,CAAC,CAAC;IACF,IAAM,UAAU,GAA4B,cAAM,OAAA,cAAM,OAAO,EAAG,EAAhB,CAAgB,CAAC;IAEnE,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,YAAY,EAAE;QAC7C,UAAU,EAAE,KAAK;QACjB,KAAK,EAAE,UAAU;KAClB,CAAC,CAAC;IAEH,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,YAAY,EAAE;QAC7C,UAAU,EAAE,KAAK;QACjB,KAAK,EAAE,UAAU;KAClB,CAAC,CAAC;IAEH,OAAO,SAAsB,CAAC;AAChC,CAAC", "sourcesContent": ["import type { GraphQLRequest, Operation } from \"../core/index.js\";\n\nexport function createOperation(\n  starting: any,\n  operation: GraphQLRequest\n): Operation {\n  let context = { ...starting };\n  const setContext: Operation[\"setContext\"] = (next) => {\n    if (typeof next === \"function\") {\n      context = { ...context, ...next(context) };\n    } else {\n      context = { ...context, ...next };\n    }\n  };\n  const getContext: Operation[\"getContext\"] = () => ({ ...context });\n\n  Object.defineProperty(operation, \"setContext\", {\n    enumerable: false,\n    value: setContext,\n  });\n\n  Object.defineProperty(operation, \"getContext\", {\n    enumerable: false,\n    value: getContext,\n  });\n\n  return operation as Operation;\n}\n"]}