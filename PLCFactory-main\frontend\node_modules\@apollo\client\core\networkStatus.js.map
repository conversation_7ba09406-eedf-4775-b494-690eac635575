{"version": 3, "file": "networkStatus.js", "sourceRoot": "", "sources": ["../../src/core/networkStatus.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,CAAN,IAAY,aA0CX;AA1CD,WAAY,aAAa;IACvB;;;;OAIG;IACH,uDAAW,CAAA;IAEX;;;OAGG;IACH,iEAAgB,CAAA;IAEhB;;;OAGG;IACH,2DAAa,CAAA;IAEb;;;OAGG;IACH,uDAAW,CAAA;IAEX;;;;OAIG;IACH,iDAAQ,CAAA;IAER;;OAEG;IACH,mDAAS,CAAA;IAET;;OAEG;IACH,mDAAS,CAAA;AACX,CAAC,EA1CW,aAAa,KAAb,aAAa,QA0CxB;AAED;;;GAGG;AACH,MAAM,UAAU,wBAAwB,CACtC,aAA6B;IAE7B,OAAO,aAAa,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AACnD,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,uBAAuB,CACrC,aAA6B;IAE7B,OAAO,aAAa,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,CAAC;AACpD,CAAC", "sourcesContent": ["/**\n * The current status of a query’s execution in our system.\n */\nexport enum NetworkStatus {\n  /**\n   * The query has never been run before and the query is now currently running. A query will still\n   * have this network status even if a partial data result was returned from the cache, but a\n   * query was dispatched anyway.\n   */\n  loading = 1,\n\n  /**\n   * If `setVariables` was called and a query was fired because of that then the network status\n   * will be `setVariables` until the result of that query comes back.\n   */\n  setVariables = 2,\n\n  /**\n   * Indicates that `fetchMore` was called on this query and that the query created is currently in\n   * flight.\n   */\n  fetchMore = 3,\n\n  /**\n   * Similar to the `setVariables` network status. It means that `refetch` was called on a query\n   * and the refetch request is currently in flight.\n   */\n  refetch = 4,\n\n  /**\n   * Indicates that a polling query is currently in flight. So for example if you are polling a\n   * query every 10 seconds then the network status will switch to `poll` every 10 seconds whenever\n   * a poll request has been sent but not resolved.\n   */\n  poll = 6,\n\n  /**\n   * No request is in flight for this query, and no errors happened. Everything is OK.\n   */\n  ready = 7,\n\n  /**\n   * No request is in flight for this query, but one or more errors were detected.\n   */\n  error = 8,\n}\n\n/**\n * Returns true if there is currently a network request in flight according to a given network\n * status.\n */\nexport function isNetworkRequestInFlight(\n  networkStatus?: NetworkStatus\n): boolean {\n  return networkStatus ? networkStatus < 7 : false;\n}\n\n/**\n * Returns true if the network request is in ready or error state according to a given network\n * status.\n */\nexport function isNetworkRequestSettled(\n  networkStatus?: NetworkStatus\n): boolean {\n  return networkStatus === 7 || networkStatus === 8;\n}\n"]}