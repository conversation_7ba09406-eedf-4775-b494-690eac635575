package exporters

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	"hmi/internal/models"
	"hmi/pkg/logger"
	"time"

	"github.com/google/uuid"
)

// FactoryTalkViewSEExporter implements the HMIExporter interface for FactoryTalk View SE format.
// This exporter generates a simplified XML representation of HMI screens, components, and tags
// that can be conceptually imported into FactoryTalk View SE.
// Note: FactoryTalk View SE uses a proprietary binary format for display files (.gfx) and
// a specific database for tagname dictionaries. This implementation will generate
// text-based representations (e.g., XML) that conceptually map to FactoryTalk elements.
type FactoryTalkViewSEExporter struct{}

// NewFactoryTalkViewSEExporter creates a new FactoryTalkViewSEExporter instance.
func NewFactoryTalkViewSEExporter() *FactoryTalkViewSEExporter {
	return &FactoryTalkViewSEExporter{}
}

// GetName returns the name of the exporter.
func (e *FactoryTalkViewSEExporter) GetName() string {
	return "FactoryTalk View SE (Rockwell)"
}

// GetFileExtension returns the file extension for the exported format.
func (e *FactoryTalkViewSEExporter) GetFileExtension() string {
	return ".xml" // A simplified XML representation, not a .mer or .apa
}

// ExportScreens exports one or more HMI screens to the FactoryTalk View SE XML format.
func (e *FactoryTalkViewSEExporter) ExportScreens(ctx context.Context, screens []models.HMIScreen, options []models.HMIScreenExportOptions) (*models.HMIExportRequest, error) {
	logger.Infof("Exporting %d screens to FactoryTalk View SE format...", len(screens))

	requestId := uuid.New().String()
	createdAt := time.Now()

	exportRequest := &models.HMIExportRequest{
		ID:          requestId,
		ScreenIDs:   getScreenIDs(screens),
		Format:      models.HMIExportFormatFactoryTalkViewSE,
		Status:      models.ExportStatusInProgress,
		CreatedAt:   createdAt.Format(time.RFC3339),
		UpdatedAt:   createdAt.Format(time.RFC3339),
		Log:         "Starting FactoryTalk View SE export...\n",
		Metadata:    map[string]interface{}{"options": options},
	}

	// Simulate export process
	go func() {
		time.Sleep(3 * time.Second) // Simulate work

		var buf bytes.Buffer
		buf.WriteString(xml.Header)
		buf.WriteString("<RSViewProject ExportedBy=\"Continuum HMI Designer\" Version=\"1.0\">\n")
		buf.WriteString(fmt.Sprintf("  <ExportDate>%s</ExportDate>\n", time.Now().Format(time.RFC3339)))
		buf.WriteString("  <Displays>\n")

		for _, screen := range screens {
			screenXML, err := e.generateDisplayXML(screen, options)
			if err != nil {
				exportRequest.Status = models.ExportStatusFailed
				exportRequest.ErrorMessage = fmt.Sprintf("Failed to generate XML for screen %s: %v", screen.Name, err)
				exportRequest.Log += fmt.Sprintf("Error: %v\n", err)
				logger.Errorf("FactoryTalk View SE export failed for screen %s: %v", screen.Name, err)
				return
			}
			buf.WriteString(screenXML)
		}
		buf.WriteString("  </Displays>\n")

		// Add global tags (simplified)
		buf.WriteString("  <Tags>\n")
		allTags := make(map[string]models.PLCTag)
		for _, screen := range screens {
			for _, tag := range screen.Tags {
				allTags[tag.ID] = tag // Collect all unique tags
			}
		}
		for _, tag := range allTags {
			buf.WriteString(e.generateTagXML(tag))
		}
		buf.WriteString("  </Tags>\n")

		// Add VBA macros (simplified)
		buf.WriteString("  <VBAMacros>\n")
		buf.WriteString("    <!-- Placeholder for VBA Macro definitions -->\n")
		buf.WriteString("  </VBAMacros>\n")

		buf.WriteString("</RSViewProject>\n")

		exportRequest.Status = models.ExportStatusCompleted
		exportRequest.CompletedAt = time.Now()
		exportRequest.OutputURL = fmt.Sprintf("/exports/hmi/%s.xml", requestId) // Mock URL
		exportRequest.Log += "FactoryTalk View SE export completed successfully.\n"
		exportRequest.OutputData = buf.Bytes() // Store the generated XML

		logger.Infof("FactoryTalk View SE export completed: %s", requestId)
	}()

	return exportRequest, nil
}

// generateDisplayXML converts an HMIScreen model to FactoryTalk View SE-compatible XML.
// This is a simplified representation. Real FactoryTalk displays are complex.
func (e *FactoryTalkViewSEExporter) generateDisplayXML(screen models.HMIScreen, options []models.HMIScreenExportOptions) (string, error) {
	var buf bytes.Buffer
	buf.WriteString(fmt.Sprintf("    <Display Name=\"%s\" Width=\"%d\" Height=\"%d\" BackgroundColor=\"%s\">\n",
		screen.Name, screen.Width, screen.Height, screen.BackgroundColor))
	buf.WriteString(fmt.Sprintf("      <Description>%s</Description>\n", screen.Description))
	buf.WriteString("      <Objects>\n")

	for _, comp := range screen.Components {
		compXML, err := e.generateGraphicXML(comp, options)
		if err != nil {
			return "", err
		}
		buf.WriteString(compXML)
	}
	buf.WriteString("      </Objects>\n")
	buf.WriteString("    </Display>\n")
	return buf.String(), nil
}

// generateGraphicXML converts an HMIComponent model to FactoryTalk View SE-compatible XML.
// This is a simplified representation. Real FactoryTalk graphics have many more properties.
func (e *FactoryTalkViewSEExporter) generateGraphicXML(comp models.HMIComponent, options []models.HMIScreenExportOptions) (string, error) {
	var buf bytes.Buffer

	// Map HMI component type to FactoryTalk graphic type
	ftvType := e.mapComponentType(comp.Type)

	buf.WriteString(fmt.Sprintf("        <Graphic Type=\"%s\" Name=\"%s\">\n", ftvType, comp.Name))
	buf.WriteString(fmt.Sprintf("          <Position X=\"%f\" Y=\"%f\" Width=\"%f\" Height=\"%f\" ZOrder=\"%d\" />\n",
		comp.Position.X, comp.Position.Y, comp.Size.X, comp.Size.Y, comp.ZIndex))

	// Basic properties
	buf.WriteString("          <Properties>\n")
	for key, val := range comp.Properties {
		// Convert property names to FactoryTalk format
		ftvProp := e.mapPropertyName(key)
		buf.WriteString(fmt.Sprintf("            <%s>%v</%s>\n", ftvProp, val, ftvProp))
	}
	buf.WriteString("          </Properties>\n")

	// Tag bindings (converted to FactoryTalk connections)
	if len(comp.Bindings) > 0 {
		buf.WriteString("          <Connections>\n")
		for _, binding := range comp.Bindings {
			buf.WriteString(fmt.Sprintf("            <Connection Property=\"%s\" Tag=\"%s\" ReadOnly=\"%t\">\n",
				e.mapPropertyName(binding.Property), binding.TagID, binding.ReadOnly))

			// If there's a transform, convert it to a FactoryTalk expression
			if binding.Transform != "" {
				ftvExpression := e.convertTransformToExpression(binding.Transform)
				buf.WriteString(fmt.Sprintf("              <Expression>%s</Expression>\n", ftvExpression))
			}
			buf.WriteString("            </Connection>\n")
		}
		buf.WriteString("          </Connections>\n")
	}

	// Animations (converted to FactoryTalk VBA scripts or built-in animations)
	if len(comp.Animations) > 0 {
		buf.WriteString("          <Animations>\n")
		for _, anim := range comp.Animations {
			buf.WriteString(fmt.Sprintf("            <Animation Type=\"%s\" TriggerTag=\"%s\" TriggerValue=\"%v\">\n",
				anim.Type, anim.TriggerTagID, anim.TriggerValue))
			buf.WriteString("              <Script>\n")
			buf.WriteString(e.convertAnimationToVBA(anim))
			buf.WriteString("              </Script>\n")
			buf.WriteString("            </Animation>\n")
		}
		buf.WriteString("          </Animations>\n")
	}

	// Add FactoryTalk-specific event handlers if needed
	buf.WriteString("          <Events>\n")
	if comp.Type == "button" {
		buf.WriteString("            <Event Name=\"OnRelease\">\n")
		buf.WriteString("              <Action Type=\"TagWrite\">\n")
		buf.WriteString("                <Tag>MyButtonTag</Tag>\n") // Placeholder
		buf.WriteString("                <Value>1</Value>\n")
		buf.WriteString("              </Action>\n")
		buf.WriteString("            </Event>\n")
	}
	buf.WriteString("          </Events>\n")

	buf.WriteString("        </Graphic>\n")
	return buf.String(), nil
}

// generateTagXML converts a PLCTag model to FactoryTalk View SE-compatible XML.
// This is a simplified representation for FactoryTalk's tag database.
func (e *FactoryTalkViewSEExporter) generateTagXML(tag models.PLCTag) string {
	// Map data type to FactoryTalk data type
	ftvDataType := e.mapDataType(tag.DataType)

	// Simplified tag definition. In FactoryTalk, tags are typically defined in the Tag Database.
	// This XML would represent an importable tag definition.
	tagXML := fmt.Sprintf("    <Tag Name=\"%s\" DataType=\"%s\" Address=\"%s\">\n"+
		"      <Description>%s</Description>\n"+
		"      <AccessName>MyAccess</AccessName>\n"+ // Placeholder for Access Name
		"      <AlarmGroup>None</AlarmGroup>\n"+
		"      <LogGroup>None</LogGroup>\n"+
		"    </Tag>\n",
		tag.Name, ftvDataType, tag.Address, tag.Description)

	return tagXML
}

// mapComponentType maps HMI component types to FactoryTalk graphic types.
func (e *FactoryTalkViewSEExporter) mapComponentType(hmiType string) string {
	switch hmiType {
	case "button":
		return "Pushbutton"
	case "indicator":
		return "Circle" // Or Rectangle, depending on shape
	case "gauge":
		return "AnalogMeter"
	case "text":
		return "Text"
	case "slider":
		return "Slider"
	case "trend-chart":
		return "Trend"
	default:
		return "Graphic" // Generic graphic object
	}
}

// mapPropertyName maps HMI property names to FactoryTalk property names.
func (e *FactoryTalkViewSEExporter) mapPropertyName(hmiProp string) string {
	switch hmiProp {
	case "text":
		return "Caption"
	case "backgroundColor":
		return "FillColor"
	case "textColor":
		return "FontColor"
	case "borderColor":
		return "LineColor"
	case "fontSize":
		return "FontSize"
	case "fontFamily":
		return "FontName"
	case "value":
		return "Value"
	case "minValue":
		return "MinValue"
	case "maxValue":
		return "MaxValue"
	default:
		return hmiProp // Use the original property name if no mapping exists
	}
}

// mapDataType maps HMI data types to FactoryTalk data types.
func (e *FactoryTalkViewSEExporter) mapDataType(hmiDataType string) string {
	switch hmiDataType {
	case "BOOL":
		return "Boolean"
	case "INT":
		return "Int"
	case "DINT":
		return "DInt"
	case "REAL":
		return "Real"
	case "STRING":
		return "String"
	default:
		return "Variant" // Default to Variant for unknown types
	}
}

// convertTransformToExpression converts an HMI transform expression to a FactoryTalk expression.
func (e *FactoryTalkViewSEExporter) convertTransformToExpression(transform string) string {
	// In a real implementation, you would parse the transform expression
	// and convert it to FactoryTalk's expression syntax
	
	// This is a simplified example that just wraps the transform in a function
	return fmt.Sprintf("If([%s], 1, 0)", transform)
}

// convertAnimationToVBA converts an HMI animation to a FactoryTalk VBA script.
func (e *FactoryTalkViewSEExporter) convertAnimationToVBA(anim models.Animation) string {
	// In a real implementation, you would generate specific VBA code based on animation type
	
	// This is a simplified example
	switch anim.Type {
	case "visibility":
		return "If [TriggerTag] = [TriggerValue] Then\n" +
			"    Me.Visible = True\n" +
			"Else\n" +
			"    Me.Visible = False\n" +
			"End If"
	case "color":
		return "If [TriggerTag] = [TriggerValue] Then\n" +
			"    Me.FillColor = RGB(0, 255, 0) ' Green\n" +
			"Else\n" +
			"    Me.FillColor = RGB(255, 0, 0) ' Red\n" +
			"End If"
	case "movement":
		return "If [TriggerTag] = [TriggerValue] Then\n" +
			"    Me.Left = Me.Left + 10\n" +
			"Else\n" +
			"    Me.Left = Me.Left - 10\n" +
			"End If"
	case "rotation":
		return "Me.Rotation = [TriggerTag] * 3.6 ' Scale 0-100 to 0-360 degrees"
	default:
		return "' No script generated for this animation type"
	}
}

// getScreenIDs extracts IDs from a slice of HMIScreen models.
func getScreenIDs(screens []models.HMIScreen) []string {
	ids := make([]string, len(screens))
	for i, screen := range screens {
		ids[i] = screen.ID.Hex()
	}
	return ids
}
