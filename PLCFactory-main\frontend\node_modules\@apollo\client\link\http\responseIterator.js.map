{"version": 3, "file": "responseIterator.js", "sourceRoot": "", "sources": ["../../../src/link/http/responseIterator.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAIH,OAAO,EAAE,yBAAyB,EAAE,MAAM,0BAA0B,CAAC;AAErE,OAAO,aAAa,MAAM,sBAAsB,CAAC;AACjD,OAAO,kBAAkB,MAAM,2BAA2B,CAAC;AAC3D,OAAO,eAAe,MAAM,wBAAwB,CAAC;AACrD,OAAO,cAAc,MAAM,uBAAuB,CAAC;AAEnD,SAAS,cAAc,CAAC,KAAU;IAChC,OAAO,CAAC,CAAE,KAAsB,CAAC,IAAI,CAAC;AACxC,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAU;IAClC,OAAO,CAAC,CAAE,KAA6B,CAAC,SAAS,CAAC;AACpD,CAAC;AAED,SAAS,uBAAuB,CAC9B,KAAU;IAEV,OAAO,CAAC,CAAC,CACP,yBAAyB;QACxB,KAAoC,CAAC,MAAM,CAAC,aAAa,CAAC,CAC5D,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAU;IAClC,OAAO,CAAC,CAAE,KAAc,CAAC,MAAM,CAAC;AAClC,CAAC;AAED,SAAS,MAAM,CAAC,KAAU;IACxB,OAAO,CAAC,CAAE,KAAc,CAAC,WAAW,CAAC;AACvC,CAAC;AAED,SAAS,oBAAoB,CAAC,KAAU;IACtC,OAAO,CAAC,CAAE,KAA4B,CAAC,IAAI,CAAC;AAC9C,CAAC;AAED,MAAM,UAAU,gBAAgB,CAC9B,QAAiC;IAEjC,IAAI,IAAI,GAAY,QAAQ,CAAC;IAE7B,IAAI,cAAc,CAAC,QAAQ,CAAC;QAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;IAEnD,IAAI,uBAAuB,CAAC,IAAI,CAAC;QAAE,OAAO,aAAa,CAAI,IAAI,CAAC,CAAC;IAEjE,IAAI,gBAAgB,CAAC,IAAI,CAAC;QAAE,OAAO,cAAc,CAAI,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IAEvE,mDAAmD;IACnD,wDAAwD;IACxD,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO,cAAc,CAClB,IAAI,CAAC,MAAM,EAAmC,CAAC,SAAS,EAAE,CAC5D,CAAC;IACJ,CAAC;IAED,IAAI,MAAM,CAAC,IAAI,CAAC;QAAE,OAAO,eAAe,CAAI,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IAEhE,IAAI,oBAAoB,CAAC,IAAI,CAAC;QAAE,OAAO,kBAAkB,CAAI,IAAI,CAAC,CAAC;IAEnE,MAAM,IAAI,KAAK,CACb,4EAA4E,CAC7E,CAAC;AACJ,CAAC", "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/index.ts\n */\n\nimport type { Response as NodeResponse } from \"node-fetch\";\nimport type { Readable as NodeReadableStream } from \"stream\";\nimport { canUseAsyncIteratorSymbol } from \"../../utilities/index.js\";\n\nimport asyncIterator from \"./iterators/async.js\";\nimport nodeStreamIterator from \"./iterators/nodeStream.js\";\nimport promiseIterator from \"./iterators/promise.js\";\nimport readerIterator from \"./iterators/reader.js\";\n\nfunction isNodeResponse(value: any): value is NodeResponse {\n  return !!(value as NodeResponse).body;\n}\n\nfunction isReadableStream(value: any): value is ReadableStream<any> {\n  return !!(value as ReadableStream<any>).getReader;\n}\n\nfunction isAsyncIterableIterator(\n  value: any\n): value is AsyncIterableIterator<any> {\n  return !!(\n    canUseAsyncIteratorSymbol &&\n    (value as AsyncIterableIterator<any>)[Symbol.asyncIterator]\n  );\n}\n\nfunction isStreamableBlob(value: any): value is Blob {\n  return !!(value as Blob).stream;\n}\n\nfunction isBlob(value: any): value is Blob {\n  return !!(value as Blob).arrayBuffer;\n}\n\nfunction isNodeReadableStream(value: any): value is NodeReadableStream {\n  return !!(value as NodeReadableStream).pipe;\n}\n\nexport function responseIterator<T>(\n  response: Response | NodeResponse\n): AsyncIterableIterator<T> {\n  let body: unknown = response;\n\n  if (isNodeResponse(response)) body = response.body;\n\n  if (isAsyncIterableIterator(body)) return asyncIterator<T>(body);\n\n  if (isReadableStream(body)) return readerIterator<T>(body.getReader());\n\n  // this errors without casting to ReadableStream<T>\n  // because Blob.stream() returns a NodeJS ReadableStream\n  if (isStreamableBlob(body)) {\n    return readerIterator<T>(\n      (body.stream() as unknown as ReadableStream<T>).getReader()\n    );\n  }\n\n  if (isBlob(body)) return promiseIterator<T>(body.arrayBuffer());\n\n  if (isNodeReadableStream(body)) return nodeStreamIterator<T>(body);\n\n  throw new Error(\n    \"Unknown body type for responseIterator. Please pass a streamable response.\"\n  );\n}\n"]}