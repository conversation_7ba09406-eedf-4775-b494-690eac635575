{"version": 3, "file": "validateOperation.js", "sourceRoot": "", "sources": ["../../../src/link/utils/validateOperation.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AAGrE,MAAM,UAAU,iBAAiB,CAAC,SAAyB;IACzD,IAAM,gBAAgB,GAAG;QACvB,OAAO;QACP,eAAe;QACf,WAAW;QACX,YAAY;QACZ,SAAS;KACV,CAAC;IACF,KAAgB,UAAsB,EAAtB,KAAA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAtB,cAAsB,EAAtB,IAAsB,EAAE,CAAC;QAApC,IAAI,GAAG,SAAA;QACV,IAAI,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACtC,MAAM,iBAAiB,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC", "sourcesContent": ["import { newInvariantError } from \"../../utilities/globals/index.js\";\nimport type { GraphQLRequest } from \"../core/index.js\";\n\nexport function validateOperation(operation: GraphQLRequest): GraphQLRequest {\n  const OPERATION_FIELDS = [\n    \"query\",\n    \"operationName\",\n    \"variables\",\n    \"extensions\",\n    \"context\",\n  ];\n  for (let key of Object.keys(operation)) {\n    if (OPERATION_FIELDS.indexOf(key) < 0) {\n      throw newInvariantError(`illegal argument: %s`, key);\n    }\n  }\n\n  return operation;\n}\n"]}