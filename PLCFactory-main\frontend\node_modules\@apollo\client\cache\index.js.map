{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/cache/index.ts"], "names": [], "mappings": "AAAA,OAAO,+BAA+B,CAAC;AAOvC,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAC9C,OAAO,EAAE,KAAK,EAAE,MAAM,uBAAuB,CAAC;AAS9C,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAG3D,OAAO,EACL,WAAW,EACX,aAAa,EACb,kBAAkB,GACnB,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EACL,sBAAsB,EACtB,uBAAuB,GACxB,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAG5D,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AAWhE,OAAO,EAAE,QAAQ,EAAE,MAAM,wBAAwB,CAAC;AAGlD,OAAO,EAAE,sBAAsB,EAAE,MAAM,gCAAgC,CAAC", "sourcesContent": ["import \"../utilities/globals/index.js\";\n\nexport type {\n  Transaction,\n  WatchFragmentOptions,\n  WatchFragmentResult,\n} from \"./core/cache.js\";\nexport { ApolloCache } from \"./core/cache.js\";\nexport { Cache } from \"./core/types/Cache.js\";\nexport type { DataProxy } from \"./core/types/DataProxy.js\";\nexport type {\n  MissingTree,\n  Modifier,\n  Modifiers,\n  ModifierDetails,\n  ReadFieldOptions,\n} from \"./core/types/common.js\";\nexport { MissingFieldError } from \"./core/types/common.js\";\n\nexport type { Reference } from \"../utilities/index.js\";\nexport {\n  isReference,\n  makeReference,\n  canonicalStringify,\n} from \"../utilities/index.js\";\n\nexport { EntityStore } from \"./inmemory/entityStore.js\";\nexport {\n  fieldNameFromStoreName,\n  defaultDataIdFromObject,\n} from \"./inmemory/helpers.js\";\n\nexport { InMemoryCache } from \"./inmemory/inMemoryCache.js\";\n\nexport type { ReactiveVar } from \"./inmemory/reactiveVars.js\";\nexport { makeVar, cacheSlot } from \"./inmemory/reactiveVars.js\";\n\nexport type {\n  TypePolicies,\n  TypePolicy,\n  FieldPolicy,\n  FieldReadFunction,\n  FieldMergeFunction,\n  FieldFunctionOptions,\n  PossibleTypesMap,\n} from \"./inmemory/policies.js\";\nexport { Policies } from \"./inmemory/policies.js\";\n\nexport type { FragmentRegistryAPI } from \"./inmemory/fragmentRegistry.js\";\nexport { createFragmentRegistry } from \"./inmemory/fragmentRegistry.js\";\n\nexport type * from \"./inmemory/types.js\";\n"]}