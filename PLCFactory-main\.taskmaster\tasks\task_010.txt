# Task ID: 10
# Title: Implement API Gateway and Integration Layer
# Status: pending
# Dependencies: 1, 2, 3, 4
# Priority: medium
# Description: Develop the GraphQL API gateway, webhooks system, OPC-UA gateway, and plugin SDK for external integrations.
# Details:
1. Implement GraphQL API gateway with schema stitching across microservices
2. Create REST endpoints for backward compatibility
3. Develop webhook system for `build.completed` and `anomaly.detected` events
4. Implement OPC-UA gateway with auto-discovery of endpoints
5. Create plugin SDK with JS/TS support and sandbox iframe
6. Implement capability-based security model for plugins
7. Create API documentation and developer portal

GraphQL gateway implementation:
```typescript
import { ApolloServer } from 'apollo-server-express';
import { ApolloGateway, RemoteGraphQLDataSource } from '@apollo/gateway';
import express from 'express';
import { authMiddleware } from './auth';

class AuthenticatedDataSource extends RemoteGraphQLDataSource {
  willSendRequest({ request, context }) {
    // Pass user context to downstream services
    if (context.user) {
      request.http.headers.set('x-user-id', context.user.id);
      request.http.headers.set('x-user-roles', JSON.stringify(context.user.roles));
    }
  }
}

async function startGateway() {
  const gateway = new ApolloGateway({
    serviceList: [
      { name: 'projects', url: process.env.PROJECTS_SERVICE_URL },
      { name: 'editor', url: process.env.EDITOR_SERVICE_URL },
      { name: 'transpiler', url: process.env.TRANSPILER_SERVICE_URL },
      { name: 'hmi', url: process.env.HMI_SERVICE_URL },
      { name: 'ghost', url: process.env.GHOST_SERVICE_URL },
    ],
    buildService({ name, url }) {
      return new AuthenticatedDataSource({ url });
    },
  });

  const server = new ApolloServer({
    gateway,
    subscriptions: false,
    context: authMiddleware
  });

  const app = express();
  
  // REST endpoints for backward compatibility
  app.get('/v1/projects/:id/build', async (req, res) => {
    const { id } = req.params;
    const { target } = req.query;
    
    try {
      // Validate auth
      const user = await authMiddleware(req);
      if (!user) return res.status(401).json({ error: 'Unauthorized' });
      
      // Call transpiler service
      const result = await transpilerClient.buildProject(id, target, user);
      
      // Return presigned URL
      return res.json({
        url: result.downloadUrl,
        expiresAt: result.expiresAt
      });
    } catch (error) {
      return res.status(500).json({ error: error.message });
    }
  });
  
  // Webhook registration endpoint
  app.post('/v1/webhooks', async (req, res) => {
    // Implementation details omitted for brevity
  });
  
  // Plugin SDK endpoints
  app.get('/v1/plugins/manifest', (req, res) => {
    res.json({
      name: 'Continuum Plugin SDK',
      version: '1.0.0',
      capabilities: [
        'read:projects',
        'write:projects',
        'read:assets',
        'execute:simulation'
      ]
    });
  });
  
  await server.start();
  server.applyMiddleware({ app });
  
  app.listen({ port: 4000 }, () =>
    console.log(`Gateway ready at http://localhost:4000${server.graphqlPath}`)
  );
}

startGateway().catch(console.error);
```

# Test Strategy:
1. Unit tests for GraphQL resolvers and REST endpoints
2. Integration tests for service communication
3. Webhook delivery and retry testing
4. OPC-UA gateway discovery and connection testing
5. Plugin sandbox security testing
6. API performance testing under load
7. Documentation completeness verification

# Subtasks:
## 1. Set up GraphQL schema and resolvers [pending]
### Dependencies: None
### Description: Design and implement the GraphQL schema and resolvers for the API gateway
### Details:
Define types, queries, and mutations. Implement resolvers to handle data fetching and manipulation. Ensure proper error handling and performance optimization.

## 2. Create REST endpoints [pending]
### Dependencies: 10.1
### Description: Develop REST endpoints for services that require traditional HTTP methods
### Details:
Implement GET, POST, PUT, DELETE endpoints as needed. Ensure proper request validation, authentication, and error handling.

## 3. Implement webhook system [pending]
### Dependencies: 10.2
### Description: Design and implement a webhook system for event-driven communication
### Details:
Create webhook registration, management, and delivery mechanisms. Implement retry logic and ensure proper security measures for webhook endpoints.

## 4. Develop OPC-UA gateway [pending]
### Dependencies: 10.2
### Description: Implement an OPC-UA gateway for industrial communication protocols
### Details:
Set up OPC-UA client and server capabilities. Implement data mapping between OPC-UA and API gateway. Ensure proper security and authentication mechanisms.

## 5. Create plugin SDK [pending]
### Dependencies: 10.1, 10.2, 10.3, 10.4
### Description: Develop a software development kit for creating plugins to extend API gateway functionality
### Details:
Design plugin architecture and interfaces. Implement SDK with necessary tools and libraries. Create documentation and examples for plugin development.

## 6. Develop comprehensive documentation [pending]
### Dependencies: 10.1, 10.2, 10.3, 10.4, 10.5
### Description: Create detailed documentation for the API gateway, including usage guides and API references
### Details:
Document GraphQL schema, REST endpoints, webhook usage, OPC-UA integration, and plugin development. Create tutorials and examples for each component of the API gateway.

