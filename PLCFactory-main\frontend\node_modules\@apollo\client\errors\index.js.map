{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/errors/index.ts"], "names": [], "mappings": ";AAAA,OAAO,+BAA+B,CAAC;AAQvC,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AAKxD,8EAA8E;AAC9E,+EAA+E;AAC/E,2DAA2D;AAC3D,MAAM,CAAC,IAAM,sBAAsB,GAAkB,MAAM,EAAE,CAAC;AAe9D,MAAM,UAAU,8BAA8B,CAC5C,MAAsB;IAEtB,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;QACtB,OAAO,KAAK,CAAC,OAAO,CACjB,MAA6C,CAAC,UAAU,CACvD,sBAAsB,CACvB,CACF,CAAC;IACJ,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,GAAU;IACtC,OAAO,GAAG,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;AAC7C,CAAC;AAED,wDAAwD;AACxD,mDAAmD;AACnD,wDAAwD;AACxD,oDAAoD;AACpD,IAAM,oBAAoB,GAAG,UAAC,GAAgB;IAC5C,IAAM,MAAM,iDACP,GAAG,CAAC,aAAa,SACjB,GAAG,CAAC,YAAY,SAChB,GAAG,CAAC,cAAc,OACtB,CAAC;IACF,IAAI,GAAG,CAAC,YAAY;QAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACpD,OAAO,CACL,MAAM;QACJ,mFAAmF;SAClF,GAAG,CACF,UAAC,GAAG;QACF,OAAA,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,0BAA0B;IAAnE,CAAmE,CACtE;SACA,IAAI,CAAC,IAAI,CAAC,CACd,CAAC;AACJ,CAAC,CAAC;AAaF;IAAiC,+BAAK;IA0BpC,yEAAyE;IACzE,oDAAoD;IACpD,2CAA2C;IAC3C,sDAAsD;IACtD,qBAAY,EAOS;YANnB,aAAa,mBAAA,EACb,cAAc,oBAAA,EACd,YAAY,kBAAA,EACZ,YAAY,kBAAA,EACZ,YAAY,kBAAA,EACZ,SAAS,eAAA;QAET,YAAA,MAAK,YAAC,YAAY,CAAC,SAAC;QACpB,KAAI,CAAC,IAAI,GAAG,aAAa,CAAC;QAC1B,KAAI,CAAC,aAAa,GAAG,aAAa,IAAI,EAAE,CAAC;QACzC,KAAI,CAAC,cAAc,GAAG,cAAc,IAAI,EAAE,CAAC;QAC3C,KAAI,CAAC,YAAY,GAAG,YAAY,IAAI,EAAE,CAAC;QACvC,KAAI,CAAC,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC;QACzC,KAAI,CAAC,OAAO,GAAG,YAAY,IAAI,oBAAoB,CAAC,KAAI,CAAC,CAAC;QAC1D,KAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,KAAI,CAAC,KAAK;YACR;gBACE,YAAY;eACT,CAAC,aAAa,IAAI,EAAE,CAAC,SACrB,CAAC,cAAc,IAAI,EAAE,CAAC,SACtB,CAAC,YAAY,IAAI,EAAE,CAAC,QACvB,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,EAAH,CAAG,CAAC,IAAI,IAAI,CAAC;QAE7B,iEAAiE;QACjE,0CAA0C;QACzC,KAAY,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;;IAClD,CAAC;IACH,kBAAC;AAAD,CAAC,AA1DD,CAAiC,KAAK,GA0DrC", "sourcesContent": ["import \"../utilities/globals/index.js\";\n\nimport type {\n  GraphQLError,\n  GraphQLErrorExtensions,\n  GraphQLFormattedError,\n} from \"graphql\";\n\nimport { isNonNullObject } from \"../utilities/index.js\";\nimport type { ServerParseError } from \"../link/http/index.js\";\nimport type { ServerError } from \"../link/utils/index.js\";\nimport type { FetchResult } from \"../link/core/index.js\";\n\n// This Symbol allows us to pass transport-specific errors from the link chain\n// into QueryManager/client internals without risking a naming collision within\n// extensions (which implementers can use as they see fit).\nexport const PROTOCOL_ERRORS_SYMBOL: unique symbol = Symbol();\n\ntype FetchResultWithSymbolExtensions<T> = FetchResult<T> & {\n  extensions: Record<string | symbol, any>;\n};\n\nexport interface ApolloErrorOptions {\n  graphQLErrors?: ReadonlyArray<GraphQLFormattedError>;\n  protocolErrors?: ReadonlyArray<GraphQLFormattedError>;\n  clientErrors?: ReadonlyArray<Error>;\n  networkError?: Error | ServerParseError | ServerError | null;\n  errorMessage?: string;\n  extraInfo?: any;\n}\n\nexport function graphQLResultHasProtocolErrors<T>(\n  result: FetchResult<T>\n): result is FetchResultWithSymbolExtensions<T> {\n  if (result.extensions) {\n    return Array.isArray(\n      (result as FetchResultWithSymbolExtensions<T>).extensions[\n        PROTOCOL_ERRORS_SYMBOL\n      ]\n    );\n  }\n  return false;\n}\n\nexport function isApolloError(err: Error): err is ApolloError {\n  return err.hasOwnProperty(\"graphQLErrors\");\n}\n\n// Sets the error message on this error according to the\n// the GraphQL and network errors that are present.\n// If the error message has already been set through the\n// constructor or otherwise, this function is a nop.\nconst generateErrorMessage = (err: ApolloError) => {\n  const errors = [\n    ...err.graphQLErrors,\n    ...err.clientErrors,\n    ...err.protocolErrors,\n  ];\n  if (err.networkError) errors.push(err.networkError);\n  return (\n    errors\n      // The rest of the code sometimes unsafely types non-Error objects as GraphQLErrors\n      .map(\n        (err) =>\n          (isNonNullObject(err) && err.message) || \"Error message not found.\"\n      )\n      .join(\"\\n\")\n  );\n};\n\n/**\n * @deprecated This type is deprecated and will be removed in the next major version of Apollo Client.\n * It mistakenly referenced `GraqhQLError` instead of `GraphQLFormattedError`.\n *\n * Use `ReadonlyArray<GraphQLFormattedError>` instead.\n */\n// eslint-disable-next-line @typescript-eslint/no-restricted-types\nexport type GraphQLErrors = ReadonlyArray<GraphQLError>;\n\nexport type NetworkError = Error | ServerParseError | ServerError | null;\n\nexport class ApolloError extends Error {\n  public name: string;\n  public message: string;\n  public graphQLErrors: ReadonlyArray<GraphQLFormattedError>;\n  public protocolErrors: ReadonlyArray<GraphQLFormattedError>;\n  public clientErrors: ReadonlyArray<Error>;\n  public networkError: Error | ServerParseError | ServerError | null;\n  /**\n   * Indicates the specific original cause of the error.\n   *\n   * This field contains the first available `networkError`, `graphQLError`, `protocolError`, `clientError`, or `null` if none are available.\n   */\n  public cause:\n    | ({\n        readonly message: string;\n        extensions?:\n          | GraphQLErrorExtensions[]\n          | GraphQLFormattedError[\"extensions\"];\n      } & Omit<Partial<Error> & Partial<GraphQLFormattedError>, \"extensions\">)\n    | null;\n\n  // An object that can be used to provide some additional information\n  // about an error, e.g. specifying the type of error this is. Used\n  // internally within Apollo Client.\n  public extraInfo: any;\n\n  // Constructs an instance of ApolloError given serialized GraphQL errors,\n  // client errors, protocol errors or network errors.\n  // Note that one of these has to be a valid\n  // value or the constructed error will be meaningless.\n  constructor({\n    graphQLErrors,\n    protocolErrors,\n    clientErrors,\n    networkError,\n    errorMessage,\n    extraInfo,\n  }: ApolloErrorOptions) {\n    super(errorMessage);\n    this.name = \"ApolloError\";\n    this.graphQLErrors = graphQLErrors || [];\n    this.protocolErrors = protocolErrors || [];\n    this.clientErrors = clientErrors || [];\n    this.networkError = networkError || null;\n    this.message = errorMessage || generateErrorMessage(this);\n    this.extraInfo = extraInfo;\n    this.cause =\n      [\n        networkError,\n        ...(graphQLErrors || []),\n        ...(protocolErrors || []),\n        ...(clientErrors || []),\n      ].find((e) => !!e) || null;\n\n    // We're not using `Object.setPrototypeOf` here as it isn't fully\n    // supported on Android (see issue #3236).\n    (this as any).__proto__ = ApolloError.prototype;\n  }\n}\n"]}