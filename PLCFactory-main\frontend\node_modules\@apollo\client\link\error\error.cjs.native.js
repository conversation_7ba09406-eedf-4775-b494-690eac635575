'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib = require('tslib');
var errors = require('../../errors');
var utilities = require('../../utilities');
var core = require('../core');

function onError(errorHandler) {
    return new core.ApolloLink(function (operation, forward) {
        return new utilities.Observable(function (observer) {
            var sub;
            var retriedSub;
            var retriedResult;
            try {
                sub = forward(operation).subscribe({
                    next: function (result) {
                        if (result.errors) {
                            retriedResult = errorHandler({
                                graphQLErrors: result.errors,
                                response: result,
                                operation: operation,
                                forward: forward,
                            });
                        }
                        else if (errors.graphQLResultHasProtocolErrors(result)) {
                            retriedResult = errorHandler({
                                protocolErrors: result.extensions[errors.PROTOCOL_ERRORS_SYMBOL],
                                response: result,
                                operation: operation,
                                forward: forward,
                            });
                        }
                        if (retriedResult) {
                            retriedSub = retriedResult.subscribe({
                                next: observer.next.bind(observer),
                                error: observer.error.bind(observer),
                                complete: observer.complete.bind(observer),
                            });
                            return;
                        }
                        observer.next(result);
                    },
                    error: function (networkError) {
                        retriedResult = errorHandler({
                            operation: operation,
                            networkError: networkError,
                            graphQLErrors: (networkError &&
                                networkError.result &&
                                networkError.result.errors) ||
                                void 0,
                            forward: forward,
                        });
                        if (retriedResult) {
                            retriedSub = retriedResult.subscribe({
                                next: observer.next.bind(observer),
                                error: observer.error.bind(observer),
                                complete: observer.complete.bind(observer),
                            });
                            return;
                        }
                        observer.error(networkError);
                    },
                    complete: function () {
                        if (!retriedResult) {
                            observer.complete.bind(observer)();
                        }
                    },
                });
            }
            catch (e) {
                errorHandler({ networkError: e, operation: operation, forward: forward });
                observer.error(e);
            }
            return function () {
                if (sub)
                    sub.unsubscribe();
                if (retriedSub)
                    sub.unsubscribe();
            };
        });
    });
}
var ErrorLink =  (function (_super) {
    tslib.__extends(ErrorLink, _super);
    function ErrorLink(errorHandler) {
        var _this = _super.call(this) || this;
        _this.link = onError(errorHandler);
        return _this;
    }
    ErrorLink.prototype.request = function (operation, forward) {
        return this.link.request(operation, forward);
    };
    return ErrorLink;
}(core.ApolloLink));

exports.ErrorLink = ErrorLink;
exports.onError = onError;
//# sourceMappingURL=error.cjs.map
