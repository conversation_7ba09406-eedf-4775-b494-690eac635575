{"version": 3, "file": "errors.cjs", "sources": ["index.js"], "sourcesContent": ["import { __extends, __spreadArray } from \"tslib\";\nimport \"../utilities/globals/index.js\";\nimport { isNonNullObject } from \"../utilities/index.js\";\n// This Symbol allows us to pass transport-specific errors from the link chain\n// into QueryManager/client internals without risking a naming collision within\n// extensions (which implementers can use as they see fit).\nexport var PROTOCOL_ERRORS_SYMBOL = Symbol();\nexport function graphQLResultHasProtocolErrors(result) {\n    if (result.extensions) {\n        return Array.isArray(result.extensions[PROTOCOL_ERRORS_SYMBOL]);\n    }\n    return false;\n}\nexport function isApolloError(err) {\n    return err.hasOwnProperty(\"graphQLErrors\");\n}\n// Sets the error message on this error according to the\n// the GraphQL and network errors that are present.\n// If the error message has already been set through the\n// constructor or otherwise, this function is a nop.\nvar generateErrorMessage = function (err) {\n    var errors = __spreadArray(__spreadArray(__spreadArray([], err.graphQLErrors, true), err.clientErrors, true), err.protocolErrors, true);\n    if (err.networkError)\n        errors.push(err.networkError);\n    return (errors\n        // The rest of the code sometimes unsafely types non-Error objects as GraphQLErrors\n        .map(function (err) {\n        return (isNonNullObject(err) && err.message) || \"Error message not found.\";\n    })\n        .join(\"\\n\"));\n};\nvar ApolloError = /** @class */ (function (_super) {\n    __extends(ApolloError, _super);\n    // Constructs an instance of ApolloError given serialized GraphQL errors,\n    // client errors, protocol errors or network errors.\n    // Note that one of these has to be a valid\n    // value or the constructed error will be meaningless.\n    function ApolloError(_a) {\n        var graphQLErrors = _a.graphQLErrors, protocolErrors = _a.protocolErrors, clientErrors = _a.clientErrors, networkError = _a.networkError, errorMessage = _a.errorMessage, extraInfo = _a.extraInfo;\n        var _this = _super.call(this, errorMessage) || this;\n        _this.name = \"ApolloError\";\n        _this.graphQLErrors = graphQLErrors || [];\n        _this.protocolErrors = protocolErrors || [];\n        _this.clientErrors = clientErrors || [];\n        _this.networkError = networkError || null;\n        _this.message = errorMessage || generateErrorMessage(_this);\n        _this.extraInfo = extraInfo;\n        _this.cause =\n            __spreadArray(__spreadArray(__spreadArray([\n                networkError\n            ], (graphQLErrors || []), true), (protocolErrors || []), true), (clientErrors || []), true).find(function (e) { return !!e; }) || null;\n        // We're not using `Object.setPrototypeOf` here as it isn't fully\n        // supported on Android (see issue #3236).\n        _this.__proto__ = ApolloError.prototype;\n        return _this;\n    }\n    return ApolloError;\n}(Error));\nexport { ApolloError };\n//# sourceMappingURL=index.js.map"], "names": ["__spread<PERSON><PERSON>y", "isNonNullObject", "__extends"], "mappings": ";;;;;;;;AAMU,IAAC,sBAAsB,GAAG,MAAM,GAAG;AACtC,SAAS,8BAA8B,CAAC,MAAM,EAAE;AACvD,IAAI,IAAI,MAAM,CAAC,UAAU,EAAE;AAC3B,QAAQ,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC;AACxE,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACM,SAAS,aAAa,CAAC,GAAG,EAAE;AACnC,IAAI,OAAO,GAAG,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;AAC/C,CAAC;AAKD,IAAI,oBAAoB,GAAG,UAAU,GAAG,EAAE;AAC1C,IAAI,IAAI,MAAM,GAAGA,mBAAa,CAACA,mBAAa,CAACA,mBAAa,CAAC,EAAE,EAAE,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AAC5I,IAAI,IAAI,GAAG,CAAC,YAAY;AACxB,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AACtC,IAAI,QAAQ,MAAM;AAElB,SAAS,GAAG,CAAC,UAAU,GAAG,EAAE;AAC5B,QAAQ,OAAO,CAACC,yBAAe,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,OAAO,KAAK,0BAA0B,CAAC;AACnF,KAAK,CAAC;AACN,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE;AACrB,CAAC,CAAC;AACC,IAAC,WAAW,KAAkB,UAAU,MAAM,EAAE;AACnD,IAAIC,eAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AAKnC,IAAI,SAAS,WAAW,CAAC,EAAE,EAAE;AAC7B,QAAQ,IAAI,aAAa,GAAG,EAAE,CAAC,aAAa,EAAE,cAAc,GAAG,EAAE,CAAC,cAAc,EAAE,YAAY,GAAG,EAAE,CAAC,YAAY,EAAE,YAAY,GAAG,EAAE,CAAC,YAAY,EAAE,YAAY,GAAG,EAAE,CAAC,YAAY,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC;AAC3M,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC;AAC5D,QAAQ,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC;AACnC,QAAQ,KAAK,CAAC,aAAa,GAAG,aAAa,IAAI,EAAE,CAAC;AAClD,QAAQ,KAAK,CAAC,cAAc,GAAG,cAAc,IAAI,EAAE,CAAC;AACpD,QAAQ,KAAK,CAAC,YAAY,GAAG,YAAY,IAAI,EAAE,CAAC;AAChD,QAAQ,KAAK,CAAC,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC;AAClD,QAAQ,KAAK,CAAC,OAAO,GAAG,YAAY,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAC;AACpE,QAAQ,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;AACpC,QAAQ,KAAK,CAAC,KAAK;AACnB,YAAYF,mBAAa,CAACA,mBAAa,CAACA,mBAAa,CAAC;AACtD,gBAAgB,YAAY;AAC5B,aAAa,GAAG,aAAa,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,cAAc,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,YAAY,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC;AAGnJ,QAAQ,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;AAChD,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC,CAAC,KAAK,CAAC;;;;;;;"}